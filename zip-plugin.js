#!/usr/bin/env node

const fs = require('fs-extra');
const slugify = require('slugify');
const archiver = require('archiver');
const { parseArgs } = require('util'); // For parsing command-line arguments
const path = require('path');

// Used as the name of the final zipped package
const pluginSlug = 'uncanny-automator';
let releaseVersion; // Will be set from command-line argument

// --- Argument Parsing ---
try {
    const { values } = parseArgs({
        options: {
            version: {
                type: 'string',
                short: 'v',
            },
            help: {
                type: 'boolean',
                short: 'h',
            }
        },
        allowPositionals: false, // We expect the version via a named flag
    });

    if (values.help) {
        console.log(`
Zip Plugin Script (Original Structure - Modified)

Usage:
  node ${path.basename(__filename)} -v <version>
  node ${path.basename(__filename)} --version <version>

Arguments:
  -v, --version <version>   The version number for the plugin zip (e.g., 1.2.3).

Options:
  -h, --help                Show this help message.
        `);
        process.exit(0);
    }

    if (!values.version || values.version.trim() === '') {
        throw new Error("Version argument (-v or --version) is missing or empty.");
    }
    // Slugify might not be strictly necessary if versions are clean like "1.0.0"
    // but keeping it in case your versions might have characters slugify would clean.
    releaseVersion = slugify(values.version.trim());
    console.log(`📦 Preparing to zip plugin version: ${releaseVersion}`);
    // Start the process
    create_dir();

} catch (err) {
    console.error(`❌ Error parsing arguments for zip-plugin.js: ${err.message}`);
    console.error(`   Usage: node ${path.basename(__filename)} --version <version_number>`);
    process.exit(1);
}

/**
 * Step one of the zip-plugin process
 * Creates the zip-package and temp directories
 * Starts adding files to the temp directory
 */
function create_dir() {
    console.log('   Ensuring directories...');
    try {
        fs.ensureDirSync('./zip-package');
        console.log('   [✓] zip-package directory ensured.');

        const tempPluginDir = `./temp/${pluginSlug}`;
        fs.emptyDirSync('./temp'); // Clean out temp before starting
        fs.ensureDirSync(tempPluginDir);
        console.log('   [✓] temp plugin directory ensured and cleaned.');

        add_files(); // Call next step
    } catch (dirErr) {
        console.error('❌ Error ensuring directories:', dirErr);
        process.exit(1);
    }
}

/**
 * Step two of the zip-plugin process
 * Adds all needed plugin files to the temp directory
 * Starts the zipping process
 */
async function add_files() {
    console.log('   Copying plugin files...');
    const tempPluginPath = `./temp/${pluginSlug}`;

    const filesToCopy = [
        { src: `./${pluginSlug}.php`, dest: `${tempPluginPath}/${pluginSlug}.php`, name: 'main plugin file' },
        { src: './readme.txt', dest: `${tempPluginPath}/readme.txt`, name: 'readme.txt' },
        { src: './uninstall.php', dest: `${tempPluginPath}/uninstall.php`, name: 'uninstall.php' },
        { src: './license.txt', dest: `${tempPluginPath}/license.txt`, name: 'license.txt' },
        { src: './languages/', dest: `${tempPluginPath}/languages`, name: 'languages directory' },
        { src: './vendor/', dest: `${tempPluginPath}/vendor`, name: 'vendor directory' },
        { src: './src/core/', dest: `${tempPluginPath}/src/core`, name: 'core directory' },
        { src: './src/integrations/', dest: `${tempPluginPath}/src/integrations`, name: 'integrations directory' },
        { src: './src/class-automator-load.php', dest: `${tempPluginPath}/src/class-automator-load.php`, name: 'class-automator-load.php' },
        { src: './src/class-db-tables.php', dest: `${tempPluginPath}/src/class-db-tables.php`, name: 'class-db-tables.php' },
        { src: './src/global-functions.php', dest: `${tempPluginPath}/src/global-functions.php`, name: 'global-functions.php' },
        { src: './src/globals.php', dest: `${tempPluginPath}/src/globals.php`, name: 'globals.php' },
        { src: './src/legacy.php', dest: `${tempPluginPath}/src/legacy.php`, name: 'legacy.php' },
        { src: './logs/.htaccess', dest: `${tempPluginPath}/logs/.htaccess`, name: 'logs/.htaccess' },
        { src: './logs/index.php', dest: `${tempPluginPath}/logs/index.php`, name: 'logs/index.php' },
        { src: './logs/robots.txt', dest: `${tempPluginPath}/logs/robots.txt`, name: 'logs/robots.txt' },
    ];

    const staticAssets = {
        assets: './src/assets/build/',
        legacy: './src/assets/_legacy/',
    };

    try {
        for (const file of filesToCopy) {
            if (fs.existsSync(file.src)) {
                await fs.copy(file.src, file.dest);
                // console.log(`   [✓] Copied: ${file.name}`); // Kept verbose logging commented out
            } else {
                console.warn(`   [!] Source not found, skipping copy: ${file.src} (for ${file.name})`);
            }
        }

        // Iterate and copy static assets
        for (const [assetID, assetPath] of Object.entries(staticAssets)) {
            const fullAssetDestPath = path.join(tempPluginPath, assetPath); // Ensure correct dest path
            if (fs.existsSync(assetPath)) {
                await fs.copy(assetPath, fullAssetDestPath);
                // console.log(`   [✓] ${assetID}: Copied ${assetPath} to ${fullAssetDestPath}`);
            } else {
                console.warn(`   [!] Static asset source not found, skipping copy: ${assetPath} (for ${assetID})`);
            }
        }

        console.log('   [✓] Plugin files and assets copied to temp directory (if they existed).');
        zip_folder(); // Call next step
    } catch (err) {
        console.error('❌ Error copying files for zipping:', err);
        process.exit(1);
    }
}

/**
 * Step three of the zip-plugin process
 * Zips the temp directory and names the zip file with the version number as the suffix
 */
function zip_folder() {
    const zipFileName = `${pluginSlug}.${releaseVersion}.zip`;
    const outputFilePath = `./zip-package/${zipFileName}`;
    const outputStream = fs.createWriteStream(outputFilePath);
    const archive = archiver('zip', {
        zlib: { level: 9 } // Sets the compression level
    });

    console.log(`   Zipping folder to ${outputFilePath}...`);

    outputStream.on('close', () => {
        console.log(`   [✓] Created: ${zipFileName} (${(archive.pointer() / 1024 / 1024).toFixed(2)} MB)`);
        fs.remove('./temp/', err => {
            if (err) {
                console.error('   Warning: Error removing temp directory:', err);
                // Even if temp removal fails, the zip was created.
                process.exit(0); // Exit successfully as zip is created
            } else {
                console.log('   [✓] Removed temp directory.');
                process.exit(0); // Explicitly exit after successful cleanup
            }
        });
    });

    archive.on('warning', function(err) {
        if (err.code === 'ENOENT') {
            // Log it and continue - might be an optional file that wasn't copied
            console.warn('   Archiver warning (some source files might be missing during zipping):', err.message);
        } else {
            // For other warnings, decide if they are critical
            console.warn('   Archiver warning:', err);
        }
    });

    archive.on('error', err => {
        console.error('❌ Error creating zip:', err);
        process.exit(1); // Exit with error code if archiver fails
    });

    archive.pipe(outputStream);
    archive.directory(`./temp/${pluginSlug}/`, pluginSlug);
    archive.finalize();
}
