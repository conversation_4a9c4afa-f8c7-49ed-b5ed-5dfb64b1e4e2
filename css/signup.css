.mepr-hidden {
  display: none;
}

.mp_wrapper .mepr-form-has-errors,
.mp_wrapper .cc-error,
.mp_wrapper .mepr-validation-error {
  display: none;
  color: #eb1c26;
  padding-left: 5px;
}

.mp_wrapper .cc-success {
  display: none;
  color: #14c146;
  padding-left: 5px;
}

.mp_wrapper .mepr-validation-error {
  display: inline;
}

.mp_wrapper .mepr-cards,
.mp_wrapper .visa,
.mp_wrapper .amex,
.mp_wrapper .mastercard,
.mp_wrapper .discover {
  background-repeat: no-repeat !important;
  background-position-x: 98% !important;
  background-position-y: 50% !important;
}

.mp_wrapper .visa {
  background: url('../images/checkout/visa.png') right white no-repeat !important;
}

.mp_wrapper .mastercard {
  background: url('../images/checkout/mastercard.png') right white no-repeat !important;
}

.mp_wrapper .discover {
  background: url('../images/checkout/discover.png') right white no-repeat !important;
}

.mp_wrapper .amex {
  background: url('../images/checkout/amex.png') right white no-repeat !important;
}

.mp_wrapper .dinersclub {
  /* TODO: Support this logo at some point */
}

.mp_wrapper .jcb {
  /* TODO: Support this logo at some point */
}

.mp_wrapper .mepr-cards {
  background: url('../images/checkout/cards.png') right white no-repeat !important;
}

/* A bit of a hack to get the invalid color looking more accurate */
.mp_wrapper .mepr-cards.invalid {
  opacity: 1;
}

.mp_wrapper .mepr-payment-method {
  display: block;
  width: 100%;
  margin-bottom: 10px;
}

.mp_wrapper .mepr-payment-method input {
  display: inline-block;
  text-align: left;
  line-height: 32px;
  vertical-align: middle;
}

.mp_wrapper .mepr-payment-method .spc  input {
  line-height: normal;
}

.mp_wrapper .mepr-payment-method .mepr-payment-method-label-text,
.mp_wrapper .mepr-payment-method span.mepr-payment-method-icon {
  padding-left: 15px;
  display: inline-block;
  width: auto;
  /* line-height: 32px; */
  vertical-align: middle !important;
}

.mp_wrapper .mepr-payment-method-icon img {
  height: 32px !important;
}

.mp_wrapper .mepr-payment-method .mepr-payment-method-desc-text.spc {
  padding: 0;
  background-color: inherit;
}

.mp_wrapper .mepr-payment-method .mepr-payment-method-desc-text {
  margin-top: 5px;
  padding: 10px;
  background-color: white;
}

.mp_wrapper .mepr-payment-method .mepr-payment-method-label-text,
.mp_wrapper .mepr-payment-method input {
  /* margin-top: -10px; */
}

.mp_wrapper .mepr-authorizenet-cc-form .mepr-cc-second-row {
  display: flex;
  justify-content: space-between;
}

.mp_wrapper .mepr-authorizenet-cc-form .mepr-cc-second-row > div {
  width: 49%;
}

.mp_wrapper .mepr-form-checkbox, .mepr-form-radio {
  /* width: 20px !important; */
}

.mp_wrapper .mepr_custom_field .mepr-form-checkbox {
  padding-left: 0 !important;
  margin-left: 2px !important;
  margin-bottom: 10px !important;
}

.mp_wrapper label.mepr-form-checkbox-label {
  width: auto !important;
  display: inline !important;
}

.mp_wrapper .mepr_payment_method {
  margin: 25px 0 !important;
  padding: 15px !important;
  background-color: #f5f5f5 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  /* max-width: 635px; */
}

.mp_wrapper .mepr-payment-methods-radios label {
  width: inherit !important;
  font-weight: 600 !important;
  margin-right: 15px;
}

.mp_wrapper .mepr-payment-methods-radios input[type="radio"] {
  margin: 0 !important;
}

.mp_wrapper .mepr_no_val,
#mepr_no_val {
  display:none !important;
  margin: 0;
  padding: 0;
}

.mp-form-submit {
  margin: 0;
  padding: 0;
}

.mepr-switch-pm-popup {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  position: relative;
  background: #FFF;
  padding: 35px 65px;
  width: auto;
  max-width: 560px;
  margin: 20px auto;
  text-align: center;
  border-radius: 16px;
}

.mepr-switch-pm-mfp.mfp-close-btn-in .mfp-close {
  color: #bdbdbd;
  font-weight: normal;
}

.mepr-switch-pm-mfp.mfp-close-btn-in .mfp-close:hover,
.mepr-switch-pm-mfp.mfp-close-btn-in .mfp-close:focus {
  background: transparent;
}

.mepr-switch-pm-popup-icon {
  width: 49px;
  margin: 5px auto;
}

.mepr-switch-pm-popup p {
  font-size: 21px;
  font-weight: 400;
  line-height: 140%;
  margin: 0;
}

.mepr-switch-pm-popup-buttons {
  margin-top: 35px;
  display: flex;
  gap: 20px;
  padding: 0 20px;
}

.mepr-switch-pm-popup .mepr-btn {
  border-radius: 32px;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  line-height: 140%;
  background: linear-gradient(90deg, #08439D 50%, #137CB3 85%);
  padding: 12px 30px 10px 30px;
  text-decoration: none;
  cursor: pointer;
  margin: 0;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mepr-switch-pm-popup .mepr-btn:hover {
  background: linear-gradient(90deg, #006FAC 0%, #05BCD6 100%), #0282C9;
  text-decoration: none;
}

.mepr-switch-pm-popup .mepr-btn:active {
  background: #06429E;
}

.mepr-switch-pm-popup .mepr-btn-secondary {
  background: #FFFFFF;
  border: 1px solid #06429E;
  color: #06429E;
}

.mepr-switch-pm-popup .mepr-btn-secondary:hover {
  background: #FFFFFF;
}

.mepr-switch-pm-popup .mepr-btn-secondary:active {
  background: #F3F6FA;
}

.mepr-tax-invoice small { font-weight: normal !important; text-transform:none !important; }
