.mepr-hidden {
  display: none;
}

div#license {
  display:block;
}

hr {
  border:none;
  background-color:#CCC;
  height:1px;
  margin-top:12px;
  margin-bottom:13px;
}

.submit {
  margin:10px 0 5px 0;
  padding:0;
}

.mepr-options-hidden-pane {
  display:none;
  margin-top:0;
  padding-top:0;
}

textarea#mepr-custom-message {
  width: 500px;
  height: 100px;
}

#i18n .mepr-field-label {
  display: inline-block;
  width: 100px;
}

#mepr-integration-table,
#mepr-integration-table tr,
#mepr-integration-table tr td {
  vertical-align: top;
}

#mepr-integration-table {
  margin-top: -25px;
}

.mepr-integration-delete {
  float: right;
  margin-top: -10px;
}

.mepr-integration {
  margin: 0 0 5px 0;
  padding: 15px;
  border: 1px solid #999999;
  line-height: 30px;
  background-color: white;
}

.mepr-stripe-customize-payment-methods {
  font-weight: normal;
}

.mepr-stripe-payment-methods > p {
  color: #626262;
  margin-bottom: 22px;
}

.mepr-stripe-payment-method {
  margin-top: 12px;
}

.mepr-stripe-payment-method label {
  color: #1d2327;
}

.mepr-update-stripe-payment-methods {
  margin-top: 22px;
}

.mepr-update-stripe-payment-methods button i {
  font-size: inherit;
  color: inherit;
}

.mepr-update-stripe-payment-methods button {
  justify-content: center;
}

ul.custom_options_list {
  margin:18px;
  padding:15px;
  background-color: transparent;
  border: 1px solid #DFDFDF;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
}

.mepr-custom-field {
  padding: 15px;
  margin-bottom: 5px !important;
}

.mepr-custom-field input[type=text],
.mepr-custom-field select {
  margin-right: 15px !important;
}

.mepr-custom-field a.mepr-custom-field-remove,
.mepr-custom-field a.mepr-option-remove {
  float: right;
}

.mepr-clipboard-input {
  background: #015289 !important;
  color: white !important;
  display: inline-block !important;
}

/* Autoresponder Stuff */
/* #mepr-mailchimp,
#mepr-adv-aweber,
#mepr-getrepsonse,
#mepr-activecampaign,
#mepr-mailpoet,
#mepr-constantcontact,
#mepr-madmimi, */
.mepr-autoresponder-config {
  margin-top: 10px;
  margin-bottom: 10px;
}

#mepr-aweber-error,
#mepr-aweber-message {
  margin-bottom: 15px;
}

#mepr-aweber-api-code {
  width: 500px;
  height: 50px;
}

.mepr-payment-description {
  width: 35em;
}

.mepr-integration-payment-method-id {
  font-weight: bold;
}

#mepr-integration-separator-cell {
  padding-left: 15px;
}

.mepr-options-panel {
  padding-left: 10px;
}

.mepr-options-table tr td {
  vertical-align: top;
  width: 415px;
}

.mepr-integration-setup-form {
  vertical-align: top;
  display: inline-block;
  width: 33%;
}

.mepr-integration-gateway-form {
  vertical-align: top;
  display: inline-block;
}

.mepr-integration-gateway-form input[type=text] {
  width: 25em !important;
}

#mepr_tax_rates_box {
  border: 1px solid #ededed;
  width: 100%;
  max-height: 250px;
  overflow: scroll;
}

#mepr_custom_tax_rates {
  width: 100% !important;
}

.mepr-tax-rates-remove {
}

.form-table td.mepr-sub-box-wrapper {
  padding: 0 !important;
}

#mepr-license-key {
  width: 400px;
  max-width: 100%;
}

.mepr-license-active {
  padding: 10px;
  background-color: #caffcd;
  border: 1px solid #27820d;
  margin: 25px 0;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  width: 450px;
}

.mepr-license-active h4 {
  font-size: 18px;
  margin: 2px 0 10px 0;
}

.mepr-deactivate-button {
  margin-top: 15px;
  margin-bottom: 7px;
}

@media only screen and (max-width: 1061px) {
  .mepr-integration-setup-form {
    vertical-align: top;
    display: block;
    width: auto;
  }

  .mepr-integration-gateway-form {
    padding-top: 25px;
    padding-left: 0;
    margin-left: 0;
    vertical-align: top;
    display: block;
  }

  .mepr-integration-gateway-form table {
    padding-left: 0;
    margin-left: 0;
    border-spacing: 0;
  }

  .mepr-integration-gateway-form input[type=text] {
    width: 25em !important;
  }
}
.mepr-detected-ip-address-p {
  margin-top: 15px;
}
#mepr-detected-ip-address {
  margin-left: 5px;
}
#mepr-anti-card-testing-blocked {
  width: 300px;
  max-width: 100%;
  height: 200px;
}
#mepr_options_form #integration {
  color: #626262;
}
#mepr_options_form #integration #mepr-add-integration {
  color: #626262;
  text-decoration: none;
  font-weight: bold;
}
#mepr_options_form #integration .mepr-with-stripe {
  margin-top: 0;
  color: #365669;
  font-size: 1.1em;
}
#mepr_options_form #integration .mepr-with-stripe img {
  position: relative;
  top: 12px;
  left: -7px;
  width: 80px;
}

#integration .mepr-paypal-standard-upgrade-box {
  text-align: center;
  max-width: 500px;
  background: #f3f3f4;
  padding: 2em;
}

#integration .mepr-paypal-standard-upgrade-box > button {
  height: 4em;
}

#integration .mepr-paypal-standard-upgrade-box button img {
  position: relative;
  top: 3px;
  padding-right: 0.5em;
}

#mepr-install-license-edition-loading {
  margin-left: 10px;
}

body li span.mp-icon-drag-target {
  display:inline-block;
  vertical-align:middle;
  font-size:24px;
  position:relative;
  left:-6px;
}

body p.mepr-custom-fields-p {
  padding:0;
  margin:0;
  margin-top:10px;
  margin-bottom:5px;
  word-break:break-all;
}

th.stripe-checkbox-column-left {
  min-width: 250px;
}

td.stripe-checkbox-column-right {
  max-width: 20%;
}

.mepr-stripe-tax-enabled #mepr_tax_calc_location_section,
.mepr-stripe-tax-enabled #mepr_tax_default_address_section,
.mepr-stripe-tax-enabled #mepr_vat_country_section,
.mepr-stripe-tax-enabled #mepr_vat_tax_businesses_section,
.mepr-stripe-tax-enabled #mepr_charge_business_customer_net_price_section,
.mepr-stripe-tax-enabled #mepr_show_negative_tax_on_invoice_section {
  display: none !important;
}
