/* Membership Options Nav */
div#product_options_wrapper {
  padding:10px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  background-color: white;
}

.mepr-hidden {
  display: none;
}

/*Sortable UI stuff*/
.mepr-sortable {
  list-style-position:inside;
  margin:0;
  padding:0;
  width:100%;
}

.mepr-sortable li {
  margin:0 3px 3px 3px;
  padding:0.4em;
  padding-left:1em;
  border:1px dashed silver;
}

/* Who can purchase meta box */
ol#who-can-purchase-list {
  margin-top:10px;
  margin-bottom:10px;
}

span.who_have_purchased {
  display:none;
}

div#who_can_purchase_hidden_row {
  display:none;
}

div#cannot_purchase_message label {
  display:block;
  margin-top:10px;
  margin-bottom:8px;
}

/* Pricing Page Meta Box */
div#mepr-price-box-configuration {
  padding:10px;
}

div.wp-editor-container {
  background:white;
}

li.benefit-item input {
  width:60%;
}

div.pricing-options-pane {
  overflow:hidden; /* HAS TO BE HERE - Seriously, don't remove it ;) or the sky will fall on your head */
}

div.pricing-options-pane textarea,
div.pricing-options-pane input#_mepr_product_pricing_title,
div.pricing-options-pane input#_mepr_product_pricing_button_text {
  width:99%;
}

.mepr-meta-sub-pane {
  margin-left: 4px;
  margin-top: 5px;
}

/*Preview Box*/
div#preview-pane {
  float:right;
  padding:0;
  margin:0px 0px 10px 10px;
}

div.mepr-price-box {
  width:250px;
  min-height:50px;
  margin: 5px 5px 10px 10px;
  padding:10px;
  border:1px solid #333;
  background:#EEE;
}

div.mepr-price-box div {
  padding:0;
  margin:0;
  display:block;
  width:100%;
  margin-bottom:15px;
}

span.pricing-preview {
  display:block;
  margin-bottom:5px;
  font-weight:bold;
}

div.mepr-price-box-title {
  font-weight:bold;
  font-size:150%;
  text-align:center;
}

div.mepr-price-box-benefits ul {
  list-style-type:disc;
  list-style-position:inside;
  padding:0;
  margin:0;
}

div.mepr-price-box-button {
  text-align:center;
}

.mepr-meta-pane {
  margin: 10px 10px 25px 10px;
  padding: 15px;
  border: 1px solid #DFDFDF;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

#sortable-benefits {
  margin-top: 10px;
  margin-bottom: 10px;
}

.remove-span {
  float:right;
  padding-top:4px;
}

.mepr_hidden {
  display:none;
}

div#mepr-shortcode-product-signup-form-area {
  margin-top:15px;
  padding:0 15px;
  font-weight:bold;
}

div#mepr-product-thank-you-area,
#mepr-product-tax-class-fields {
  margin-top:5px;
  padding:15px;
}

div#mepr-product-thank-you-page-id,
div#mepr-product-thank-you-message {
  display: none;
}

#mepr-product-manually-place-form,
#mepr-custom-login-redirect-urls,
#mepr-product-payment-methods-wrap,
#mepr-product-profile-fields-wrap,
#mepr-product-disable-address-fields-wrap,
#mepr-product-allow-gifting-fields-wrap,
.mepr-product-adv-item{
  margin-top:20px;
}

div#mepr-custom-login-urls-area {
  margin-top:10px;
  padding:0 15px;
}

div#custom-login-urls-list-area {
  margin-top:15px;
}

ul#custom-login-urls-list {
  padding:0;
  margin:10px 0 0 0;
}

.custom_login_urls_item {
  display:block;
  padding:5px 10px 5px 10px;
  border:1px solid #CCC;
}

span#custom-login-url {
  display:inline-block;
  padding:5px;
  background:#EEE;
  border:1px solid #CCC;
}

div#mepr-product-additional-shortcodes-area {
  margin-top:10px;
  margin-bottom:10px;
}

#mepr-product-inactive-payment-methods-title,
#mepr-product-inactive-payment-methods,
#mepr-product-inactive-profile-fields-title,
#mepr-product-inactive-profile-fields {
  color: #888;
  font-style: italic;
}

.mepr-profile-field-row {
  cursor:pointer;
}

#mepr-product-welcome-email {
  margin-top: 15px;
  margin-bottom: 15px;
}

#mepr-product-welcome-email .mepr-emails-wrap label {
  width: auto !important;
  min-width: none !important;
  max-width: none !important;
}

.nav-tab-active,
.nav-tab-active:hover {
  background-color: white;
  border-bottom-color: white;
}

.product-options-panel {
  padding: 10px;
}

.mepr-emails-wrap label {
  display: inline !important;
  min-width: auto !important;
  width: auto !important;
  max-width: auto !important;
}

table#mepr-interval-options tr td {
  vertical-align: top;
}

table#mepr-interval-options tr td.mepr-interval-label {
  padding-top: 5px;
}

input#_mepr_product_period-custom,
input#_mepr_product_period-custom:focus,
input#_mepr_product_period-custom:hover,
input#_mepr_expire_after,
input#_mepr_expire_after:focus,
input#_mepr_expire_after:hover,
input#_mepr_product_trial_days,
input#_mepr_product_trial_days:focus,
input#_mepr_product_trial_days:hover,
input#_mepr_product_limit_cycles_expires_after,
input#_mepr_product_limit_cycles_expires_after:focus,
input#_mepr_product_limit_cycles_expires_after:hover,
input#_mepr_product_limit_cycles_num,
input#_mepr_product_limit_cycles_num:focus,
input#_mepr_product_limit_cycles_num:hover {
  border: none !important;
  box-shadow: none !important;
}

span.mepr-price-box-price-loading {
  display:none;
}

/* Clean up rule edit page */
#submitdiv #misc-publishing-actions,
#submitdiv #minor-publishing-actions {
  display: none;
}

/* Clean up rule listing page */
.view-switch, #post-query-submit {
  display: none;
}

#mepr-custom-pricing-display {
  display: none;
}

/* Order Bumps Upsell */
#poststuff .order-bumps-upsell h2 {
  font-size: 21px;
  font-weight: 600;
  line-height: 31px;
  color: #091D2E;
  margin: 30px 0 5px 0;
  padding: 0;
  text-align: center;
}
#poststuff .order-bumps-upsell h4 {
  font-size: 17px;
  font-weight: 600;
  line-height: 31px;
  color: #091D2E;
  margin: 0 0 25px 0;
  padding: 0;
  text-align: center;
}
#poststuff .order-bumps-upsell p {
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #223443;
  margin: 0 auto 20px auto;
  max-width: 650px;
  text-align: center;
}
#poststuff .order-bumps-upsell p:last-child {
  margin-top: 40px;
}
#poststuff .order-bumps-upsell .mepr-order-bumps-upgrade {
  display: inline-block;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 700;
  line-height: 16px;
  text-decoration: none;
  border-radius: 100px;
  background: linear-gradient(90deg, #CF3200 0%, #CF3200 0.01%, #F47B0D 100%);
  transition: none;
  padding: 16px 23px;
  margin: 0 0 25px 0;
}
#poststuff .order-bumps-upsell .mepr-order-bumps-upgrade img {
  margin-left: 13px;
}
