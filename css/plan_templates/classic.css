/* CSS for plans here */
.mepr-price-menu.classic {
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: center !important;
}

.mepr-price-menu.classic .mepr-price-boxes {
  padding: 0;
  margin: 0 auto !important;
  width: 100%;
  margin-left: 0;
}

.mepr-price-menu.classic .mepr-price-box {
  vertical-align: middle;
  display: inline-block;
  padding: 15px 0 30px 0;
  margin: 0;
  /* margin-left: -8px; */
  text-align: center;
}

/* Right now we're good up to 10 columns */
.mepr-price-menu.classic .mepr-price-boxes.mepr-1-col .mepr-price-box {
  width: 100%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-2-col .mepr-price-box {
  width: 47%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-3-col .mepr-price-box {
  width: 30%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-4-col .mepr-price-box {
  width: 22.5%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-5-col .mepr-price-box {
  width: 18%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-6-col .mepr-price-box {
  width: 14.5%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-7-col .mepr-price-box {
  width: 12%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-8-col .mepr-price-box {
  width: 11%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-9-col .mepr-price-box {
  width: 9%;
}

.mepr-price-menu.classic .mepr-price-boxes.mepr-10-col .mepr-price-box {
  width: 8%;
}

.mepr-price-menu.classic .mepr-price-box.highlighted {
  font-size: 120%;
  /*
  margin: 0;
  padding: 20px 0;
  */
}

.mepr-price-menu.classic .mepr-price-box-title {
  font-weight: bold;
  font-family: sans-serif !important;
  margin: 0;
  padding-bottom: 10px;
}

.mepr-price-menu.classic .mepr-price-box-price {
  margin: 0;
  padding-bottom: 10px;
  font-weight: bold;
  font-family: sans-serif !important;
}

.mepr-price-menu.classic .mepr-price-box-heading {
  margin: 0;
  padding-bottom: 10px;
  font-family: sans-serif !important;
}

.mepr-price-menu.classic .mepr-price-box-head {
  padding: 10px;
}

.mepr-price-menu.classic .mepr-price-box-benefits {
  margin-bottom: 25px;
}

.mepr-price-menu.classic .mepr-price-box-benefits .mepr-price-box-benefits-list {
  margin: 15px 15px 20px 15px;
}

.mepr-price-menu.classic .mepr-price-box-benefits .mepr-price-box-benefits-list .mepr-price-box-benefits-item {
  margin: 0;
  padding: 10px 0;
}

.mepr-price-menu.classic .mepr-price-box-footer,
.mepr-price-menu.classic .mepr-price-box-button {
  margin: 10px 15px;
}

.mepr-price-menu.classic .mepr-price-box.highlighted .mepr-price-box-button a {
}

.mepr-price-menu.classic .mepr-price-box-button a {
  font-weight: bold;
}

.mepr-price-menu.classic .mepr-price-box-button a:hover {
  text-decoration: none;
}

.mepr-price-menu.classic .mepr-price-box .mepr-price-box-button a.mepr-disabled {
  cursor: default;
}

.mepr-price-menu.classic .mepr-price-box .mepr-price-box-button a.mepr-disabled:hover {
}

/* Responsive Design */
/* Effectively just pulls in the gray_vertical.css styles */
@media all and (max-width: 1200px) {
  .mepr-price-menu.classic .mepr-price-box-heading {
    font-size: 100%;
  }

  .mepr-price-menu.classic .mepr-price-box-benefits .mepr-price-box-benefits-list .mepr-price-box-benefits-item {
  }
}

@media all and (max-width: 1023px) {
  .mepr-price-menu.classic .mepr-price-menu, .mepr-price-boxes {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .mepr-price-menu.classic .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-1-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-2-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-3-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-4-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-5-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-6-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-7-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-8-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-9-col .mepr-price-box,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-10-col .mepr-price-box {
    width: 90%;
    /* padding: 10px; */
    margin: 0 auto;
    margin-bottom: 5px;
    text-align: center;
  }

  .mepr-price-menu.classic .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-1-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-2-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-3-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-4-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-5-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-6-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-7-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-8-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-9-col .mepr-price-box.highlighted,
  .mepr-price-menu.classic .mepr-price-boxes.mepr-10-col .mepr-price-box.highlighted {
    /* padding: 10px; */
    width: 95% !important;
  }

  .mepr-price-menu.classic .mepr-price-box-title {
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 5px;
  }

  .mepr-price-menu.classic .mepr-price-box-price {
    margin-top: 15px;
    margin-bottom: 15px;
    font-weight: bold;
  }

  .mepr-price-menu.classic .mepr-price-box-heading {
    margin-top: 5px;
  }

  .mepr-price-menu.classic .mepr-price-box-benefits .mepr-price-box-benefits-list {
    /*margin: 10px 0;*/
    /*font-weight: bold;*/
  }

  .mepr-price-menu.classic .mepr-price-box-benefits .mepr-price-box-benefits-list .mepr-price-box-benefits-item {
    margin: 0;
    padding-top: 5px;
    padding-bottom: 7px;
  }

  .mepr-price-menu.classic .mepr-price-box-footer {
    margin-top: 5px;
  }

  .mepr-price-menu.classic .mepr-price-box-button {
    margin: 10px 0;
  }

  .mepr-price-menu.classic .mepr-price-box-button a {
    font-weight: bold;
  }

  .mepr-price-menu.classic .mepr-price-box-button a:hover {
    text-decoration: none;
  }
}

