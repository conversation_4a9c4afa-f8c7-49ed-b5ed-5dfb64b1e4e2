/*--------------------------------------------------------------
# Generic
--------------------------------------------------------------*/
/* Normalize
--------------------------------------------- */
/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace;
  font-size: 1em;
}

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal;
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type=checkbox],
[type=radio] {
  box-sizing: border-box;
  padding: 0;
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type=search] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}

/* Box sizing
--------------------------------------------- */
/* Inherit box-sizing to more easily change it's value on a component level.
@link http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/ */
*,
*::before,
*::after {
  box-sizing: inherit;
}

html {
  box-sizing: border-box;
}

/*--------------------------------------------------------------
# Base
--------------------------------------------------------------*/
/* Typography
--------------------------------------------- */
body,
button,
input,
select,
optgroup,
textarea {
  color: #404040;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  clear: both;
}

p {
  margin-bottom: 1.5em;
}

dfn,
cite,
em,
i {
  font-style: italic;
}

blockquote {
  margin: 0 1.5em;
}

address {
  margin: 0 0 1.5em;
}

pre {
  background: #eee;
  font-family: "Courier 10 Pitch", courier, monospace;
  line-height: 1.6;
  margin-bottom: 1.6em;
  max-width: 100%;
  overflow: auto;
  padding: 1.6em;
}

code,
kbd,
tt,
var {
  font-family: monaco, consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
}

abbr,
acronym {
  border-bottom: 1px dotted #666;
  cursor: help;
}

mark,
ins {
  background: #fff9c0;
  text-decoration: none;
}

big {
  font-size: 125%;
}

/* Elements
--------------------------------------------- */
body {
  background: #fff;
}

[x-cloak] {
  display: none !important;
}

hr {
  background-color: #ccc;
  border: 0;
  height: 1px;
  margin-bottom: 1.5em;
}

ul,
ol {
  margin: 0 0 1.5em 3em;
}

ul {
  list-style: disc;
}

ol {
  list-style: decimal;
}

li > ul,
li > ol {
  margin-bottom: 0;
  margin-left: 1.5em;
}

dt {
  font-weight: 700;
}

dd {
  margin: 0 1.5em 1.5em;
}

/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
  max-width: 100%;
}

img {
  height: auto;
  max-width: 100%;
}

figure {
  margin: 1em 0;
}

table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
  caption-side: bottom;
  border-collapse: collapse;
}
table th,
table td {
  padding: 0.75rem;
  vertical-align: top;
  text-align: left;
}

/* Links
--------------------------------------------- */
a {
  color: #06429E;
  text-decoration: none;
}
a:visited {
  color: #06429E;
}
a:hover, a:focus, a:active {
  color: #191970;
}
a:focus {
  outline: thin dotted;
}
a:hover, a:active {
  outline: 0;
}

/* Forms
--------------------------------------------- */
button,
.mepr-button,
input[type=button],
input[type=reset],
input[type=submit] {
  border: 1px solid;
  border-color: #fff;
  background: #06429E;
  color: #fff;
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
button:hover,
.mepr-button:hover,
input[type=button]:hover,
input[type=reset]:hover,
input[type=submit]:hover {
  border-color: #ccc #bbb #aaa;
}
button:active, button:focus,
.mepr-button:active,
.mepr-button:focus,
input[type=button]:active,
input[type=button]:focus,
input[type=reset]:active,
input[type=reset]:focus,
input[type=submit]:active,
input[type=submit]:focus {
  border-color: #aaa #bbb #bbb;
}
button.btn-link,
.mepr-button.btn-link,
input[type=button].btn-link,
input[type=reset].btn-link,
input[type=submit].btn-link {
  font-weight: 400;
  color: #007bff;
  background-color: transparent;
  border: 0;
}

.dropdown {
  position: absolute;
  z-index: 1;
  transform: translate3d(0px, 38px, 0px);
  top: 0px;
  left: 0px;
  will-change: transform;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}
.dropdown a {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
}

.mepr-button.btn-outline {
  background: transparent;
  color: #06429E;
  border-color: #06429E;
}

.mepr-form .mp-address-group {
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08), 0px 1px 1px rgba(0, 0, 0, 0.04);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.mepr-form .mp-address-group .mp-form-row {
  margin: 0;
}
.mepr-form .mp-address-group .mp-form-row .mp-form-label {
  display: none;
}
.mepr-form .mp-address-group .mepr-form-input,
.mepr-form .mp-address-group input[type=text] {
  margin: 0;
  box-shadow: none;
  border-radius: 0;
  height: calc(1.7em + 0.75rem + 2px);
}
.mepr-form .mp-address-group > .mp-form-row + .mp-form-row .mepr-form-input {
  margin-top: -1px;
}
.mepr-form .mp-address-group .mp-form-row:first-child .mepr-form-input {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.mepr-form .mp-address-group .mp-form-row:last-child .mepr-form-input {
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.mepr-form #mp-address-group-label {
  display: none;
}
.mepr-form input[type=text],
.mepr-form input[type=email],
.mepr-form input[type=url],
.mepr-form input[type=password],
.mepr-form input[type=search],
.mepr-form input[type=number],
.mepr-form input[type=tel],
.mepr-form input[type=range],
.mepr-form input[type=date],
.mepr-form input[type=month],
.mepr-form input[type=week],
.mepr-form input[type=time],
.mepr-form input[type=datetime],
.mepr-form input[type=datetime-local],
.mepr-form input[type=color],
.mepr-form textarea {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  /* padding: 0.375rem 0.75rem; */
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08), 0px 1px 1px rgba(0, 0, 0, 0.04);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.mepr-form input[type=text]:focus,
.mepr-form input[type=email]:focus,
.mepr-form input[type=url]:focus,
.mepr-form input[type=password]:focus,
.mepr-form input[type=search]:focus,
.mepr-form input[type=number]:focus,
.mepr-form input[type=tel]:focus,
.mepr-form input[type=range]:focus,
.mepr-form input[type=date]:focus,
.mepr-form input[type=month]:focus,
.mepr-form input[type=week]:focus,
.mepr-form input[type=time]:focus,
.mepr-form input[type=datetime]:focus,
.mepr-form input[type=datetime-local]:focus,
.mepr-form input[type=color]:focus,
.mepr-form textarea:focus {
  color: #111;
}
.mepr-form select {
  border: 1px solid #ccc;
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  -moz-padding-start: calc(0.75rem - 3px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.mepr-form select[multiple] {
  padding-right: 0.75rem;
  background-image: none;
}
.mepr-form textarea {
  width: 100%;
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 90px;
}
.mepr-form .iti {
  width: 100%;
}

/*--------------------------------------------------------------
# Layouts
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Components
--------------------------------------------------------------*/
.site-header {
  display: flex;
  position: relative;
  z-index: 999;
  background-color: #06429E;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
}
.site-header .site-branding__logo {
  display: block;
  width: auto;
  max-height: 100px;
  height: 40px;
}
.mepr-guest-layout .site-branding img.site-logo {
  max-height: 100px;
  width: 100%;
}

.mepr-guest-layout main{
  padding-bottom: 6em;
}


.site-header .profile-menu__dropdown {
  transform: translate3d(0px, 58px, 0px) !important;
  right: 0;
  left: auto;
}
.site-header .profile-menu__button-group {
  position: relative;
}
.site-header .profile-menu__button {
  background: transparent;
  display: none;
  padding: 0.5rem;
  border: 0;
  cursor: pointer;
}
.site-header .profile-menu__button:hover {
  background: rgba(0, 0, 0, 0.4);
}
.site-header .profile-menu__avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  margin-right: 0.75rem;
}
.site-header .profile-menu__text {
  color: #fff;
  display: block;
  text-align: left;
  margin-right: 1rem;
}
.site-header .profile-menu__text--small {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0px;
}
.site-header .profile-menu__arrow_down {
  width: 1.5rem;
  height: 1.5rem;
  color: #fff;
}
.site-header .profile-menu__hamburger {
  color: #fff;
  width: 1.5rem;
  height: 1.5rem;
}
.site-header .dropdown__item,
.site-header .dropdown a {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
}
.site-header .dropdown__item:hover,
.site-header .dropdown__item:focus,
.site-header .dropdown a:hover,
.site-header .dropdown a:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}
@media (max-width: 839px) {
  .site-header .profile-menu__button.--is-mobile {
    display: block;
  }
}
@media (min-width: 840px) {
  .site-header .profile-menu__button.--is-tablet {
    display: flex;
    align-items: center;
  }
}

.guest-layout .site-header {
  display: flex;
  justify-content: center;
  justify-items: center;
  padding: 3rem 0;
}


.mepr_pro_error,
.mepr-unauthorized-message {
  display: flex;
  align-items: center;
  margin-top: 2rem;
}
.mepr_pro_error > * + *,
.mepr-unauthorized-message > * + * {
  margin-left: 1rem;
}
.mepr_pro_error .mepr_pro_error_content {
  flex: 1;
}
.mepr_pro_error ul,
.mepr-unauthorized-message ul {
  list-style-type: none;
  padding: 0;
  margin-bottom: 0;
}
.mepr_pro_error p,
.mepr-unauthorized-message p {
  margin: 0;
}
.mepr_pro_error svg,
.mepr-unauthorized-message svg {
  color: #eb5757;
  width: 3rem;
  height: 3rem;
}
.mepr-guest-layout .mepr_pro_error {
  display: none;
}
.mepr-guest-layout .mepro-login-contents .mepr_pro_error {
  display: flex;
}
.mp-form-row.mepr_forgot_password_input label {
  display: block !important;
  margin-bottom: 7px;
}
.table-responsive{
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.flex-centered{
  justify-content: center;
  align-items: center;
  /* height: 100vh; */
  display: flex;
}
/*--------------------------------------------------------------
# RTL
--------------------------------------------------------------*/
/* Login Form */
.rtl .mp_wrapper form {
  text-align: right;
}
.rtl #mepr-template-login .mepro-form button.mp-hide-pw {
  right: auto;
  left: 0;
}
.rtl #mepr-template-login .mepro-form .mepr_remember_me > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* Account Page */
.rtl #mepr-account-nav .mepr-nav-item a {
  justify-content: right;
}
.rtl #mepr-account-nav .mepr-nav-item a::before {
  margin-right: 0;
  margin-left: 10px;
}
.rtl .site-header .profile-menu__dropdown {
  right: auto;
  left: 0;
  text-align: right;
}
.rtl .site-header .profile-menu__text {
  text-align: right;
}

.rtl .button.mp-hide-pw {
  right: auto;
  left: 0;
}
@media (min-width: 840px) {
  .rtl #mepr-account-content {
    padding-right: 0;
    padding-left: 2em;
  }
}
@media (min-width: 1024px) {
  .rtl .mepr-checkout-container .form-wrapper {
    padding-right: 3rem;
    padding-left: 0;
    border-right: 1px solid #ccc;
    border-left: none;
  }

  .rtl .mepr-checkout-container .invoice-wrapper {
    padding-left: 3rem !important;
    padding-right: 0;
  }
}
