@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?dzcuuo');
  src:  url('fonts/icomoon.eot?dzcuuo#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?dzcuuo') format('truetype'),
    url('fonts/icomoon.woff?dzcuuo') format('woff'),
    url('fonts/icomoon.svg?dzcuuo#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-house:before {
  content: "\e901";
}
.icon-mortarboard:before {
  content: "\e902";
}
.icon-credit-card:before {
  content: "\e903";
}
.icon-arrow-repeat:before {
  content: "\e904";
}
.icon-arrow-right-circle:before {
  content: "\e905";
}
.icon-box-arrow-right:before {
  content: "\e900";
}
