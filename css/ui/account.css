/* Account page */
div#mepr-account-nav {
  margin-bottom:25px;
  width: auto !important;
}

#mepr-subscriptions-paging,
#mepr-payments-paging {
  width:auto !important;
  margin-top: 10px;
  border-spacing: 0;
}

.mepr-nav-item {
  padding: 0 10px 0 0;
}

.mepr-active-nav-tab {
  font-weight: bold;
}

.mepr-active,
.mepr-inactive {
  font-weight: bold;
}

.mepr-active {
  color: limegreen;
}

.mepr-inactive {
  color: darkred;
}

.mepr-account-terms,
.mepr-account-subscr-id,
.mepr-account-rebill {
  font-size: 75%;
  font-weight: bold;
  font-style: italic;
  color: #464646;
}

/* NEW Responsive Tables */
.mp_wrapper table.mepr-account-table {
  border:1px solid #ccc;
  width:100% !important;
  margin:0;
  padding:0;
  border-collapse:collapse;
  border-spacing:0;
}

.mp_wrapper table.mepr-account-table tr {
  border:1px solid #ddd;
  padding:5px;
}

.mp_wrapper table.mepr-account-table td {
  padding:5px;
  text-align:left;
  border-left:1px solid #ededed;
}

.mp_wrapper table.mepr-account-table th {
  padding:5px;
  text-transform:uppercase;
  font-size:12px;
  letter-spacing:1px;
}

.mp_wrapper table.mepr-account-table tr.mepr-alt-row {
  background:#f9f9f9;
}

.mp_wrapper table.mepr-account-table .mepr-account-actions a {
  display: block;
}

@media screen and (max-width: 600px) {
  .mp_wrapper table.mepr-account-table {
    border:0;
  }

  .mp_wrapper table.mepr-account-table thead {
    display:none !important;
  }

  .mp_wrapper table.mepr-account-table tr {
    margin-bottom:10px;
    display:block;
    border-bottom:2px solid #ddd;
  }

  .mp_wrapper table.mepr-account-table td {
    display:block;
    text-align:right;
    border-top:none;
    border-left:none;
    border-bottom:1px dotted #ccc;
  }

  .mp_wrapper table.mepr-account-table td:last-child {
    border-bottom:0;
  }

  .mp_wrapper table.mepr-account-table td div{
    padding:0;
    margin:0;
    margin-left:15px;
  }

  .mp_wrapper table.mepr-account-table td:before {
    content:attr(data-label);
    float:left;
    text-transform:uppercase;
    font-weight:bold;
  }

  .mepr-account-subscr-id,
  .mepr-account-auto-rebill,
  .mepr-account-rebill {
    display:none;
  }

  .mepr-account-terms,
  .mepr-account-subscr-id,
  .mepr-account-rebill {
    font-size: inherit;
    font-weight: inherit;
    font-style: inherit;
    color: inherit;
  }
}
.mepr-two-factor-options th {
    vertical-align: top;
    text-align: left;
    padding: 20px 10px 20px 0;
    width: 200px;
    line-height: 1.3;
    font-weight: 600;
}
.mepr-two-factor-options td {
    vertical-align: top;
    margin-bottom: 9px;
    padding: 15px 10px;
    line-height: 1.3;
}
.mepr-two-factor-options .two-factor-method-label,
.mepr-two-factor-options select[name="_two_factor_provider"] {
    margin-bottom: 10px;
}
.mepr-two-factor-options p {
    margin: 0 0 1.5em 0;
}
.mepr-two-factor-options label[for="two-factor-totp-authcode"],
.mepr-two-factor-options input[type="text"],
.mepr-two-factor-options select {
    width: auto !important;
}
