div#hidden-line-item {
  display: none;
}

.remove-span {
  float: right;
  padding-top: 4px;
}

div#mepr-groups-form {
  padding: 10px;
}

#products-label {
  margin-top: 15px;
  display: inline-block;
}

/*Sortable UI stuff*/
.mepr-sortable {
  list-style-position: inside;
  margin: 0;
  padding: 0;
  width: 100%;
}

.mepr-sortable li {
  margin: 0 3px 3px 3px;
  padding: 0.4em;
  padding-left: 1em;
  border: 1px dashed silver;
}

.mepr-hover {
  cursor: move;
}

/*Group Style Options*/
select.group-style-select {
  width: 125px;
}

div#group-option-dropdowns {
  margin-top: 20px;
}

#group-label-for-dropdowns {
  font-weight: bold;
  margin-top: 0px;
  padding-top: 0px;
}

#mepr_hidden_pricing_page_theme,
#mepr_hidden_alternate_group_url {
  margin-top: 5px;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 3px;
  display: block;
}

div#mepr-shortcode-group-pricing-boxes-area {
  margin-top: 5px;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-weight: bold;
}

#mepr-group-price-box-shortcodes {
  display: none;
}

.mepr-group-button-css {
  width: 99%;
}

/* Clean up edit page */
#submitdiv #misc-publishing-actions,
#submitdiv #minor-publishing-actions {
  display: none;
}

/* Clean up listing page */
.view-switch,
#post-query-submit {
  display: none;
}

#readylaunch-group-limit {
  display: none;
  background: #fff;
  border-top: 1px solid #c3c4c7;
  border-right: 1px solid #c3c4c7;
  border-bottom: 1px solid #c3c4c7;
  border-left-width: 4px;
  border-left-style: solid;
  box-shadow: 0 1px 1px rgb(0 0 0 / 4%);
  margin: 5px 0 15px;
  padding: 10px;
}
