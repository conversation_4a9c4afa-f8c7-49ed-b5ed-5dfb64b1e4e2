@keyframes pulse-black {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

@keyframes pulse-green {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(51, 217, 178, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(51, 217, 178, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(51, 217, 178, 0);
  }
}

.memberpress-menu-pulse {
  background: black;
  border-radius: 50%;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 1);
  margin: 0;
  height: 12px;
  width: 12px;
  transform: scale(1);
  animation: pulse-black 2s infinite;
  display: inline-block;
  position: relative;
  top: -10px;
  right: 5px;
}

.memberpress-menu-pulse.green {
  background: rgba(51, 217, 178, 1);
  box-shadow: 0 0 0 0 rgba(51, 217, 178, 1);
  animation: pulse-green 2s infinite;
}

.mepr-options-pane {
  padding-left: 15px;
  padding-bottom: 15px;
}

.mepr-options-sub-pane {
  margin: 15px;
  padding: 15px;
  border: 1px solid #999999;
}

.mepr-main-pane {
  padding-bottom: 15px;
}

.mepr-sub-pane {
  padding-left: 15px;
}

.mepr-sub-option-arrow,
.mepr-sub-option {
  padding-top: 5px;
  padding-left: 25px;
}

.mepr-sub-option-arrow {
  background-image: url("../images/sub-option-arrow.png");
  background-repeat: no-repeat;
  background-position: 3px 10px;
  margin-left: -25px;
}

.mepr-resend-welcome-email-loader {
  vertical-align: middle;
  width: 16px !important;
  height: 16px !important;
  margin-right: 5px;
  display: none;
}

/* WP List Table CSS */
#cspf-table-search,
#mepr_fake_submit {
  vertical-align: middle;
}
.cspf-tablenav-spacer {
  display: block;
  width: 100%;
  line-height: 85px;
}

.search-box {
  text-align: right !important;
}

.search-box .search-fields {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 3px;
  margin-bottom: 10px !important;
}

.search-box .filter-by {
  margin-bottom: 10px !important;
  display: block;
  text-align: right;
}

div#table-actions {
  display: block;
  padding-bottom: 5px;
  width: auto;
  float: left;
}

div#mepr-shortcode-login-page-area {
  margin-top: 5px;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-weight: bold;
}

#meprcustomunauthorizedmessage_tbl {
  background: white;
}

.mepr_hidden {
  display: none;
}

/* Make nav tabs look the same */
h2.nav-tab-wrapper {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

table.users .column-role {
  width: auto !important;
}

.mepr-active,
.mepr-inactive {
  font-weight: bold;
}

.mepr-active {
  color: limegreen;
}

.mepr-inactive {
  color: darkred;
}

.mepr-row-actions {
  visibility: hidden;
}

img.mepr_loader {
  vertical-align: -2px;
  display: none;
}

.button-primary .mp-icon {
  color: #fff;
  font-size: 13px;
}

.button-primary[disabled] .mp-icon {
  color: #666;
}

.mepr-radius-border {
  margin-top: 5px;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.mepr-vertical-separator {
  margin: 0 auto;
  padding: 0;
  top: 0;
  display: inline-block;
  width: 2px;
  max-width: 2px;
  min-width: 2px;
  height: 100%;
  background: #015289; /* Old browsers */
  background: -moz-linear-gradient(
    top,
    rgba(1, 82, 137, 0) 0%,
    rgba(1, 82, 137, 1) 15%,
    rgba(1, 82, 137, 1) 50%,
    rgba(1, 82, 137, 1) 85%,
    rgba(1, 82, 137, 0) 100%
  ); /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, rgba(1, 82, 137, 0)),
    color-stop(15%, rgba(1, 82, 137, 0)),
    color-stop(50%, rgba(1, 82, 137, 1)),
    color-stop(85%, rgba(1, 82, 137, 0)),
    color-stop(100%, rgba(1, 82, 137, 0))
  ); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    top,
    rgba(1, 82, 137, 0) 0%,
    rgba(1, 82, 137, 1) 15%,
    rgba(1, 82, 137, 1) 50%,
    rgba(1, 82, 137, 1) 85%,
    rgba(1, 82, 137, 0) 100%
  ); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(
    top,
    rgba(1, 82, 137, 0) 0%,
    rgba(1, 82, 137, 1) 15%,
    rgba(1, 82, 137, 1) 50%,
    rgba(1, 82, 137, 1) 85%,
    rgba(1, 82, 137, 0) 100%
  ); /* Opera 11.10+ */
  background: -ms-linear-gradient(
    top,
    rgba(1, 82, 137, 0) 0%,
    rgba(1, 82, 137, 1) 15%,
    rgba(1, 82, 137, 1) 50%,
    rgba(1, 82, 137, 1) 85%,
    rgba(1, 82, 137, 0) 100%
  ); /* IE10+ */
  background: linear-gradient(
    to bottom,
    rgba(1, 82, 137, 0) 0%,
    rgba(1, 82, 137, 1) 15%,
    rgba(1, 82, 137, 1) 50%,
    rgba(1, 82, 137, 1) 85%,
    rgba(1, 82, 137, 0) 100%
  ); /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#015289', endColorstr='#015289',GradientType=0 ); /* IE6-9 */
}

.mepr-hidden {
  display: none;
}

.mepr-hover {
  cursor: move;
}

.mepr-strikethrough {
  text-decoration: line-through;
}

.mepr-rules-table {
  width: 100%;
}

table.mepr-rules-table td {
  vertical-align: top;
}

table.mepr-rules-table td.mepr-rules-col {
  min-width: 260px;
}

.column-mepr-access {
  width: 15%;
}

.mepr-clipboard-input {
  display: inline-block !important;
}

.mepr-clipboard {
  cursor: pointer !important;
}

.mp-icon {
  color: #666666;
  font-size: 16px;
}

.mp-icon.mp-16 {
  font-size: 16px !important;
}

.mp-icon.mp-18 {
  font-size: 18px !important;
}

.mp-icon.mp-20 {
  font-size: 20px !important;
}

.mp-icon.mp-22 {
  font-size: 22px !important;
}

.mp-icon.mp-24 {
  font-size: 24px !important;
}

.mp-icon.mp-32 {
  font-size: 32px !important;
}

.mp-icon.mp-48 {
  font-size: 48px !important;
}

.mp-icon.mp-64 {
  font-size: 64px !important;
}

.mp-icon.mp-flip-horizontal:before {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

a i.mp-icon {
  text-decoration: none;
}

a i.mp-icon:hover {
  color: #aeaeae;
}

.mepr-tooltip {
  cursor: pointer;
  vertical-align: middle;
}

.mepr-tooltip:hover i {
  color: #aeaeae;
}

.mp-row {
  display: block;
}

.mp-row .mp-col-1 {
  vertical-align: top;
  display: inline-block;
  width: 97px;
  padding: 5px;
}

.mp-row .mp-col-2 {
  vertical-align: top;
  display: inline-block;
  width: 194px;
  padding: 5px;
}

.mp-row .mp-col-3 {
  vertical-align: top;
  display: inline-block;
  width: 291px;
  padding: 5px;
}

.mp-row .mp-col-4 {
  vertical-align: top;
  display: inline-block;
  width: 388px;
  padding: 5px;
}

.mp-row .mp-col-5 {
  vertical-align: top;
  display: inline-block;
  width: 485px;
  padding: 5px;
}

.mp-row .mp-col-6 {
  vertical-align: top;
  display: inline-block;
  width: 582px;
  padding: 5px;
}

.mp-row .mp-col-7 {
  vertical-align: top;
  display: inline-block;
  width: 679px;
  padding: 5px;
}

.mp-row .mp-col-8 {
  vertical-align: top;
  display: inline-block;
  width: 776px;
  padding: 5px;
}

.mp-row .mp-col-9 {
  vertical-align: top;
  display: inline-block;
  width: 873px;
  padding: 5px;
}

.mp-row .mp-col-10 {
  vertical-align: top;
  display: inline-block;
  width: 970px;
  padding: 5px;
}

.mp-row .mp-col-11 {
  vertical-align: top;
  display: inline-block;
  width: 1067px;
  padding: 5px;
}

.mp-row .mp-col-12 {
  vertical-align: top;
  display: inline-block;
  width: 1164px;
  padding: 5px;
}

.mepr-active {
  color: limegreen;
}

.mepr-inactive {
  color: darkred;
}

.mepr_error,
.mepr_updated {
  padding: 5px 5px 5px 15px !important;
  margin-bottom: 25px !important;
  width: 95%;
}

.mepr_error {
  background-color: #feb9bb !important;
  border: 1px solid #d40022 !important;
}

.mepr_error ul {
  padding: 0 0 5px 0 !important;
  margin: 0 !important;
}

.mepr_error ul li {
  list-style-type: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.mepr_updated {
  background-color: #def4c5 !important;
  border: 1px solid #4d8c2e !important;
}

table.mepr-addons-table {
  box-sizing: border-box;
  width: 100%;
  border-collapse: collapse;
}

table.mepr-addons-table tr th,
table.mepr-addons-table tr td {
  text-align: left;
  padding: 15px;
  border: 1px solid #444444;
}

table.mepr-addons-table tr th {
  font-weight: bold;
  font-size: 120%;
}

.mepr-payment-option-prompt {
  background: rgba(139, 139, 150, 0.1);
  box-shadow: 0 0 1px #bbb;
  align-items: center;
  justify-content: center;
  padding: 1em;
  color: #0a2540;
  width: 600px;
  text-align: center;
  font-size: 15px;
}

.mepr-payment-option-prompt img.mepr-pp-icon {
  margin-right: 5px;
  position: relative;
  top: 3px;
}

.mepr-integration-gateway-form .stripe-feature-list {
  margin: auto;
}

.mepr-integration-gateway-form .stripe-features li,
.mepr-integration-gateway-form .paypal-features li {
  margin: 0;
  text-align: left;
}

.mepr-integration-gateway-form .stripe-features li img,
.mepr-integration-gateway-form .paypal-features li img {
  padding-right: 3px;
}

.mepr-payment-option-prompt.connected {
  background: #f3f3f3;
  box-shadow: none;
  margin-bottom: 25px;
}

.mepr-integration-gateway-form .stripe-checkout-method-select {
  display: flex;
  margin-top: 15px;
  align-items: stretch;
  color: #0a2540;
  justify-content: space-around;
}

.mepr-integration-gateway-form .stripe-title {
  font-size: 1.5em;
  font-weight: bold;
  color: #0a2540;
}

.mepr-integration-gateway-form
  .stripe-checkout-method-select
  .mepr-stripe-method {
  width: 45%;
  border: 4px solid #bdbdbd;
  border-radius: 5px;
}
.mepr-integration-gateway-form
  .stripe-checkout-method-select
  .mepr-stripe-method.selected {
  border-color: #0a2540;
}
.mepr-integration-gateway-form .mepr-stripe-method {
  position: relative;
}
.mepr-integration-gateway-form .mepr-paypal-onboarding-button {
  line-height: 3.5em;
}
.mepr-integration-gateway-form .mepr-paypal-onboarding-button {
  line-height: 3.5em;
}
.mepr-integration-gateway-form .mepr-paypal-onboarding-button {
  line-height: 3.5em;
  margin: 5px 10px;
}
.mepr-stripe-method .stripe-features {
  padding: 0px 40px;
}
.mepr-stripe-method .mepr-heading-section {
  font-size: 1.5em;
  padding: 10px 10px 0px 10px;
}
.mepr-stripe-method input {
  position: absolute;
  top: 14px;
  right: 10px;
}

.mepr-payment-option-prompt.connected .stripe-btn {
  border-radius: 7px;
  background: #00528c;
  color: white;
  font-weight: bold;
  padding: 0px 10px;
}

.mepr-stripe-setting-promo {
  margin: 0;
}
@media (max-width: 1100px) {
  .mepr-payment-option-prompt {
    width: auto;
  }
}
@media (max-width: 750px) {
  .mepr-integration-gateway-form
    .stripe-checkout-method-select
    .mepr-stripe-method {
    display: block;
    width: auto;
  }
  .mepr-integration-gateway-form .stripe-checkout-method-select {
    display: block;
  }
}
.mepr_stripe_disconnect_button .dashicons-warning {
  display: inline-block;
  margin-top: 3px;
}

/* Override for simplegrid styles */
.grid.mepr-left-aligned-grid {
  margin: 0 !important;
  padding-left: 0 !important;
}

#wpcontent #mp-admin-header {
  margin-left: -20px;
  padding: 15px;
  padding-left: 22px;
  background-color: #fff;
  min-height: 36px;
  border-bottom: 2px solid #00cee6;
}
@media screen and (max-width: 600px) {
  #wpcontent #mp-admin-header {
    position: relative;
    top: 46px;
  }
}
#wpcontent #mp-admin-header img.mp-logo {
  height: 36px;
}
@media screen and (max-width: 390px) {
  #wpcontent #mp-admin-header img.mp-logo {
    height: 30px;
  }
}
#wpcontent #mp-admin-header .mp-admin-header-actions {
  align-items: center;
  display: flex;
  float: right;
  gap: 10px;
}

.mepr-account-connected {
  padding: 20px;
  background-color: #cbfecf;
  border: 1px solid #86c881;
}

.mepr-account-connected table th {
  min-width: 200px;
  text-align: left;
}

.mepr-warning-notice-icon {
  color: #dc3232 !important;
  font-size: 32px !important;
  vertical-align: top !important;
}

.mepr-shake-tooltip {
  font-size: 20px;
  color: #dc3232;
  position: absolute;
  right: 10px;
  top: 8px;
  cursor: help;
  z-index: 1000;
}

.mepr-shake {
  animation: meprshake 0.5s;
}

@keyframes meprshake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }
  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }
  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }
  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }
  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }
  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }
  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }
  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

/* Fix for CMSMasters Content Composer plugin where widgets are broken on edit post/page on the backend */
.cmsmastersBoxContIn.animate-spin,
.cmsmastersLightBoxContIn.animate-spin {
  animation: auto;
  display: block;
}

.mepr-warning-notice-title {
  vertical-align: top !important;
  margin-left: 18px !important;
  font-size: 18px !important;
  font-weight: bold !important;
  line-height: 32px !important;
}

.mepr-clearfix:before,
.mepr-clearfix:after {
  content: " ";
  display: block;
}

.mepr-clearfix:after {
  clear: both;
}

@media only screen and (max-width: 1050px) {
  .mp-row .mp-col-1 {
    width: 75px;
  }

  .mp-row .mp-col-2 {
    width: 150px;
  }

  .mp-row .mp-col-3 {
    width: 225px;
  }

  .mp-row .mp-col-4 {
    width: 300px;
  }

  .mp-row .mp-col-5 {
    width: 375px;
  }

  .mp-row .mp-col-6 {
    width: 450px;
  }

  .mp-row .mp-col-7 {
    width: 525px;
  }

  .mp-row .mp-col-8 {
    width: 600px;
  }

  .mp-row .mp-col-9 {
    width: 675px;
  }

  .mp-row .mp-col-10 {
    width: 750px;
  }

  .mp-row .mp-col-11 {
    width: 825px;
  }

  .mp-row .mp-col-12 {
    width: 900px;
  }
}

@media only screen and (max-width: 675px) {
  .mp-row .mp-col-1 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-2 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-3 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-4 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-5 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-6 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-7 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-8 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-9 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-10 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-11 {
    display: block;
    width: auto;
  }

  .mp-row .mp-col-12 {
    display: block;
    width: auto;
  }
}

.mepr-replace-file,
.mepr-view-file {
  text-decoration: none;
}

.mepr-replace-file {
  margin-right: 5px;
}

/* Help Manual Tab Styles */
div.mepr-support-help-wrapper {
  display: flex;
  flex-wrap: wrap;
}

div.mepr-support-help-topic {
  height: 115px;
  width: 250px;
  text-align: center;
  border: 1px solid #00cee6;
  padding: 10px;
  margin: 10px;
  flex-shrink: 0;
  background-color: #f8f8f8;
}

.mepr-support-help-topic a {
  text-decoration: none;
}

.mepr-support-help-topic h3 {
  color: #184699;
  line-height: 1.25em;
}

.mepr-support-help-topic p {
  color: #444;
}

.mepr-support-help-topic:hover {
  background-color: #e8e8e8;
}

.mepr__modal {
  position: relative;
  z-index: 10;
}

.mepr_modal__overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(107, 114, 128, 0.75);
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.mepr_modal__content_wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  overflow-y: auto;
}
.mepr_modal__content {
  display: flex;
  align-items: center;
  min-height: 100%;
  justify-content: center;
  text-align: center;
}
.mepr_modal__box {
  position: relative;
  max-width: 30rem;
  width: 100%;
  margin-top: 2rem;
  margin-bottom: 2rem;
  transform: matrix(1, 0, 0, 1, 0, 0);
  overflow: hidden;
  border-radius: 8px;
  background-color: #fff;
  padding-left: 25px;
  padding-right: 25px;
  padding-top: 29px;
  padding-bottom: 25px;
  text-align: left;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}
.mepr_modal__box h3 {
  font-size: 22px;
  margin-top: 0;
}
button.mepr_modal__close {
  /* float: right; */
  width: 1.5rem;
  line-height: 1.5rem;
  text-align: center;
  cursor: pointer;
  border-radius: 0.25rem;
  background: none;
  border: none;
  position: absolute;
  right: 15px;
  top: 15px;
  font-size: 1.3rem;
  color: #ccc;
}

button.mepr_modal__close:hover {
  color: #555;
}
.mepr_modal__button {
  display: flex !important;
  margin-left: auto !important;
}
/* Ends Modal */

.mepr-template-welcome-img-preview {
  display: grid;
  column-gap: 4rem;
  grid-template-columns: 1fr 1fr;
  border: 2px dashed #ccc;
  padding: 10px;
}
.mepr-template-welcome-img-preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
}

.mepr-template-welcome-img-preview .actions div {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.mepr-pluploader-wrapper .drag-drop-area {
  border: 4px dashed #c3c4c7;
  height: 200px;
}

.mepr-pluploader-preview {
  display: grid;
  column-gap: 4rem;
  grid-template-columns: 1fr 1fr;
  border: 2px dashed #ccc;
  padding: 10px;
}

.mepr-pluploader-wrapper .actions div {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.mepr-pluploader-wrapper .mepr-pluploader-preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
}
.mepr-shared-mfp.mfp-bg {
  z-index: 100005;
}
.mepr-shared-mfp.mfp-wrap {
  z-index: 100006;
}
.mepr-shared-mfp .mfp-content {
  z-index: 100007;
}
.mepr-shared-popup {
  position: relative;
  background: #fff;
  padding: 40px;
  width: auto;
  max-width: 600px;
  margin: 20px auto;
  border-radius: 6px;
}
.mepr-shared-popup > h2 {
  font-size: 20px;
  font-weight: 400;
  line-height: 35px;
  color: #091D2E;
  margin: 0 0 22px 0;
}
.mepr-shared-popup p {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: #5B748A;
}
.mepr-text-align-center {
  text-align: center;
}
.mepr-stripe-tax-notice,
.mepr-learndash-migrator-notice {
  padding: 12px;
}
.mepr-stripe-tax-notice-heading,
.mepr-learndash-migrator-notice-heading {
  vertical-align: top !important;
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: bold !important;
  line-height: 32px !important;
}
.mepr-stripe-tax-notice > div,
.mepr-learndash-migrator-notice > div,
.mepr-stripe-tax-notice-button-row,
.mepr-learndash-migrator-notice-button-row {
  display: flex;
  gap: 16px;
  align-items: center;
}
.mepr-stripe-tax-notice .mepr-stripe-tax-fine-print {
  margin-bottom: 17px;
}
.mepr-new-badge {
  background-color: #ed5a4c;
  color: #fff;
  font-size: 10px;
  font-weight: bold;
  line-height: 1.4;
  margin-left: 5px;
  padding: 2px 6px 3px;
  border-radius: 100px;
  position: relative;
  top: -1px;
}

#mepr_unauth_modern_paywall {
  padding-bottom: 0px;
  padding-top: 10px;
}

/* Form Slider */
.mepr-form label.switch{
  position: relative;
  display: inline-block;
  width: 36px;
  height: 21px;
  margin-right: 1em;
}

.mepr-form label.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.mepr-form label.switch .slider {
  width: auto;
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.mepr-form label.switch .slider.round {
  border-radius: 17px;
}

.mepr-form label.switch .slider.round:before {
  border-radius: 50%;
}

.mepr-form label.switch .slider:before {
  position: absolute;
  content: "";
  height: 15px;
  width: 15px;
  left: 2px;
  bottom: 2px;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.mepr-form label.switch input:checked + .slider {
  border: 1px solid #06429E;
}

.mepr-form label.switch input:checked + .slider:before {
  -webkit-transform: translateX(15px);
  -ms-transform: translateX(15px);
  transform: translateX(15px);
  background-color: #06429E;
}

.mepr-form label.switch input:disabled + .slider {
  opacity: 0.5;
}
/* End Form Slider */
#toplevel_page_memberpress-drm li.wp-first-item {
  display: none;
}

.mepr-locked #toplevel_page_memberpress-downloads, .mepr-locked .memberpress, .mepr-locked .memberpress-downloads { display: none; }

.mepr-notice-dismiss-24hour { color: #484848; border-width: 1px 1px 1px 18px; border-style: solid; border-color: #c3c3c4 #c3c3c4 #c3c3c4 #fc5034; border-radius: 8px; padding: 0;}
body.site-health-php .mepr-notice-dismiss-24hour{ display:none; }
.mepr-notice-dismiss-24hour .mepr-notice-wrapper { padding: 25px 80px; }
.mepr-notice-dismiss-24hour .mepr-notice-wrapper > * { margin: 0; padding: 0; }
.mepr-notice-dismiss-24hour .mepr-notice-wrapper:before { content: ""; background: url('../images/notice-icon-error.png') no-repeat; background-size: 100%; display: block; position: absolute; top: 25px; left: 20px; width: 42px; height: 42px; }
.mepr-notice-dismiss-24hour .mepr-notice-title { margin-bottom: 15px; font-size: 18px; font-weight: 600; }
.mepr-notice-dismiss-24hour .mepr-notice-desc { margin-bottom: 15px; font-size: 15px; line-height: 1.5; }

.mepr-notice-dismiss-24hour .mepr-notice-actions .button-primary { min-height: auto; padding: 6px 13px; background-color: #0085ba; border: 1px solid #006799; box-shadow: 0 2px 0 #006799; border-radius: 3px; color: #fff; text-shadow: -1px 0px 1px #006799, 0px 1px 1px #006799, 1px 0px 1px #006799, 0px -1px 1px #006799; font-size: 13px; font-weight: 600; line-height: 1.5; }

.mepr-notice-dismiss-24hour .notice-dismiss { top: 10px; right: 15px; padding: 10px; color: #484848; }
.mepr-notice-dismiss-24hour .notice-dismiss:before { content: "\f158"; font-size: 21px; }
.mepr-notice-dismiss-24hour .notice-dismiss:active::before,
.mepr-notice-dismiss-24hour .notice-dismiss:focus::before,
.mepr-notice-dismiss-24hour .notice-dismiss:hover::before { color: #fc5034; }

.mepr-notice-modal-active { overflow: hidden; }
.mepr-notice-modal-active #wpcontent { position: relative; }
.mepr-notice-modal-active #wpcontent #wpbody { filter: blur(3px); }
.mepr-notice-modal-active #wpcontent:before { content: ""; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(9, 29, 46, 0.2); z-index: 99; }

.mepr-notice-modal { display: none; position: fixed; top: 50%; left: 160px; width: calc(100% - 160px); z-index: 999; -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.mepr-notice-modal-active .mepr-notice-modal { display: block; }

.mepr-notice-modal .mepr-notice-modal-wrapper { position: relative; max-height: 95vh; overflow-y: auto; padding: 36px 72px 0; box-sizing: border-box; width: 73%; max-width: 904px; margin: 0 auto; background-color: #fff;   border-radius: 10px; text-align: center; box-shadow: 0px 10px 20px rgba(9, 29, 46, 0.1); }

.mepr-notice-modal .mepr-notice-modal-banner { display: block; max-width: 100%; height: auto; }

.mepr-notice-modal .mepr-notice-modal-content { max-width: 650px; margin: 0 auto; }

.mepr-notice-modal .mepr-notice-title { margin-bottom: 30px; font-size: 25px; font-weight: 600; line-height: 37px; }
.mepr-notice-modal .mepr-notice-title span { position: relative; display: inline-block; padding-left: 48px; color: #fc5034; }
.mepr-notice-modal .mepr-notice-title span:before { display: block; position: absolute; left: 0; content: ""; width: 37px; height: 37px; background: url('../images/notice-icon-error.png') no-repeat left center; background-size: 100%; }

.mepr-notice-modal .mepr-notice-desc { margin-bottom: 25px; font-size: 15px; line-height: 1.5; }
.mepr-notice-modal .mepr-notice-desc p { font-size: 15px; margin-bottom: 25px; }
.mepr-notice-modal .mepr-notice-desc ul { margin-bottom: 25px; }
.mepr-notice-modal .mepr-notice-desc ul li { position: relative; display: inline-block; padding: 0 0 0 32px; margin: 0 13px; }
.mepr-notice-modal .mepr-notice-desc ul li:before { display: block; position: absolute; top: 50%; left: 0; -webkit-transform: translateY(-50%); transform: translateY(-50%); content: ""; width: 17px; height: 18px; background: url('../images/bullet-cross-2x.png') no-repeat center; background-size: 100%; }

.mepr-notice-modal .button-primary { max-width: 100%; white-space: pre-wrap; background: linear-gradient(90deg, #CF3200 0%, #CF3200 0.01%, #F47B0D 100%); border: 0; border-radius: 100px; font-weight: 600; font-size: 16px; padding: 18px 32px; line-height: 1; -webkit-transition: all 0.3s linear 0s; transition: all 0.25s linear 0s; }
.mepr-notice-modal .button-primary:hover { background: linear-gradient(180deg, #CF3200 0%, #CF3200 0.01%, #F47B0D 100%); }

.mepr-notice-modal .button-secondary { margin-bottom: 15px; color: #FFF; max-width: 100%; white-space: pre-wrap; background: #135e96; border-color: #135e96; border: 0; border-radius: 100px; font-weight: 600; font-size: 16px; padding: 18px 32px; line-height: 1; -webkit-transition: all 0.3s linear 0s; transition: all 0.25s linear 0s; }
.mepr-notice-modal .button-secondary:hover { color: #FFF;  background: linear-gradient(90deg, #CF3200 0%, #CF3200 0.01%, #F47B0D 100%);  }
.mepr-drm-cta{ width: 280px; }

.mepr-notice-modal .mepr-notice-desc form { display: flex; align-items: flex; justify-content: center; align-items: center; }

.mepr-notice-modal .mepr-notice-desc form .field { margin: 0 15px; }
.mepr-notice-modal .mepr-notice-desc form input[type=text] { width: 280px; padding: 7px 13px; line-height: 1; }
.mepr-notice-modal .mepr-notice-desc form input[type=button] { cursor: pointer; padding: 8px 13px; color: #06429E; background-color: #f3f6fb; border-color: #06429E; border-radius: 3px; font-weight: 600; font-size: 14px; line-height: 1; }

.mepr-notice-modal .mepr-notice-desc .mepr-key-error { padding: 0 20px; margin-top: 25px; background-color: #ffd0cb; border-width: 1px 1px 1px 10px; border-style: solid; border-color: #bdbdbd #bdbdbd #bdbdbd #fb5034; border-radius: 8px; font-weight: 600; font-size: 15px; }
.mepr-notice-modal .mepr-notice-desc .mepr-key-error > span { display: inline-block; position: relative; padding: 10px 10px 10px 42px; }
.mepr-notice-modal .mepr-notice-desc .mepr-key-error > span:before { content: ""; background: url('../images/notice-icon-error.png') no-repeat; background-size: 100% auto; display: block; position: absolute; top: 50%; left: 0; -webkit-transform: translateY(-50%); transform: translateY(-50%); width: 24px; height: 24px; }

.mepr-notice-modal .mepr-notice-desc .mepr-key-success { font-weight: 600; font-size: 15px; }
.mepr-notice-modal .mepr-notice-desc .mepr-key-success > span { display: inline-block; position: relative; padding: 10px 10px 10px 47px; }
.mepr-notice-modal .mepr-notice-desc .mepr-key-success > span:before { content: ""; background: url('../images/notice-icon-success.gif') no-repeat; background-size: 100% auto; display: block; position: absolute; top: 50%; left: 0; -webkit-transform: translateY(-50%); transform: translateY(-50%); width: 24px; height: 24px; }

.mepr-notice-modal-close {
  position: absolute;
  width: 24px;
  height: 24px;
  right: 20px;
  top: 20px;
  background: url("../images/icon-close.png") left top no-repeat;
  background-size: 100%;
}

@media (min-width: 680px) {
  .mepr-notice-modal .button-reactivate-fee { margin-right: 20px; }
}

@media (max-width: 782px) {
  .mepr-notice-modal { left: 0; width: calc(100%); }

  .mepr-notice-modal .mepr-notice-modal-wrapper { width: 95%; padding-top: 32px; padding-right: 32px; padding-left: 32px; }
}
@media (min-width: 783px) and (max-width: 1199px) {
  .mepr-notice-modal .mepr-notice-modal-wrapper { padding-top: 52px; padding-right: 52px; padding-left: 52px; }
}
@media (max-width: 960px) {
  .mepr-notice-modal .mepr-notice-modal-content { max-width: 100%; }

  .mepr-notice-modal .mepr-notice-title { font-size: 21px; }
}
@media (min-width: 783px) and  (max-width: 960px) {
  .mepr-notice-modal { left: 36px; width: calc(100% - 36px); }

  .mepr-notice-modal .mepr-notice-modal-wrapper { width: 90%; }
}
@media (min-width: 961px) and (max-width: 1199px) {
  .mepr-notice-modal .mepr-notice-modal-wrapper { width: 90%; }

  .mepr-notice-modal .mepr-notice-title { font-size: 23px; }
}
