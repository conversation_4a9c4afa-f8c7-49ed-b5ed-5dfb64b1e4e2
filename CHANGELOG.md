# Changelog

## [6.6](https://github.com/UncannyOwl/Automator/tree/6.6) [2025-06-05]


**New App Integrations:**

* Anthropic [#5818](https://github.com/UncannyOwl/Automator/issues/5818)

* Cohere [#5819](https://github.com/UncannyOwl/Automator/issues/5819)

* DeepSeek [#5821](https://github.com/UncannyOwl/Automator/issues/5821)

* Google Gemini [#5823](https://github.com/UncannyOwl/Automator/issues/5823)

* Mistral AI [#5827](https://github.com/UncannyOwl/Automator/issues/5827)

* Perplexity [#5829](https://github.com/UncannyOwl/Automator/issues/5829)

* xAI [#5825](https://github.com/UncannyOwl/Automator/issues/5825)


**New Plugin Integrations:**

* FluentCommunity [#5668](https://github.com/UncannyOwl/Automator/issues/5668)

* Mailster [#5405](https://github.com/UncannyOwl/Automator/issues/5405)


**New Triggers:**

* ARMember -  A user is added to a membership plan [#5769](https://github.com/UncannyOwl/Automator/issues/5769)

* Events Manager - A user publishes a new event [#5366](https://github.com/UncannyOwl/Automator/issues/5366)

* FluentCommunity - A user completes a course [#5704](https://github.com/UncannyOwl/Automator/issues/5704)

* FluentCommunity - A user completes a lesson [#5714](https://github.com/UncannyOwl/Automator/issues/5714)

* FluentCommunity - A user is enrolled in a course [#5702](https://github.com/UncannyOwl/Automator/issues/5702)

* FluentCommunity - A user joins a space [#5700](https://github.com/UncannyOwl/Automator/issues/5700)

* FluentCommunity - A user posts to a space [#5709](https://github.com/UncannyOwl/Automator/issues/5709)

* Mailster - A new subscriber is added to a Mailster list [#5406](https://github.com/UncannyOwl/Automator/issues/5406)


**New Actions:**

* Anthropic - Use a prompt to generate a text response with a Claude model [#5817](https://github.com/UncannyOwl/Automator/issues/5817)

* Cohere - Use a prompt to generate a text response with a Cohere model [#5820](https://github.com/UncannyOwl/Automator/issues/5820)

* DeepSeek - Use a prompt to generate a text response with a DeepSeek model [#5822](https://github.com/UncannyOwl/Automator/issues/5822)

* FluentCommunity - Add the user to a space [#5707](https://github.com/UncannyOwl/Automator/issues/5707)

* FluentCommunity - Enroll the user in a course [#5705](https://github.com/UncannyOwl/Automator/issues/5705)

* Google Gemini - Use a prompt to generate a text response with a Gemini model [#5824](https://github.com/UncannyOwl/Automator/issues/5824)

* Mailster - Add a subscriber to a Mailster list [#5407](https://github.com/UncannyOwl/Automator/issues/5407)

* Mistral AI - Use a prompt to generate a text response with a Le Chat model [#5828](https://github.com/UncannyOwl/Automator/issues/5828)

* Newsletter - Add a subscriber to a list [#5030](https://github.com/UncannyOwl/Automator/issues/5030)

* OpenAI - Use a prompt to generate an image [#5797](https://github.com/UncannyOwl/Automator/issues/5797)

* Perplexity - Use a prompt to generate a text response with a Perplexity model [#5830](https://github.com/UncannyOwl/Automator/issues/5830)

* xAI - Use a prompt to generate a text response with a Grok model [#5826](https://github.com/UncannyOwl/Automator/issues/5826)


**Added:**

* Automator banners - Added click tracking [#5179](https://github.com/UncannyOwl/Automator/issues/5179)

* WordPress  - A user's post receives a comment AND A user submits a comment on a post - Added Akismet support [#5841](https://github.com/UncannyOwl/Automator/issues/5841)


**Updated:**

* Gravity Forms - A form is submitted - "Can login user?" field in the user selector added [#5799](https://github.com/UncannyOwl/Automator/issues/5799)

* OpenAI - Add Dall-E support [#5796](https://github.com/UncannyOwl/Automator/issues/5796)

* WPForms - Separate name's smart tag into multiple tokens [#5514](https://github.com/UncannyOwl/Automator/issues/5514)


**Fixed:**

* Action token field - Unable to parse repeater field [#5765](https://github.com/UncannyOwl/Automator/issues/5765)

* Addons - Pro basic licenses is not showing link on Plus list [#5754](https://github.com/UncannyOwl/Automator/issues/5754)

* Discord - Send a message to a channel - Allow line breaks [#5751](https://github.com/UncannyOwl/Automator/issues/5751)

* Formidable - A form is submitted - Repeater token not returning the correct value [#5509](https://github.com/UncannyOwl/Automator/issues/5509)

* Mailchimp - Settings page connection issue "Something went wrong while connecting to application. Please try again." [#5786](https://github.com/UncannyOwl/Automator/issues/5786)

* Tin Canny - v5.0+ of Tin Canny throwing PHP error [#5803](https://github.com/UncannyOwl/Automator/issues/5803)


**Security Fix:**

* Additional security check when the App disconnected [#5746](https://github.com/UncannyOwl/Automator/issues/5746)


**Under the hood:**

* Frontend asset architecture and process improvements & optimizations [#5632](https://github.com/UncannyOwl/Automator/issues/5632)

* Improve support for third-party App settings pages [#5638](https://github.com/UncannyOwl/Automator/issues/5638)

* uap_options - Added a new `type` column to store the type of data instead of a separate row, which reduces the number of rows stored to half [#5793](https://github.com/UncannyOwl/Automator/issues/5793)


---

**Closed Issues with a PR but no labels:**

* Build AI integration framework [#5812](https://github.com/UncannyOwl/Automator/issues/5812)

* CSVUploading doesn't show anything in file [#5835](https://github.com/UncannyOwl/Automator/issues/5835)

* Fix the recipe live toggle modal behaviour [#5833](https://github.com/UncannyOwl/Automator/issues/5833)

* Frontend - Validate & Fix Various Issues [#5832](https://github.com/UncannyOwl/Automator/issues/5832)

* New icon - Mailster [#5653](https://github.com/UncannyOwl/Automator/issues/5653)

* Shift+Clicking a token group opens N number of popups [#5834](https://github.com/UncannyOwl/Automator/issues/5834)


**Closed PRs without linked Issue:**

* FC [#5847](https://github.com/UncannyOwl/Automator/pull/5847)

* Release/******* [#5782](https://github.com/UncannyOwl/Automator/pull/5782)


**Closed Issues without PR:**

* 1.1.1 Define the structure of the global integrations object [#5590](https://github.com/UncannyOwl/Automator/issues/5590)

* AI integration icons [#5800](https://github.com/UncannyOwl/Automator/issues/5800)

* Add an event to a Google Calendar set guest attributes [#5699](https://github.com/UncannyOwl/Automator/issues/5699)

* Automator Core - Refactor the "A recipe completes with errors" trigger [#5749](https://github.com/UncannyOwl/Automator/issues/5749)

* Brevo Integration – Invalid API key [#5726](https://github.com/UncannyOwl/Automator/issues/5726)

* Check ActiveCampaign webhooks API to see if there is anything we need to do [#5792](https://github.com/UncannyOwl/Automator/issues/5792)

* Convert filters into post types [#5317](https://github.com/UncannyOwl/Automator/issues/5317)

* ConvertKit - Shows as connected on a blank/brand new site [#5468](https://github.com/UncannyOwl/Automator/issues/5468)

* Duplicate recipe - Duplicate recipe breaks "Calculation" tokens [#3687](https://github.com/UncannyOwl/Automator/issues/3687)

* EDD Digital Downloads - Research available do_actions/triggers [#2568](https://github.com/UncannyOwl/Automator/issues/2568)

* Error logs are unnecessarily generated [#4385](https://github.com/UncannyOwl/Automator/issues/4385)

* Finalize the actions of the conditions endpoint [#4293](https://github.com/UncannyOwl/Automator/issues/4293)

* Fix - Automatorplugin.com standalone social connects not working [#5842](https://github.com/UncannyOwl/Automator/issues/5842)

* Fix - Salesforce - Connection page - Error fetching access token [#5785](https://github.com/UncannyOwl/Automator/issues/5785)

* Fix - WP Download Manager Triggers not firing [#5779](https://github.com/UncannyOwl/Automator/issues/5779)

* FluentCommunity - A user comments on a post in a space [#5711](https://github.com/UncannyOwl/Automator/issues/5711)

* FluentCommunity - A user is unenrolled from a course [#5703](https://github.com/UncannyOwl/Automator/issues/5703)

* FluentCommunity - A user leaves a space [#5701](https://github.com/UncannyOwl/Automator/issues/5701)

* FluentCommunity - A user reacts to a post in a space [#5710](https://github.com/UncannyOwl/Automator/issues/5710)

* FluentCommunity - A user submits a request to join a space [#5708](https://github.com/UncannyOwl/Automator/issues/5708)

* FluentCommunity - Add a post to a space [#5712](https://github.com/UncannyOwl/Automator/issues/5712)

* FluentCommunity - Mark a course complete for the user [#5715](https://github.com/UncannyOwl/Automator/issues/5715)

* FluentCommunity - Mark a lesson complete for the user [#5713](https://github.com/UncannyOwl/Automator/issues/5713)

* FluentCommunity - Remove the user from a space [#5706](https://github.com/UncannyOwl/Automator/issues/5706)

* FluentCommunity - Unenroll the user from a course [#5718](https://github.com/UncannyOwl/Automator/issues/5718)

* Freaking test task [#5816](https://github.com/UncannyOwl/Automator/issues/5816)

* Get - LearnDash - Get users's enrolled courses [#4552](https://github.com/UncannyOwl/Automator/issues/4552)

* Google Sheets - Create or Update a Row [#4844](https://github.com/UncannyOwl/Automator/issues/4844)

* Google calendar - Allow users to create a custom payload via apply filters [#5750](https://github.com/UncannyOwl/Automator/issues/5750)

* Investigate - Paid Membership Pro - A user's subscription to Membership expires. [#5836](https://github.com/UncannyOwl/Automator/issues/5836)

* Issue - Formatter - Convert date into format - Unix value in the input not recognized [#5768](https://github.com/UncannyOwl/Automator/issues/5768)

* LearnDash - A user has completed X% of a course [#5367](https://github.com/UncannyOwl/Automator/issues/5367)

* LearnPress - Mark a lesson complete for the user - PHP Fatal [#5416](https://github.com/UncannyOwl/Automator/issues/5416)

* LinkedIn API Deprecation [#5770](https://github.com/UncannyOwl/Automator/issues/5770)

* Logs tables - Track the recipe run number relative to the user's total run number [#3890](https://github.com/UncannyOwl/Automator/issues/3890)

* Memberpress - A user purchases a recurring subscription trigger is not firing anymore since last update [#5778](https://github.com/UncannyOwl/Automator/issues/5778)

* New integration icon - FluentCommunity [#5752](https://github.com/UncannyOwl/Automator/issues/5752)

* Shorten a URL [#1680](https://github.com/UncannyOwl/Automator/issues/1680)

* SureCart - A user's order status is changed to confirmed [#2526](https://github.com/UncannyOwl/Automator/issues/2526)

* Thrive Apprentice - Content is unlocked for a user [#3054](https://github.com/UncannyOwl/Automator/issues/3054)

* Thrive Quiz Builder - Migrated to newer framework and tokens normalized [#5635](https://github.com/UncannyOwl/Automator/issues/5635)

* Translations .pot file missing sentences - Needs to regenerate the pot file [#5787](https://github.com/UncannyOwl/Automator/issues/5787)

* UserFeedback - A visitor skipped a survey [#4532](https://github.com/UncannyOwl/Automator/issues/4532)

* WPForms - Split token checks throwing warnings [#5682](https://github.com/UncannyOwl/Automator/issues/5682)

* WordPress - Comment and Commenter tokens in WordPress triggers [#3551](https://github.com/UncannyOwl/Automator/issues/3551)

* WordPress Core - Comment triggers - Add Akismet support [#5436](https://github.com/UncannyOwl/Automator/issues/5436)


**Closed Pull Requests:**

* Bump @babel/helpers from 7.24.7 to 7.27.1 [#5758](https://github.com/UncannyOwl/Automator/pull/5758)

* Bump @babel/runtime from 7.24.7 to 7.27.1 [#5757](https://github.com/UncannyOwl/Automator/pull/5757)

* Bump axios from 1.7.2 to 1.9.0 [#5759](https://github.com/UncannyOwl/Automator/pull/5759)

* Bump tar-fs from 3.0.8 to 3.0.9 in /src/assets [#5808](https://github.com/UncannyOwl/Automator/pull/5808)

* Convert BuddyPress integration to framework 3 [#5837](https://github.com/UncannyOwl/Automator/pull/5837)

* Missing $pm var and PR revert [#5781](https://github.com/UncannyOwl/Automator/pull/5781)

* Resolve WordPress 6.7+ translation warnings with new deferred event processing [#5784](https://github.com/UncannyOwl/Automator/pull/5784)

* WPForms - Split token checks throwing warnings [#5684](https://github.com/UncannyOwl/Automator/pull/5684)

* ⁃ Improve support for third-party app settings pages [#5661](https://github.com/UncannyOwl/Automator/pull/5661)


**Merged Pull Requests:**

* 5366 new trigger events manager a user publish a new event [#5446](https://github.com/UncannyOwl/Automator/pull/5446)

* 5405 mailster integration [#5725](https://github.com/UncannyOwl/Automator/pull/5725)

* Add docs support [#5767](https://github.com/UncannyOwl/Automator/pull/5767)

* Add usage tracking to review request admin notices [#5386](https://github.com/UncannyOwl/Automator/pull/5386)

* Ajay/migration suggestions [#5791](https://github.com/UncannyOwl/Automator/pull/5791)

* Armember add user to membership [#5794](https://github.com/UncannyOwl/Automator/pull/5794)

* Check capabilities on dicsonnect [#5747](https://github.com/UncannyOwl/Automator/pull/5747)

* Fix - Add nonce validation Discord disconnect ( Account and Server Bot ) [#5756](https://github.com/UncannyOwl/Automator/pull/5756)

* Fix - AddOns page when connected with Pro Basic - not showing link on Plus list [#5755](https://github.com/UncannyOwl/Automator/pull/5755)

* Fix assets orchestrator on Windows [#5762](https://github.com/UncannyOwl/Automator/pull/5762)

* Frontend - Validate & Fix Various Issues [#5840](https://github.com/UncannyOwl/Automator/pull/5840)

* Frontend asset architecture and process improvements [#5667](https://github.com/UncannyOwl/Automator/pull/5667)

* Made it compatible with v5.0 [#5805](https://github.com/UncannyOwl/Automator/pull/5805)

* Moving uap_options to single column for type [#5790](https://github.com/UncannyOwl/Automator/pull/5790)

* OpenAI - New Action - Use a model to generate an image using gpt-image-1 [#5776](https://github.com/UncannyOwl/Automator/pull/5776)

* Recognize whether the dialog was closed because it was cancelled [#5846](https://github.com/UncannyOwl/Automator/pull/5846)

* Update anon-gf-subform.php [#5811](https://github.com/UncannyOwl/Automator/pull/5811)

* Update class-automator-send-webhook-fields.php [#5780](https://github.com/UncannyOwl/Automator/pull/5780)

* WPForms - names token smart tag [#5521](https://github.com/UncannyOwl/Automator/pull/5521)

* migrate v3 integrations [#5839](https://github.com/UncannyOwl/Automator/pull/5839)

* new integration fluentcommunity [#5777](https://github.com/UncannyOwl/Automator/pull/5777)

* newletter action add contact to list [#5488](https://github.com/UncannyOwl/Automator/pull/5488)

* repeater value fixes [#5569](https://github.com/UncannyOwl/Automator/pull/5569)

* ⁃ (Joseph's) Disconnect nonce & capability checks [#5760](https://github.com/UncannyOwl/Automator/pull/5760)

* ⁃ Fix - Action token field - Unable to parse repeater field. [#5766](https://github.com/UncannyOwl/Automator/pull/5766)

* ⁃ Fix - Discord - Allow line breaks [#5753](https://github.com/UncannyOwl/Automator/pull/5753)

* ⁃ Fix - MailChimp - Settings page connection issue "Something went wrong while connecting to application. Please try again." [#5788](https://github.com/UncannyOwl/Automator/pull/5788)

* ⁃ Improve support for third-party app settings pages [#5775](https://github.com/UncannyOwl/Automator/pull/5775)

* ⁃ WordPress Core - Comment triggers - Add Akismet support [#5441](https://github.com/UncannyOwl/Automator/pull/5441)

* 🎉✨ Lucky 7 - launch seven new frontier AI integrations & the delicious AI framework  refactored OpenAI text generation action to use the new framework 🤖 [#5806](https://github.com/UncannyOwl/Automator/pull/5806)


