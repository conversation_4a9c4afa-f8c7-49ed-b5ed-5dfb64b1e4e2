<svg xmlns="http://www.w3.org/2000/svg" width="206" height="45.894" viewBox="0 0 206 45.894"><defs><style>.a{fill:#1c3141;}.b{fill:#0075ff;}.c{fill:#ffc300;}.d{fill:#202a44;}</style></defs><g transform="translate(0)"><g transform="translate(0)"><path class="a" d="M28.267,45.144v-.278a5.333,5.333,0,0,1-1.839,1.172,6.243,6.243,0,0,1-2.228.389,7.97,7.97,0,0,1-6-2.552,8.745,8.745,0,0,1-2.435-6.257A8.708,8.708,0,0,1,18.2,31.362a7.95,7.95,0,0,1,6-2.552,6.217,6.217,0,0,1,2.228.389,5.559,5.559,0,0,1,1.839,1.159v-.3a.754.754,0,0,1,.784-.784h4.255a.754.754,0,0,1,.784.784V45.144a.788.788,0,0,1-.227.563.721.721,0,0,1-.538.233H29.083a.793.793,0,0,1-.57-.233A.752.752,0,0,1,28.267,45.144Zm-6.652-7.532a3.323,3.323,0,0,0,.978,2.422,3.331,3.331,0,0,0,4.767,0,3.489,3.489,0,0,0,0-4.845,3.331,3.331,0,0,0-4.767,0A3.359,3.359,0,0,0,21.615,37.612Z" transform="translate(-15.76 -21.737)"/><path class="a" d="M55.378,41.5a2.2,2.2,0,0,0,1.729-.654,2.812,2.812,0,0,0,.589-1.924V30.226a.629.629,0,0,1,.253-.492.851.851,0,0,1,.563-.214h4.21a.851.851,0,0,1,.563.214.629.629,0,0,1,.253.492v9.469a6.45,6.45,0,0,1-2.209,5.026,8.548,8.548,0,0,1-5.913,1.969,8.786,8.786,0,0,1-6.01-1.988,6.363,6.363,0,0,1-2.267-5.007V30.226a.634.634,0,0,1,.24-.492.823.823,0,0,1,.557-.214h4.21a.867.867,0,0,1,.57.214.618.618,0,0,1,.259.492v8.685a2.687,2.687,0,0,0,.628,1.911A2.29,2.29,0,0,0,55.378,41.5Z" transform="translate(-26.816 -21.988)"/><path class="a" d="M82.591,44.235A6.4,6.4,0,0,1,78.219,42.8a5,5,0,0,1-1.632-3.964V32.059H74.864a.721.721,0,0,1-.538-.233.807.807,0,0,1-.227-.576v-1.2a2.867,2.867,0,0,1,.1-.861,1.621,1.621,0,0,1,.369-.628l4.145-4.6a3.334,3.334,0,0,1,.887-.751,2.048,2.048,0,0,1,.913-.175h1.114a.773.773,0,0,1,.557.227.762.762,0,0,1,.227.557V27.24h3.659a.788.788,0,0,1,.563.227.743.743,0,0,1,.233.557v3.225a.786.786,0,0,1-.233.576.768.768,0,0,1-.563.233H82.416v5.557a1.363,1.363,0,0,0,.33.965,1.171,1.171,0,0,0,.894.35,2.088,2.088,0,0,0,.706-.136,5.349,5.349,0,0,0,.861-.415,1.16,1.16,0,0,1,.823-.2,1.142,1.142,0,0,1,.635.5l1.47,2.209a1.221,1.221,0,0,1,.233.751.961.961,0,0,1-.3.654,6.094,6.094,0,0,1-2.358,1.425A9.453,9.453,0,0,1,82.591,44.235Z" transform="translate(-36.315 -19.701)"/><path class="a" d="M106.383,30.36a2.688,2.688,0,0,0-1.956.745,2.65,2.65,0,0,0-.764,1.995v8.562a.754.754,0,0,1-.784.784H98.624a.754.754,0,0,1-.784-.784V19.654a.754.754,0,0,1,.784-.784h4.255a.754.754,0,0,1,.784.784v8.109a5.288,5.288,0,0,1,2.027-1.833,6.167,6.167,0,0,1,2.824-.628,5.956,5.956,0,0,1,4.767,1.872,8.063,8.063,0,0,1,1.619,5.44v9.042a.754.754,0,0,1-.784.784h-4.255a.754.754,0,0,1-.784-.784V33.093a2.56,2.56,0,0,0-2.694-2.733Z" transform="translate(-44.679 -18.235)"/><path class="a" d="M126.37,37.608a8.316,8.316,0,0,1,2.694-6.237,9.557,9.557,0,0,1,13.044,0,8.566,8.566,0,0,1,0,12.474,9.557,9.557,0,0,1-13.044,0A8.316,8.316,0,0,1,126.37,37.608Zm5.855,0a3.323,3.323,0,0,0,.978,2.422,3.331,3.331,0,0,0,4.767,0,3.489,3.489,0,0,0,0-4.845,3.331,3.331,0,0,0-4.767,0A3.333,3.333,0,0,0,132.225,37.608Z" transform="translate(-54.731 -21.734)"/><path class="a" d="M158.1,29.386h3.038a1.521,1.521,0,0,1,.855.233,1.62,1.62,0,0,1,.57.706l.466,1.049a7.605,7.605,0,0,1,1.9-1.677,3.722,3.722,0,0,1,1.917-.576,2.316,2.316,0,0,1,1.541.447,1.34,1.34,0,0,1,.414,1.276l-.466,3.8a1.692,1.692,0,0,1-.421.984,1.208,1.208,0,0,1-.894.33,2.079,2.079,0,0,1-.389-.039c-.136-.026-.337-.078-.609-.149-.3-.091-.531-.155-.68-.181a2.6,2.6,0,0,0-.447-.039,1.774,1.774,0,0,0-1.282.466,1.694,1.694,0,0,0-.486,1.282v8.044a.655.655,0,0,1-.24.492.823.823,0,0,1-.557.214h-4.242a.761.761,0,0,1-.563-.22.742.742,0,0,1-.22-.551V30.169a.773.773,0,0,1,.227-.557A.723.723,0,0,1,158.1,29.386Z" transform="translate(-65.632 -21.847)"/><path class="b" d="M182.979,21.064a2.985,2.985,0,0,1-.965,2.215,3.292,3.292,0,0,1-4.592,0,2.968,2.968,0,0,1-.972-2.215,3.005,3.005,0,0,1,.972-2.228,3.26,3.26,0,0,1,4.592,0A3,3,0,0,1,182.979,21.064Z" transform="translate(-72.376 -17.89)"/><path class="a" d="M203.161,34.1l-6.606,7.137h6.574a.753.753,0,0,1,.771.771v3.446a.753.753,0,0,1-.771.771H189.47a.793.793,0,0,1-.57-.233.749.749,0,0,1-.24-.563v-2.83a1.39,1.39,0,0,1,.078-.5,1.274,1.274,0,0,1,.279-.408l6.742-7.15h-6.043a.8.8,0,0,1-.8-.8V30.38a.819.819,0,0,1,.8-.81h13.005a.774.774,0,0,1,.563.24.793.793,0,0,1,.233.57V33.2a1.39,1.39,0,0,1-.078.5A1.411,1.411,0,0,1,203.161,34.1Z" transform="translate(-76.677 -22.005)"/><path class="a" d="M229.844,43.211a6.862,6.862,0,0,1-2.986,2.332,10.685,10.685,0,0,1-4.352.874,9.019,9.019,0,0,1-6.548-2.481,8.344,8.344,0,0,1-2.558-6.25,8.833,8.833,0,0,1,2.4-6.4,8.351,8.351,0,0,1,6.3-2.487,8.182,8.182,0,0,1,6.1,2.383,8.025,8.025,0,0,1,2.345,5.861V38a1.486,1.486,0,0,1-.233.874.776.776,0,0,1-.674.33H219.248a2.941,2.941,0,0,0,1.14,1.833,3.577,3.577,0,0,0,2.189.674,3.986,3.986,0,0,0,1.587-.311,4.5,4.5,0,0,0,1.4-.984.929.929,0,0,1,1.282-.142l2.817,1.846a.664.664,0,0,1,.337.5A.777.777,0,0,1,229.844,43.211Zm-10.466-7.759H224.9a2.545,2.545,0,0,0-.933-1.58,2.745,2.745,0,0,0-1.775-.609,2.863,2.863,0,0,0-1.794.609A2.767,2.767,0,0,0,219.378,35.452Z" transform="translate(-85.394 -21.734)"/><path class="c" d="M249.241,48.2a3.742,3.742,0,1,1-3.757-3.737,3.748,3.748,0,0,1,3.757,3.737Z" transform="translate(-95.386 -27.251)"/><path class="a" d="M264.563,33.858a2.687,2.687,0,0,0-1.956.745,2.65,2.65,0,0,0-.764,1.995V45.16a.754.754,0,0,1-.784.784H256.8a.754.754,0,0,1-.784-.784v-15.1a.754.754,0,0,1,.784-.784h4.255a.754.754,0,0,1,.784.784v1.2a5.288,5.288,0,0,1,2.027-1.833,6.167,6.167,0,0,1,2.824-.628,5.956,5.956,0,0,1,4.767,1.872,8.063,8.063,0,0,1,1.619,5.44v9.042a.754.754,0,0,1-.784.784h-4.255a.754.754,0,0,1-.784-.784V36.591a2.56,2.56,0,0,0-2.694-2.733Z" transform="translate(-100.41 -21.734)"/><path class="a" d="M301.044,43.211a6.861,6.861,0,0,1-2.986,2.332,10.685,10.685,0,0,1-4.352.874,9.019,9.019,0,0,1-6.548-2.481,8.345,8.345,0,0,1-2.558-6.25,8.833,8.833,0,0,1,2.4-6.4,8.351,8.351,0,0,1,6.3-2.487,8.182,8.182,0,0,1,6.1,2.383,8.025,8.025,0,0,1,2.345,5.861V38a1.486,1.486,0,0,1-.233.874.776.776,0,0,1-.674.33H290.449a2.94,2.94,0,0,0,1.14,1.833,3.577,3.577,0,0,0,2.189.674,3.986,3.986,0,0,0,1.587-.311,4.5,4.5,0,0,0,1.4-.984.93.93,0,0,1,1.282-.142l2.817,1.846a.664.664,0,0,1,.337.5A.777.777,0,0,1,301.044,43.211Zm-10.466-7.759H296.1a2.544,2.544,0,0,0-.933-1.58,2.745,2.745,0,0,0-1.775-.609,2.862,2.862,0,0,0-1.794.609A2.767,2.767,0,0,0,290.578,35.452Z" transform="translate(-110.48 -21.734)"/><path class="a" d="M320.281,44.235a6.4,6.4,0,0,1-4.372-1.431,5,5,0,0,1-1.632-3.964V32.059h-1.723a.721.721,0,0,1-.538-.233.807.807,0,0,1-.227-.576v-1.2a2.866,2.866,0,0,1,.1-.861,1.622,1.622,0,0,1,.369-.628l4.145-4.6a3.335,3.335,0,0,1,.887-.751,2.049,2.049,0,0,1,.913-.175h1.114a.773.773,0,0,1,.557.227.762.762,0,0,1,.227.557V27.24h3.659a.788.788,0,0,1,.563.227.743.743,0,0,1,.233.557v3.225a.787.787,0,0,1-.233.576.768.768,0,0,1-.563.233h-3.659v5.557a1.363,1.363,0,0,0,.33.965,1.171,1.171,0,0,0,.894.35,2.088,2.088,0,0,0,.706-.136,5.352,5.352,0,0,0,.861-.415,1.159,1.159,0,0,1,.823-.2,1.142,1.142,0,0,1,.635.5l1.47,2.209a1.221,1.221,0,0,1,.233.751.961.961,0,0,1-.3.654,6.094,6.094,0,0,1-2.357,1.425A9.453,9.453,0,0,1,320.281,44.235Z" transform="translate(-120.06 -19.701)"/></g><path class="d" d="M184.59,81.853c-.032.117-.065.168-.188.168h-1.561c-.117,0-.149-.052-.188-.168l-4.139-11.535c-.032-.117.019-.168.136-.168h1.321c.117,0,.168.052.2.168l3.446,10.007,3.446-10.007c.032-.117.084-.168.2-.168h1.321c.117,0,.168.052.136.168Z" transform="translate(-73.1 -36.303)"/><path class="d" d="M196.75,73.68c0-.117.052-.168.168-.168h.984c.117,0,.168.052.168.168v8.174c0,.117-.052.168-.168.168h-.984c-.117,0-.168-.052-.168-.168Zm-.24-3.361c0-.117.052-.168.168-.168h1.457c.117,0,.168.052.168.168v1.457c0,.117-.052.168-.168.168h-1.457c-.117,0-.168-.052-.168-.168Z" transform="translate(-79.443 -36.303)"/><path class="d" d="M205.172,83.927c-1.982,0-3.582-1.172-3.582-2.766a.151.151,0,0,1,.168-.168h1.088c.117,0,.155.052.168.168.136.984.933,1.509,2.2,1.509,1.133,0,1.969-.356,1.969-1.237s-.745-1.185-2.474-1.457c-2.034-.324-2.9-.984-2.9-2.474,0-1.561,1.425-2.422,3.219-2.422,1.9,0,3.07.965,3.271,2.442a.151.151,0,0,1-.168.168h-.952c-.117,0-.136-.052-.168-.168a1.954,1.954,0,0,0-2.034-1.185c-1.088,0-1.846.376-1.846,1.133,0,.829.68,1.12,2.39,1.425,1.626.291,3.051.764,3.051,2.377.007,1.71-1.418,2.655-3.4,2.655" transform="translate(-81.233 -38.04)"/><path class="d" d="M218.4,79.649c-1.2,0-2.137.44-2.137,1.49,0,.984.68,1.529,1.813,1.529a2.505,2.505,0,0,0,2.63-2.649,6.94,6.94,0,0,0-2.306-.369m-3.07-1.878c-.071,0-.117-.032-.117-.1a.6.6,0,0,1,.019-.168,3.285,3.285,0,0,1,3.426-2.429c1.969,0,3.374,1.185,3.374,2.966v4.087a2.473,2.473,0,0,0,.285,1.457.153.153,0,0,1,.032.084c0,.065-.052.084-.136.084h-1.341c-.117,0-.168-.052-.168-.168V82.7a3.4,3.4,0,0,1-2.779,1.224,2.8,2.8,0,0,1-3.051-2.694c0-1.885,1.529-2.83,3.446-2.83a8.671,8.671,0,0,1,2.39.376V78.3a1.822,1.822,0,0,0-2.053-1.969,1.987,1.987,0,0,0-2,1.289.217.217,0,0,1-.24.155h-1.088Z" transform="translate(-85.916 -38.036)"/><path class="d" d="M239.908,69.89c2.558,0,4.022,1.58,4.074,3.31,0,.117-.052.168-.168.168h-1.088c-.117,0-.168-.052-.168-.168-.065-1.172-1.224-1.949-2.649-1.949-1.444,0-2.558.641-2.558,1.749,0,1.269,1.36,1.749,3.141,2.222,2.17.576,3.886,1.373,3.886,3.258,0,2.442-1.813,3.627-4.378,3.627-2.461,0-4.378-1.529-4.378-3.763,0-.117.052-.168.168-.168h1.088c.117,0,.168.052.168.168.052,1.373,1.185,2.409,2.953,2.409,1.833,0,2.953-.68,2.953-2.189,0-1.185-1.457-1.645-3.1-2.073-2.325-.6-3.918-1.425-3.918-3.426-.013-2.092,1.852-3.174,3.977-3.174" transform="translate(-93.223 -36.211)"/><path class="d" d="M255.756,82.674a3.183,3.183,0,1,0-2.934-3.174,2.9,2.9,0,0,0,2.934,3.174m0-7.6a4.431,4.431,0,1,1-4.326,4.43,4.2,4.2,0,0,1,4.326-4.43" transform="translate(-98.793 -38.036)"/><path class="d" d="M267.79,70.318c0-.117.052-.168.168-.168h.984c.117,0,.168.052.168.168V81.853c0,.117-.052.168-.168.168h-.984c-.117,0-.168-.052-.168-.168Z" transform="translate(-104.557 -36.303)"/><path class="d" d="M280.4,83.682c0,.117-.052.168-.168.168h-.984c-.117,0-.168-.052-.168-.168v-.965a3.554,3.554,0,0,1-2.817,1.308,2.785,2.785,0,0,1-2.953-3.089V75.508c0-.117.052-.168.168-.168h.984c.117,0,.168.052.168.168v5.227a1.776,1.776,0,0,0,1.936,2.034c1.425,0,2.513-1.036,2.513-3.089V75.508c0-.117.052-.168.168-.168h.984c.117,0,.168.052.168.168Z" transform="translate(-106.502 -38.131)"/><path class="d" d="M293.016,73.92c.117,0,.168.052.168.168V75c0,.117-.052.168-.168.168H290.2V79.9c0,.952.44,1.444,1.341,1.444a4.272,4.272,0,0,0,1.613-.253.264.264,0,0,1,.117-.032c.084,0,.117.071.117.168v.8c0,.155,0,.24-.136.291a5.124,5.124,0,0,1-1.814.285,2.364,2.364,0,0,1-2.558-2.578V75.177h-1.509c-.117,0-.168-.052-.168-.168v-.92c0-.117.052-.168.168-.168h1.509V71.478c0-.117.052-.168.168-.168h.984c.117,0,.168.052.168.168V73.92Z" transform="translate(-111.396 -36.711)"/><path class="d" d="M299.633,73.68c0-.117.052-.168.168-.168h.984c.117,0,.168.052.168.168v8.174c0,.117-.052.168-.168.168H299.8c-.117,0-.168-.052-.168-.168Zm-.233-3.361c0-.117.052-.168.168-.168h1.457c.117,0,.168.052.168.168v1.457c0,.117-.052.168-.168.168h-1.457c-.117,0-.168-.052-.168-.168Z" transform="translate(-115.694 -36.303)"/><path class="d" d="M308.986,82.674a3.183,3.183,0,1,0-2.934-3.174,2.9,2.9,0,0,0,2.934,3.174m0-7.6a4.431,4.431,0,1,1-4.326,4.43,4.2,4.2,0,0,1,4.326-4.43" transform="translate(-117.548 -38.036)"/><path class="d" d="M320.65,75.413c0-.117.052-.168.168-.168h.984c.117,0,.168.052.168.168v.965a3.525,3.525,0,0,1,2.817-1.308,2.785,2.785,0,0,1,2.953,3.089v5.427c0,.117-.052.168-.168.168h-.984c-.117,0-.168-.052-.168-.168V78.36a1.776,1.776,0,0,0-1.936-2.034c-1.425,0-2.513,1.036-2.513,3.089v4.171c0,.117-.052.168-.168.168h-.984c-.117,0-.168-.052-.168-.168Z" transform="translate(-123.181 -38.036)"/><path class="d" d="M159.987,77.223h3.867l-1.937-5.363Zm4.359,1.36h-4.851l-1.172,3.271c-.032.117-.071.168-.2.168h-1.133c-.117,0-.188-.052-.188-.168a.236.236,0,0,1,.019-.1l4.2-11.431c.032-.117.052-.168.168-.168h1.457c.117,0,.136.052.168.168l4.2,11.431a.222.222,0,0,1,.019.1c0,.117-.071.168-.188.168h-1.133c-.136,0-.168-.052-.2-.168Z" transform="translate(-65.452 -36.303)"/><path class="a" d="M182.816,30.314v15.1a.773.773,0,0,1-.227.557.781.781,0,0,1-.57.227H177.8a.781.781,0,0,1-.57-.227.762.762,0,0,1-.227-.557v-15.1a.773.773,0,0,1,.227-.557.781.781,0,0,1,.57-.227h4.223a.781.781,0,0,1,.57.227A.762.762,0,0,1,182.816,30.314Z" transform="translate(-72.569 -21.991)"/></g></svg>