.mp-form-row .card-js input.card-number {
    padding-right: 48px;
}
.mp-form-row .card-js .card-number-wrapper .card-type-icon {
    height: 23px;
    width: 32px;
    position: absolute;
    display: block;
    right: 8px;
    top: 7px;
    background: url(https://cardjs.co.uk/img/cards.png) 0 23px no-repeat;
    pointer-events: none;
    opacity: 0;
    -webkit-transition: opacity 0.15s linear;
    -moz-transition: opacity 0.15s linear;
    -ms-transition: opacity 0.15s linear;
    -o-transition: opacity 0.15s linear;
    transition: opacity 0.15s linear;
}
.mp-form-row .card-js .card-number-wrapper .show {
    opacity: 1;
}
.mp-form-row .card-js .card-number-wrapper .card-type-icon.visa {
    background-position: 0 0;
}
.mp-form-row .card-js .card-number-wrapper .card-type-icon.master-card {
    background-position: -32px 0;
}
.mp-form-row .card-js .card-number-wrapper .card-type-icon.american-express {
    background-position: -64px 0;
}
.mp-form-row .card-js .card-number-wrapper .card-type-icon.discover {
    background-position: -96px 0;
}
.mp-form-row .card-js .card-number-wrapper .card-type-icon.diners {
    background-position: -128px 0;
}
.mp-form-row .card-js .card-number-wrapper .card-type-icon.jcb {
    background-position: -160px 0;
}
.mp-form-row .card-js .cvc-container {
    width: 50%;
    float: right;
}
.mp-form-row .card-js .cvc-wrapper {
    box-sizing: border-box;
    margin-left: 5px;
}
.mp-form-row .card-js .cvc-wrapper .cvc {
    display: block;
    width: 100%;
}
.mp-form-row .card-js .expiry-container {
    width: 50%;
    float: left;
}
.mp-form-row .card-js .expiry-wrapper {
    box-sizing: border-box;
    margin-right: 5px;
}
.mp-form-row .card-js .expiry-wrapper .expiry {
    display: block;
    width: 100%;
}
.mp-form-row .card-js .expiry-wrapper .expiry-month {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    padding-left: 30px;
}
.mp-form-row .card-js .expiry-wrapper .expiry-year {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}
.mp-form-row .card-js .expiry-wrapper .expiry-month, .mp-form-row .card-js .expiry-wrapper .expiry-year {
    display: inline-block;
}
.mp-form-row .card-js .expiry-wrapper .expiry {
    padding-left: 38px;
}
.mp-form-row .card-js .icon {
    position: absolute;
    display: block;
    width: 24px;
    height: 17px;
    left: 8px;
    top: 10px;
    pointer-events: none;
}
.mp-form-row .card-js .icon.right {
    right: 8px;
    left: auto;
}
.mp-form-row .card-js .icon.popup {
    cursor: pointer;
    pointer-events: auto;
}
.mp-form-row .card-js .icon .svg {
    fill: #888;
}
.mp-form-row .card-js .icon.popup .svg {
    fill: #aaa !important;
}
.mp-form-row .card-js .card-number-wrapper, .mp-form-row .card-js .name-wrapper {
    margin-bottom: 15px;
    width: 100%;
}
.mp-form-row .card-js .card-number-wrapper, .mp-form-row .card-js .cvc-wrapper, .mp-form-row .card-js .expiry-wrapper, .mp-form-row .card-js .name-wrapper {
    -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, .7), inset 0 1px 0 rgba(255, 255, 255, .7);
    -moz-box-shadow: 0 1px 0 rgba(255, 255, 255, .7), inset 0 1px 0 rgba(255, 255, 255, .7);
    -ms-box-shadow: 0 1px 0 rgba(255, 255, 255, .7), inset 0 1px 0 rgba(255, 255, 255, .7);
    -o-box-shadow: 0 1px 0 rgba(255, 255, 255, .7), inset 0 1px 0 rgba(255, 255, 255, .7);
    box-shadow: 0 1px 0 rgba(255, 255, 255, .7), inset 0 1px 0 rgba(255, 255, 255, .7);
    position: relative;
}
.mp-form-row .card-js .card-number-wrapper, .mp-form-row .card-js .cvc-container, .mp-form-row .card-js .expiry-container, .mp-form-row .card-js .name-wrapper {
    display: inline-block;
}
.mp-form-row .card-js::after {
    content: ' ';
    display: table;
    clear: both;
}
.mp-form-row .card-js input, .mp-form-row .card-js select {
    color: #676767;
    font-size: 15px;
    font-weight: 300;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    height: 36px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: none;
    background-color: #fdfdfd;
    box-sizing: border-box;
    padding: 0;
    -webkit-transition: border-color 0.15s linear, box-shadow 0.15s linear;
    -moz-transition: border-color 0.15s linear, box-shadow 0.15s linear;
    -ms-transition: border-color 0.15s linear, box-shadow 0.15s linear;
    -o-transition: border-color 0.15s linear, box-shadow 0.15s linear;
    transition: border-color 0.15s linear, box-shadow 0.15s linear;
}
.mp-form-row .card-js select {
    -moz-appearance: none;
    text-indent: 0.01px;
    text-overflow: '';
}
.mp-form-row .card-js input[disabled], .mp-form-row .card-js select[disabled] {
    background-color: #eee;
    color: #555;
}
.mp-form-row .card-js select option[hidden] {
    color: #aba9a9;
}
.mp-form-row .card-js input:focus, .mp-form-row .card-js select:focus {
    background-color: #fff;
    outline: 0;
    border-color: #66afe9;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}
.mp-form-row .card-js input[readonly=readonly]:not([disabled]), .mp-form-row .card-js input[readonly]:not([disabled]) {
    background-color: #fff;
    cursor: pointer;
}
.mp-form-row .card-js .has-error input, .mp-form-row .card-js .has-error input:focus {
    border-color: #f64b2f;
    box-shadow: none;
}
.mp-form-row .card-js input.card-number, .mp-form-row .card-js input.cvc, .mp-form-row .card-js input.name {
    padding-left: 38px;
    width: 100%;
}
.mp-form-row .card-js.stripe .icon .svg {
    fill: #559a28;
}
