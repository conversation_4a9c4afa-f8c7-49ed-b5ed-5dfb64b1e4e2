<?php

if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.');
}

return [
    'AF' => _x('Afghanistan', 'ui', 'memberpress'),
    'AX' => _x('&#197;land Islands', 'ui', 'memberpress'),
    'AL' => _x('Albania', 'ui', 'memberpress'),
    'DZ' => _x('Algeria', 'ui', 'memberpress'),
    'AD' => _x('Andorra', 'ui', 'memberpress'),
    'AO' => _x('Angola', 'ui', 'memberpress'),
    'AI' => _x('Anguilla', 'ui', 'memberpress'),
    'AQ' => _x('Antarctica', 'ui', 'memberpress'),
    'AG' => _x('Antigua and Barbuda', 'ui', 'memberpress'),
    'AR' => _x('Argentina', 'ui', 'memberpress'),
    'AM' => _x('Armenia', 'ui', 'memberpress'),
    'AW' => _x('Aruba', 'ui', 'memberpress'),
    'AU' => _x('Australia', 'ui', 'memberpress'),
    'AT' => _x('Austria', 'ui', 'memberpress'),
    'AZ' => _x('Azerbaijan', 'ui', 'memberpress'),
    'BS' => _x('Bahamas', 'ui', 'memberpress'),
    'BH' => _x('Bahrain', 'ui', 'memberpress'),
    'BD' => _x('Bangladesh', 'ui', 'memberpress'),
    'BB' => _x('Barbados', 'ui', 'memberpress'),
    'BY' => _x('Belarus', 'ui', 'memberpress'),
    'BE' => _x('Belgium', 'ui', 'memberpress'),
    'PW' => _x('Belau', 'ui', 'memberpress'),
    'BZ' => _x('Belize', 'ui', 'memberpress'),
    'BJ' => _x('Benin', 'ui', 'memberpress'),
    'BM' => _x('Bermuda', 'ui', 'memberpress'),
    'BT' => _x('Bhutan', 'ui', 'memberpress'),
    'BO' => _x('Bolivia', 'ui', 'memberpress'),
    'BQ' => _x('Bonaire, Saint Eustatius and Saba', 'ui', 'memberpress'),
    'BA' => _x('Bosnia and Herzegovina', 'ui', 'memberpress'),
    'BW' => _x('Botswana', 'ui', 'memberpress'),
    'BV' => _x('Bouvet Island', 'ui', 'memberpress'),
    'BR' => _x('Brazil', 'ui', 'memberpress'),
    'IO' => _x('British Indian Ocean Territory', 'ui', 'memberpress'),
    'VG' => _x('British Virgin Islands', 'ui', 'memberpress'),
    'BN' => _x('Brunei', 'ui', 'memberpress'),
    'BG' => _x('Bulgaria', 'ui', 'memberpress'),
    'BF' => _x('Burkina Faso', 'ui', 'memberpress'),
    'BI' => _x('Burundi', 'ui', 'memberpress'),
    'KH' => _x('Cambodia', 'ui', 'memberpress'),
    'CM' => _x('Cameroon', 'ui', 'memberpress'),
    'CA' => _x('Canada', 'ui', 'memberpress'),
    'CV' => _x('Cape Verde', 'ui', 'memberpress'),
    'KY' => _x('Cayman Islands', 'ui', 'memberpress'),
    'CF' => _x('Central African Republic', 'ui', 'memberpress'),
    'TD' => _x('Chad', 'ui', 'memberpress'),
    'CL' => _x('Chile', 'ui', 'memberpress'),
    'CN' => _x('China', 'ui', 'memberpress'),
    'CX' => _x('Christmas Island', 'ui', 'memberpress'),
    'CC' => _x('Cocos (Keeling) Islands', 'ui', 'memberpress'),
    'CO' => _x('Colombia', 'ui', 'memberpress'),
    'KM' => _x('Comoros', 'ui', 'memberpress'),
    'CG' => _x('Congo (Brazzaville)', 'ui', 'memberpress'),
    'CD' => _x('Congo (Kinshasa)', 'ui', 'memberpress'),
    'CK' => _x('Cook Islands', 'ui', 'memberpress'),
    'CR' => _x('Costa Rica', 'ui', 'memberpress'),
    'HR' => _x('Croatia', 'ui', 'memberpress'),
    'CU' => _x('Cuba', 'ui', 'memberpress'),
    'CW' => _x('Cura&Ccedil;ao', 'ui', 'memberpress'),
    'CY' => _x('Cyprus', 'ui', 'memberpress'),
    'CZ' => _x('Czech Republic', 'ui', 'memberpress'),
    'DK' => _x('Denmark', 'ui', 'memberpress'),
    'DJ' => _x('Djibouti', 'ui', 'memberpress'),
    'DM' => _x('Dominica', 'ui', 'memberpress'),
    'DO' => _x('Dominican Republic', 'ui', 'memberpress'),
    'EC' => _x('Ecuador', 'ui', 'memberpress'),
    'EG' => _x('Egypt', 'ui', 'memberpress'),
    'SV' => _x('El Salvador', 'ui', 'memberpress'),
    'GQ' => _x('Equatorial Guinea', 'ui', 'memberpress'),
    'ER' => _x('Eritrea', 'ui', 'memberpress'),
    'EE' => _x('Estonia', 'ui', 'memberpress'),
    'ET' => _x('Ethiopia', 'ui', 'memberpress'),
    'FK' => _x('Falkland Islands', 'ui', 'memberpress'),
    'FO' => _x('Faroe Islands', 'ui', 'memberpress'),
    'FJ' => _x('Fiji', 'ui', 'memberpress'),
    'FI' => _x('Finland', 'ui', 'memberpress'),
    'FR' => _x('France', 'ui', 'memberpress'),
    'GF' => _x('French Guiana', 'ui', 'memberpress'),
    'PF' => _x('French Polynesia', 'ui', 'memberpress'),
    'TF' => _x('French Southern Territories', 'ui', 'memberpress'),
    'GA' => _x('Gabon', 'ui', 'memberpress'),
    'GM' => _x('Gambia', 'ui', 'memberpress'),
    'GE' => _x('Georgia', 'ui', 'memberpress'),
    'DE' => _x('Germany', 'ui', 'memberpress'),
    'GH' => _x('Ghana', 'ui', 'memberpress'),
    'GI' => _x('Gibraltar', 'ui', 'memberpress'),
    'GR' => _x('Greece', 'ui', 'memberpress'),
    'GL' => _x('Greenland', 'ui', 'memberpress'),
    'GD' => _x('Grenada', 'ui', 'memberpress'),
    'GP' => _x('Guadeloupe', 'ui', 'memberpress'),
    'GT' => _x('Guatemala', 'ui', 'memberpress'),
    'GG' => _x('Guernsey', 'ui', 'memberpress'),
    'GN' => _x('Guinea', 'ui', 'memberpress'),
    'GW' => _x('Guinea-Bissau', 'ui', 'memberpress'),
    'GY' => _x('Guyana', 'ui', 'memberpress'),
    'HT' => _x('Haiti', 'ui', 'memberpress'),
    'HM' => _x('Heard Island and McDonald Islands', 'ui', 'memberpress'),
    'HN' => _x('Honduras', 'ui', 'memberpress'),
    'HK' => _x('Hong Kong', 'ui', 'memberpress'),
    'HU' => _x('Hungary', 'ui', 'memberpress'),
    'IS' => _x('Iceland', 'ui', 'memberpress'),
    'IN' => _x('India', 'ui', 'memberpress'),
    'ID' => _x('Indonesia', 'ui', 'memberpress'),
    'IR' => _x('Iran', 'ui', 'memberpress'),
    'IQ' => _x('Iraq', 'ui', 'memberpress'),
    'IE' => _x('Republic of Ireland', 'ui', 'memberpress'),
    'IM' => _x('Isle of Man', 'ui', 'memberpress'),
    'IL' => _x('Israel', 'ui', 'memberpress'),
    'IT' => _x('Italy', 'ui', 'memberpress'),
    'CI' => _x('Ivory Coast', 'ui', 'memberpress'),
    'JM' => _x('Jamaica', 'ui', 'memberpress'),
    'JP' => _x('Japan', 'ui', 'memberpress'),
    'JE' => _x('Jersey', 'ui', 'memberpress'),
    'JO' => _x('Jordan', 'ui', 'memberpress'),
    'KZ' => _x('Kazakhstan', 'ui', 'memberpress'),
    'KE' => _x('Kenya', 'ui', 'memberpress'),
    'KI' => _x('Kiribati', 'ui', 'memberpress'),
    'KW' => _x('Kuwait', 'ui', 'memberpress'),
    'KG' => _x('Kyrgyzstan', 'ui', 'memberpress'),
    'LA' => _x('Laos', 'ui', 'memberpress'),
    'LV' => _x('Latvia', 'ui', 'memberpress'),
    'LB' => _x('Lebanon', 'ui', 'memberpress'),
    'LS' => _x('Lesotho', 'ui', 'memberpress'),
    'LR' => _x('Liberia', 'ui', 'memberpress'),
    'LY' => _x('Libya', 'ui', 'memberpress'),
    'LI' => _x('Liechtenstein', 'ui', 'memberpress'),
    'LT' => _x('Lithuania', 'ui', 'memberpress'),
    'LU' => _x('Luxembourg', 'ui', 'memberpress'),
    'MO' => _x('Macao S.A.R., China', 'ui', 'memberpress'),
    'MK' => _x('Macedonia', 'ui', 'memberpress'),
    'MG' => _x('Madagascar', 'ui', 'memberpress'),
    'MW' => _x('Malawi', 'ui', 'memberpress'),
    'MY' => _x('Malaysia', 'ui', 'memberpress'),
    'MV' => _x('Maldives', 'ui', 'memberpress'),
    'ML' => _x('Mali', 'ui', 'memberpress'),
    'MT' => _x('Malta', 'ui', 'memberpress'),
    'MH' => _x('Marshall Islands', 'ui', 'memberpress'),
    'MQ' => _x('Martinique', 'ui', 'memberpress'),
    'MR' => _x('Mauritania', 'ui', 'memberpress'),
    'MU' => _x('Mauritius', 'ui', 'memberpress'),
    'YT' => _x('Mayotte', 'ui', 'memberpress'),
    'MX' => _x('Mexico', 'ui', 'memberpress'),
    'FM' => _x('Micronesia', 'ui', 'memberpress'),
    'MD' => _x('Moldova', 'ui', 'memberpress'),
    'MC' => _x('Monaco', 'ui', 'memberpress'),
    'MN' => _x('Mongolia', 'ui', 'memberpress'),
    'ME' => _x('Montenegro', 'ui', 'memberpress'),
    'MS' => _x('Montserrat', 'ui', 'memberpress'),
    'MA' => _x('Morocco', 'ui', 'memberpress'),
    'MZ' => _x('Mozambique', 'ui', 'memberpress'),
    'MM' => _x('Myanmar', 'ui', 'memberpress'),
    'NA' => _x('Namibia', 'ui', 'memberpress'),
    'NR' => _x('Nauru', 'ui', 'memberpress'),
    'NP' => _x('Nepal', 'ui', 'memberpress'),
    'NL' => _x('Netherlands', 'ui', 'memberpress'),
    'AN' => _x('Netherlands Antilles', 'ui', 'memberpress'),
    'NC' => _x('New Caledonia', 'ui', 'memberpress'),
    'NZ' => _x('New Zealand', 'ui', 'memberpress'),
    'NI' => _x('Nicaragua', 'ui', 'memberpress'),
    'NE' => _x('Niger', 'ui', 'memberpress'),
    'NG' => _x('Nigeria', 'ui', 'memberpress'),
    'NU' => _x('Niue', 'ui', 'memberpress'),
    'NF' => _x('Norfolk Island', 'ui', 'memberpress'),
    'KP' => _x('North Korea', 'ui', 'memberpress'),
    'NO' => _x('Norway', 'ui', 'memberpress'),
    'OM' => _x('Oman', 'ui', 'memberpress'),
    'PK' => _x('Pakistan', 'ui', 'memberpress'),
    'PS' => _x('Palestinian Territory', 'ui', 'memberpress'),
    'PA' => _x('Panama', 'ui', 'memberpress'),
    'PG' => _x('Papua New Guinea', 'ui', 'memberpress'),
    'PY' => _x('Paraguay', 'ui', 'memberpress'),
    'PE' => _x('Peru', 'ui', 'memberpress'),
    'PH' => _x('Philippines', 'ui', 'memberpress'),
    'PN' => _x('Pitcairn', 'ui', 'memberpress'),
    'PL' => _x('Poland', 'ui', 'memberpress'),
    'PT' => _x('Portugal', 'ui', 'memberpress'),
    'PR' => _x('Puerto Rico', 'ui', 'memberpress'),
    'QA' => _x('Qatar', 'ui', 'memberpress'),
    'RE' => _x('Reunion', 'ui', 'memberpress'),
    'RO' => _x('Romania', 'ui', 'memberpress'),
    'RU' => _x('Russia', 'ui', 'memberpress'),
    'RW' => _x('Rwanda', 'ui', 'memberpress'),
    'BL' => _x('Saint Barth&eacute;lemy', 'ui', 'memberpress'),
    'SH' => _x('Saint Helena', 'ui', 'memberpress'),
    'KN' => _x('Saint Kitts and Nevis', 'ui', 'memberpress'),
    'LC' => _x('Saint Lucia', 'ui', 'memberpress'),
    'MF' => _x('Saint Martin (French part)', 'ui', 'memberpress'),
    'SX' => _x('Saint Martin (Dutch part)', 'ui', 'memberpress'),
    'PM' => _x('Saint Pierre and Miquelon', 'ui', 'memberpress'),
    'VC' => _x('Saint Vincent and the Grenadines', 'ui', 'memberpress'),
    'SM' => _x('San Marino', 'ui', 'memberpress'),
    'ST' => _x('S&atilde;o Tom&eacute; and Pr&iacute;ncipe', 'ui', 'memberpress'),
    'SA' => _x('Saudi Arabia', 'ui', 'memberpress'),
    'SN' => _x('Senegal', 'ui', 'memberpress'),
    'RS' => _x('Serbia', 'ui', 'memberpress'),
    'SC' => _x('Seychelles', 'ui', 'memberpress'),
    'SL' => _x('Sierra Leone', 'ui', 'memberpress'),
    'SG' => _x('Singapore', 'ui', 'memberpress'),
    'SK' => _x('Slovakia', 'ui', 'memberpress'),
    'SI' => _x('Slovenia', 'ui', 'memberpress'),
    'SB' => _x('Solomon Islands', 'ui', 'memberpress'),
    'SO' => _x('Somalia', 'ui', 'memberpress'),
    'ZA' => _x('South Africa', 'ui', 'memberpress'),
    'GS' => _x('South Georgia/Sandwich Islands', 'ui', 'memberpress'),
    'KR' => _x('South Korea', 'ui', 'memberpress'),
    'SS' => _x('South Sudan', 'ui', 'memberpress'),
    'ES' => _x('Spain', 'ui', 'memberpress'),
    'LK' => _x('Sri Lanka', 'ui', 'memberpress'),
    'SD' => _x('Sudan', 'ui', 'memberpress'),
    'SR' => _x('Suriname', 'ui', 'memberpress'),
    'SJ' => _x('Svalbard and Jan Mayen', 'ui', 'memberpress'),
    'SZ' => _x('Swaziland', 'ui', 'memberpress'),
    'SE' => _x('Sweden', 'ui', 'memberpress'),
    'CH' => _x('Switzerland', 'ui', 'memberpress'),
    'SY' => _x('Syria', 'ui', 'memberpress'),
    'TW' => _x('Taiwan', 'ui', 'memberpress'),
    'TJ' => _x('Tajikistan', 'ui', 'memberpress'),
    'TZ' => _x('Tanzania', 'ui', 'memberpress'),
    'TH' => _x('Thailand', 'ui', 'memberpress'),
    'TL' => _x('Timor-Leste', 'ui', 'memberpress'),
    'TG' => _x('Togo', 'ui', 'memberpress'),
    'TK' => _x('Tokelau', 'ui', 'memberpress'),
    'TO' => _x('Tonga', 'ui', 'memberpress'),
    'TT' => _x('Trinidad and Tobago', 'ui', 'memberpress'),
    'TN' => _x('Tunisia', 'ui', 'memberpress'),
    'TR' => _x('Turkey', 'ui', 'memberpress'),
    'TM' => _x('Turkmenistan', 'ui', 'memberpress'),
    'TC' => _x('Turks and Caicos Islands', 'ui', 'memberpress'),
    'TV' => _x('Tuvalu', 'ui', 'memberpress'),
    'UG' => _x('Uganda', 'ui', 'memberpress'),
    'UA' => _x('Ukraine', 'ui', 'memberpress'),
    'AE' => _x('United Arab Emirates', 'ui', 'memberpress'),
    'GB' => _x('United Kingdom (UK)', 'ui', 'memberpress'),
    'US' => _x('United States (US)', 'ui', 'memberpress'),
    'UY' => _x('Uruguay', 'ui', 'memberpress'),
    'UZ' => _x('Uzbekistan', 'ui', 'memberpress'),
    'VU' => _x('Vanuatu', 'ui', 'memberpress'),
    'VA' => _x('Vatican', 'ui', 'memberpress'),
    'VE' => _x('Venezuela', 'ui', 'memberpress'),
    'VN' => _x('Vietnam', 'ui', 'memberpress'),
    'WF' => _x('Wallis and Futuna', 'ui', 'memberpress'),
    'EH' => _x('Western Sahara', 'ui', 'memberpress'),
    'WS' => _x('Western Samoa', 'ui', 'memberpress'),
    'YE' => _x('Yemen', 'ui', 'memberpress'),
    'ZM' => _x('Zambia', 'ui', 'memberpress'),
    'ZW' => _x('Zimbabwe', 'ui', 'memberpress'),
];
