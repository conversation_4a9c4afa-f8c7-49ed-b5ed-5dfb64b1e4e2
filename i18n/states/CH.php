<?php

/**
 * Swiss Federal States
 */

$states['CH'] = [
    'AG' => _x('Aargau', 'ui', 'memberpress'),
    'AR' => _x('Appenzell Ausserrhoden', 'ui', 'memberpress'),
    'AI' => _x('Appenzell Innerrhoden', 'ui', 'memberpress'),
    'BL' => _x('Basel-Landschaft', 'ui', 'memberpress'),
    'BS' => _x('Basel-Stadt', 'ui', 'memberpress'),
    'BE' => _x('Bern', 'ui', 'memberpress'),
    'FR' => _x('Freiburg', 'ui', 'memberpress'),
    'GE' => _x('Genève', 'ui', 'memberpress'),
    'GL' => _x('Glarus', 'ui', 'memberpress'),
    'GR' => _x('Graubünden', 'ui', 'memberpress'),
    'JU' => _x('Jura', 'ui', 'memberpress'),
    'LU' => _x('Luzern', 'ui', 'memberpress'),
    'NE' => _x('Neuchâtel', 'ui', 'memberpress'),
    'NW' => _x('Nidwalden', 'ui', 'memberpress'),
    'OW' => _x('Obwalden', 'ui', 'memberpress'),
    'SH' => _x('Schaffhausen', 'ui', 'memberpress'),
    'SZ' => _x('Schwyz', 'ui', 'memberpress'),
    'SO' => _x('Solothurn', 'ui', 'memberpress'),
    'SG' => _x('St. Gallen', 'ui', 'memberpress'),
    'TG' => _x('Thurgau', 'ui', 'memberpress'),
    'TI' => _x('Ticino', 'ui', 'memberpress'),
    'UR' => _x('Uri', 'ui', 'memberpress'),
    'VS' => _x('Valais', 'ui', 'memberpress'),
    'VD' => _x('Vaud', 'ui', 'memberpress'),
    'ZG' => _x('Zug', 'ui', 'memberpress'),
    'ZH' => _x('Zürich', 'ui', 'memberpress'),
];
