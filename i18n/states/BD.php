<?php

/**
 * Bangladeshi states (districts)
 */

$states['BD'] = [
    'BAG'  => _x('Bagerhat', 'ui', 'memberpress'),
    'BAN'  => _x('Bandarban', 'ui', 'memberpress'),
    'BAR'  => _x('Barguna', 'ui', 'memberpress'),
    'BARI' => _x('Barisal', 'ui', 'memberpress'),
    'BHO'  => _x('Bhola', 'ui', 'memberpress'),
    'BOG'  => _x('Bogra', 'ui', 'memberpress'),
    'BRA'  => _x('Brahmanbaria', 'ui', 'memberpress'),
    'CHA'  => _x('Chandpur', 'ui', 'memberpress'),
    'CHI'  => _x('Chittagong', 'ui', 'memberpress'),
    'CHU'  => _x('Chuadanga', 'ui', 'memberpress'),
    'COM'  => _x('<PERSON>milla', 'ui', 'memberpress'),
    'COX'  => _x('Cox\'s Bazar', 'ui', 'memberpress'),
    'DHA'  => _x('Dhaka', 'ui', 'memberpress'),
    'DIN'  => _x('Dinajpur', 'ui', 'memberpress'),
    'FAR'  => _x('Faridpur ', 'ui', 'memberpress'),
    'FEN'  => _x('Feni', 'ui', 'memberpress'),
    'GAI'  => _x('Gaibandha', 'ui', 'memberpress'),
    'GAZI' => _x('Gazipur', 'ui', 'memberpress'),
    'GOP'  => _x('Gopalganj', 'ui', 'memberpress'),
    'HAB'  => _x('Habiganj', 'ui', 'memberpress'),
    'JAM'  => _x('Jamalpur', 'ui', 'memberpress'),
    'JES'  => _x('Jessore', 'ui', 'memberpress'),
    'JHA'  => _x('Jhalokati', 'ui', 'memberpress'),
    'JHE'  => _x('Jhenaidah', 'ui', 'memberpress'),
    'JOY'  => _x('Joypurhat', 'ui', 'memberpress'),
    'KHA'  => _x('Khagrachhari', 'ui', 'memberpress'),
    'KHU'  => _x('Khulna', 'ui', 'memberpress'),
    'KIS'  => _x('Kishoreganj', 'ui', 'memberpress'),
    'KUR'  => _x('Kurigram', 'ui', 'memberpress'),
    'KUS'  => _x('Kushtia', 'ui', 'memberpress'),
    'LAK'  => _x('Lakshmipur', 'ui', 'memberpress'),
    'LAL'  => _x('Lalmonirhat', 'ui', 'memberpress'),
    'MAD'  => _x('Madaripur', 'ui', 'memberpress'),
    'MAG'  => _x('Magura', 'ui', 'memberpress'),
    'MAN'  => _x('Manikganj ', 'ui', 'memberpress'),
    'MEH'  => _x('Meherpur', 'ui', 'memberpress'),
    'MOU'  => _x('Moulvibazar', 'ui', 'memberpress'),
    'MUN'  => _x('Munshiganj', 'ui', 'memberpress'),
    'MYM'  => _x('Mymensingh', 'ui', 'memberpress'),
    'NAO'  => _x('Naogaon', 'ui', 'memberpress'),
    'NAR'  => _x('Narail', 'ui', 'memberpress'),
    'NARG' => _x('Narayanganj', 'ui', 'memberpress'),
    'NARD' => _x('Narsingdi', 'ui', 'memberpress'),
    'NAT'  => _x('Natore', 'ui', 'memberpress'),
    'NAW'  => _x('Nawabganj', 'ui', 'memberpress'),
    'NET'  => _x('Netrakona', 'ui', 'memberpress'),
    'NIL'  => _x('Nilphamari', 'ui', 'memberpress'),
    'NOA'  => _x('Noakhali', 'ui', 'memberpress'),
    'PAB'  => _x('Pabna', 'ui', 'memberpress'),
    'PAN'  => _x('Panchagarh', 'ui', 'memberpress'),
    'PAT'  => _x('Patuakhali', 'ui', 'memberpress'),
    'PIR'  => _x('Pirojpur', 'ui', 'memberpress'),
    'RAJB' => _x('Rajbari', 'ui', 'memberpress'),
    'RAJ'  => _x('Rajshahi', 'ui', 'memberpress'),
    'RAN'  => _x('Rangamati', 'ui', 'memberpress'),
    'RANP' => _x('Rangpur', 'ui', 'memberpress'),
    'SAT'  => _x('Satkhira', 'ui', 'memberpress'),
    'SHA'  => _x('Shariatpur', 'ui', 'memberpress'),
    'SHE'  => _x('Sherpur', 'ui', 'memberpress'),
    'SIR'  => _x('Sirajganj', 'ui', 'memberpress'),
    'SUN'  => _x('Sunamganj', 'ui', 'memberpress'),
    'SYL'  => _x('Sylhet', 'ui', 'memberpress'),
    'TAN'  => _x('Tangail', 'ui', 'memberpress'),
    'THA'  => _x('Thakurgaon', 'ui', 'memberpress'),
];
