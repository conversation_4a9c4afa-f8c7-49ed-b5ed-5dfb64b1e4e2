<?php

/**
 * Bulgarian states
 */

$states['BG'] = [
    'BG-01' => _x('Blagoevgrad', 'ui', 'memberpress'),
    'BG-02' => _x('Burgas', 'ui', 'memberpress'),
    'BG-08' => _x('Dobri<PERSON>', 'ui', 'memberpress'),
    'BG-07' => _x('Gabrovo', 'ui', 'memberpress'),
    'BG-26' => _x('Haskovo', 'ui', 'memberpress'),
    'BG-09' => _x('Kardzhali', 'ui', 'memberpress'),
    'BG-10' => _x('Kyustendil', 'ui', 'memberpress'),
    'BG-11' => _x('Lovech', 'ui', 'memberpress'),
    'BG-12' => _x('Montana', 'ui', 'memberpress'),
    'BG-13' => _x('Pazardzhik', 'ui', 'memberpress'),
    'BG-14' => _x('Pernik', 'ui', 'memberpress'),
    'BG-15' => _x('Pleven', 'ui', 'memberpress'),
    'BG-16' => _x('Plovdiv', 'ui', 'memberpress'),
    'BG-17' => _x('Razgrad', 'ui', 'memberpress'),
    'BG-18' => _x('Ruse', 'ui', 'memberpress'),
    'BG-27' => _x('Shumen', 'ui', 'memberpress'),
    'BG-19' => _x('Silistra', 'ui', 'memberpress'),
    'BG-20' => _x('Sliven', 'ui', 'memberpress'),
    'BG-21' => _x('Smolyan', 'ui', 'memberpress'),
    'BG-23' => _x('Sofia', 'ui', 'memberpress'),
    'BG-22' => _x('Sofia-Grad', 'ui', 'memberpress'),
    'BG-24' => _x('Stara Zagora', 'ui', 'memberpress'),
    'BG-25' => _x('Targovishte', 'ui', 'memberpress'),
    'BG-03' => _x('Varna', 'ui', 'memberpress'),
    'BG-04' => _x('Veliko Tarnovo', 'ui', 'memberpress'),
    'BG-05' => _x('Vidin', 'ui', 'memberpress'),
    'BG-06' => _x('Vratsa', 'ui', 'memberpress'),
    'BG-28' => _x('Yambol', 'ui', 'memberpress'),
];
