<?php

/**
 * Italy Provinces
 */

$states['IT'] = [
    'AG' => _x('Agrigento', 'ui', 'memberpress'),
    'AL' => _x('Alessandria', 'ui', 'memberpress'),
    'AN' => _x('Ancona', 'ui', 'memberpress'),
    'AO' => _x('Aosta', 'ui', 'memberpress'),
    'AR' => _x('Arezzo', 'ui', 'memberpress'),
    'AP' => _x('Ascoli Pi<PERSON>no', 'ui', 'memberpress'),
    'AT' => _x('Asti', 'ui', 'memberpress'),
    'AV' => _x('Avellino', 'ui', 'memberpress'),
    'BA' => _x('Bari', 'ui', 'memberpress'),
    'BT' => _x('Barletta-Andria-Trani', 'ui', 'memberpress'),
    'BL' => _x('Belluno', 'ui', 'memberpress'),
    'BN' => _x('Benevento', 'ui', 'memberpress'),
    'BG' => _x('Berg<PERSON>', 'ui', 'memberpress'),
    'BI' => _x('<PERSON><PERSON><PERSON>', 'ui', 'memberpress'),
    'BO' => _x('Bologna', 'ui', 'memberpress'),
    'BZ' => _x('Bolzano', 'ui', 'memberpress'),
    'BS' => _x('Brescia', 'ui', 'memberpress'),
    'BR' => _x('Brindisi', 'ui', 'memberpress'),
    'CA' => _x('Cagliari', 'ui', 'memberpress'),
    'CL' => _x('Caltanissetta', 'ui', 'memberpress'),
    'CB' => _x('Campobasso', 'ui', 'memberpress'),
    'CI' => _x('Carbonia-Iglesias', 'ui', 'memberpress'),
    'CE' => _x('Caserta', 'ui', 'memberpress'),
    'CT' => _x('Catania', 'ui', 'memberpress'),
    'CZ' => _x('Catanzaro', 'ui', 'memberpress'),
    'CH' => _x('Chieti', 'ui', 'memberpress'),
    'CO' => _x('Como', 'ui', 'memberpress'),
    'CS' => _x('Cosenza', 'ui', 'memberpress'),
    'CR' => _x('Cremona', 'ui', 'memberpress'),
    'KR' => _x('Crotone', 'ui', 'memberpress'),
    'CN' => _x('Cuneo', 'ui', 'memberpress'),
    'EN' => _x('Enna', 'ui', 'memberpress'),
    'FM' => _x('Fermo', 'ui', 'memberpress'),
    'FE' => _x('Ferrara', 'ui', 'memberpress'),
    'FI' => _x('Firenze', 'ui', 'memberpress'),
    'FG' => _x('Foggia', 'ui', 'memberpress'),
    'FC' => _x('Forlì-Cesena', 'ui', 'memberpress'),
    'FR' => _x('Frosinone', 'ui', 'memberpress'),
    'GE' => _x('Genova', 'ui', 'memberpress'),
    'GO' => _x('Gorizia', 'ui', 'memberpress'),
    'GR' => _x('Grosseto', 'ui', 'memberpress'),
    'IM' => _x('Imperia', 'ui', 'memberpress'),
    'IS' => _x('Isernia', 'ui', 'memberpress'),
    'SP' => _x('La Spezia', 'ui', 'memberpress'),
    'AQ' => _x('L&apos;Aquila', 'ui', 'memberpress'),
    'LT' => _x('Latina', 'ui', 'memberpress'),
    'LE' => _x('Lecce', 'ui', 'memberpress'),
    'LC' => _x('Lecco', 'ui', 'memberpress'),
    'LI' => _x('Livorno', 'ui', 'memberpress'),
    'LO' => _x('Lodi', 'ui', 'memberpress'),
    'LU' => _x('Lucca', 'ui', 'memberpress'),
    'MC' => _x('Macerata', 'ui', 'memberpress'),
    'MN' => _x('Mantova', 'ui', 'memberpress'),
    'MS' => _x('Massa-Carrara', 'ui', 'memberpress'),
    'MT' => _x('Matera', 'ui', 'memberpress'),
    'ME' => _x('Messina', 'ui', 'memberpress'),
    'MI' => _x('Milano', 'ui', 'memberpress'),
    'MO' => _x('Modena', 'ui', 'memberpress'),
    'MB' => _x('Monza e della Brianza', 'ui', 'memberpress'),
    'NA' => _x('Napoli', 'ui', 'memberpress'),
    'NO' => _x('Novara', 'ui', 'memberpress'),
    'NU' => _x('Nuoro', 'ui', 'memberpress'),
    'OT' => _x('Olbia-Tempio', 'ui', 'memberpress'),
    'OR' => _x('Oristano', 'ui', 'memberpress'),
    'PD' => _x('Padova', 'ui', 'memberpress'),
    'PA' => _x('Palermo', 'ui', 'memberpress'),
    'PR' => _x('Parma', 'ui', 'memberpress'),
    'PV' => _x('Pavia', 'ui', 'memberpress'),
    'PG' => _x('Perugia', 'ui', 'memberpress'),
    'PU' => _x('Pesaro e Urbino', 'ui', 'memberpress'),
    'PE' => _x('Pescara', 'ui', 'memberpress'),
    'PC' => _x('Piacenza', 'ui', 'memberpress'),
    'PI' => _x('Pisa', 'ui', 'memberpress'),
    'PT' => _x('Pistoia', 'ui', 'memberpress'),
    'PN' => _x('Pordenone', 'ui', 'memberpress'),
    'PZ' => _x('Potenza', 'ui', 'memberpress'),
    'PO' => _x('Prato', 'ui', 'memberpress'),
    'RG' => _x('Ragusa', 'ui', 'memberpress'),
    'RA' => _x('Ravenna', 'ui', 'memberpress'),
    'RC' => _x('Reggio Calabria', 'ui', 'memberpress'),
    'RE' => _x('Reggio Emilia', 'ui', 'memberpress'),
    'RI' => _x('Rieti', 'ui', 'memberpress'),
    'RN' => _x('Rimini', 'ui', 'memberpress'),
    'RM' => _x('Roma', 'ui', 'memberpress'),
    'RO' => _x('Rovigo', 'ui', 'memberpress'),
    'SA' => _x('Salerno', 'ui', 'memberpress'),
    'VS' => _x('Medio Campidano', 'ui', 'memberpress'),
    'SS' => _x('Sassari', 'ui', 'memberpress'),
    'SV' => _x('Savona', 'ui', 'memberpress'),
    'SI' => _x('Siena', 'ui', 'memberpress'),
    'SR' => _x('Siracusa', 'ui', 'memberpress'),
    'SO' => _x('Sondrio', 'ui', 'memberpress'),
    'TA' => _x('Taranto', 'ui', 'memberpress'),
    'TE' => _x('Teramo', 'ui', 'memberpress'),
    'TR' => _x('Terni', 'ui', 'memberpress'),
    'TO' => _x('Torino', 'ui', 'memberpress'),
    'OG' => _x('Ogliastra', 'ui', 'memberpress'),
    'TP' => _x('Trapani', 'ui', 'memberpress'),
    'TN' => _x('Trento', 'ui', 'memberpress'),
    'TV' => _x('Treviso', 'ui', 'memberpress'),
    'TS' => _x('Trieste', 'ui', 'memberpress'),
    'UD' => _x('Udine', 'ui', 'memberpress'),
    'VA' => _x('Varese', 'ui', 'memberpress'),
    'VE' => _x('Venezia', 'ui', 'memberpress'),
    'VB' => _x('Verbano-Cusio-Ossola', 'ui', 'memberpress'),
    'VC' => _x('Vercelli', 'ui', 'memberpress'),
    'VR' => _x('Verona', 'ui', 'memberpress'),
    'VV' => _x('Vibo Valentia', 'ui', 'memberpress'),
    'VI' => _x('Vicenza', 'ui', 'memberpress'),
    'VT' => _x('Viterbo', 'ui', 'memberpress'),
];
