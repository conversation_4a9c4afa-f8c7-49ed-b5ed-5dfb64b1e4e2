<?php

/**
 * Nepal states (Districts)
 */

$states['NP'] = [

    // Mechi.
    'ILL' => _x('Illam', 'ui', 'memberpress'),
    'JHA' => _x('<PERSON><PERSON><PERSON>', 'ui', 'memberpress'),
    'PAN' => _x('<PERSON><PERSON>har', 'ui', 'memberpress'),
    'TAP' => _x('Taplejung', 'ui', 'memberpress'),

    // Koshi.
    'BHO' => _x('Bhojpur', 'ui', 'memberpress'),
    'DKA' => _x('Dhanku<PERSON>', 'ui', 'memberpress'),
    'MOR' => _x('Morang', 'ui', 'memberpress'),
    'SUN' => _x('Sunsari', 'ui', 'memberpress'),
    'SAN' => _x('Sankhuwa', 'ui', 'memberpress'),
    'TER' => _x('Terhathum', 'ui', 'memberpress'),

    // Sagarmatha.
    'KHO' => _x('Khotang', 'ui', 'memberpress'),
    'OKH' => _x('Okhaldhunga', 'ui', 'memberpress'),
    'SAP' => _x('Saptari', 'ui', 'memberpress'),
    'SIR' => _x('Siraha', 'ui', 'memberpress'),
    'SOL' => _x('Solukhumbu', 'ui', 'memberpress'),
    'UDA' => _x('Udayapur', 'ui', 'memberpress'),

    // Janakpur.
    'DHA' => _x('Dhanusa', 'ui', 'memberpress'),
    'DLK' => _x('Dolakha', 'ui', 'memberpress'),
    'MOH' => _x('Mohottari', 'ui', 'memberpress'),
    'RAM' => _x('Ramechha', 'ui', 'memberpress'),
    'SAR' => _x('Sarlahi', 'ui', 'memberpress'),
    'SIN' => _x('Sindhuli', 'ui', 'memberpress'),

    // Bagmati.
    'BHA' => _x('Bhaktapur', 'ui', 'memberpress'),
    'DHD' => _x('Dhading', 'ui', 'memberpress'),
    'KTM' => _x('Kathmandu', 'ui', 'memberpress'),
    'KAV' => _x('Kavrepalanchowk', 'ui', 'memberpress'),
    'LAL' => _x('Lalitpur', 'ui', 'memberpress'),
    'NUW' => _x('Nuwakot', 'ui', 'memberpress'),
    'RAS' => _x('Rasuwa', 'ui', 'memberpress'),
    'SPC' => _x('Sindhupalchowk', 'ui', 'memberpress'),

    // Narayani.
    'BAR' => _x('Bara', 'ui', 'memberpress'),
    'CHI' => _x('Chitwan', 'ui', 'memberpress'),
    'MAK' => _x('Makwanpur', 'ui', 'memberpress'),
    'PAR' => _x('Parsa', 'ui', 'memberpress'),
    'RAU' => _x('Rautahat', 'ui', 'memberpress'),

    // Gandaki.
    'GOR' => _x('Gorkha', 'ui', 'memberpress'),
    'KAS' => _x('Kaski', 'ui', 'memberpress'),
    'LAM' => _x('Lamjung', 'ui', 'memberpress'),
    'MAN' => _x('Manang', 'ui', 'memberpress'),
    'SYN' => _x('Syangja', 'ui', 'memberpress'),
    'TAN' => _x('Tanahun', 'ui', 'memberpress'),

    // Dhawalagiri.
    'BAG' => _x('Baglung', 'ui', 'memberpress'),
    'PBT' => _x('Parbat', 'ui', 'memberpress'),
    'MUS' => _x('Mustang', 'ui', 'memberpress'),
    'MYG' => _x('Myagdi', 'ui', 'memberpress'),

    // Lumbini.
    'AGR' => _x('Agrghakanchi', 'ui', 'memberpress'),
    'GUL' => _x('Gulmi', 'ui', 'memberpress'),
    'KAP' => _x('Kapilbastu', 'ui', 'memberpress'),
    'NAW' => _x('Nawalparasi', 'ui', 'memberpress'),
    'PAL' => _x('Palpa', 'ui', 'memberpress'),
    'RUP' => _x('Rupandehi', 'ui', 'memberpress'),

    // Rapti.
    'DAN' => _x('Dang', 'ui', 'memberpress'),
    'PYU' => _x('Pyuthan', 'ui', 'memberpress'),
    'ROL' => _x('Rolpa', 'ui', 'memberpress'),
    'RUK' => _x('Rukum', 'ui', 'memberpress'),
    'SAL' => _x('Salyan', 'ui', 'memberpress'),

    // Bheri.
    'BAN' => _x('Banke', 'ui', 'memberpress'),
    'BDA' => _x('Bardiya', 'ui', 'memberpress'),
    'DAI' => _x('Dailekh', 'ui', 'memberpress'),
    'JAJ' => _x('Jajarkot', 'ui', 'memberpress'),
    'SUR' => _x('Surkhet', 'ui', 'memberpress'),

    // Karnali.
    'DOL' => _x('Dolpa', 'ui', 'memberpress'),
    'HUM' => _x('Humla', 'ui', 'memberpress'),
    'JUM' => _x('Jumla', 'ui', 'memberpress'),
    'KAL' => _x('Kalikot', 'ui', 'memberpress'),
    'MUG' => _x('Mugu', 'ui', 'memberpress'),

    // Seti.
    'ACH' => _x('Achham', 'ui', 'memberpress'),
    'BJH' => _x('Bajhang', 'ui', 'memberpress'),
    'BJU' => _x('Bajura', 'ui', 'memberpress'),
    'DOT' => _x('Doti', 'ui', 'memberpress'),
    'KAI' => _x('Kailali', 'ui', 'memberpress'),

    // Mahakali.
    'BAI' => _x('Baitadi', 'ui', 'memberpress'),
    'DAD' => _x('Dadeldhura', 'ui', 'memberpress'),
    'DAR' => _x('Darchula', 'ui', 'memberpress'),
    'KAN' => _x('Kanchanpur', 'ui', 'memberpress'),
];
