<?php

/**
 * Spain states
 */

$states['ES'] = [
    'C'  => _x('A Coru&ntilde;a', 'ui', 'memberpress'),
    'VI' => _x('Araba/&Aacute;lava', 'ui', 'memberpress'),
    'AB' => _x('Albacete', 'ui', 'memberpress'),
    'A'  => _x('Alicante', 'ui', 'memberpress'),
    'AL' => _x('Almer&iacute;a', 'ui', 'memberpress'),
    'O'  => _x('Asturias', 'ui', 'memberpress'),
    'AV' => _x('&Aacute;vila', 'ui', 'memberpress'),
    'BA' => _x('Badajoz', 'ui', 'memberpress'),
    'PM' => _x('Baleares', 'ui', 'memberpress'),
    'B'  => _x('Barcelona', 'ui', 'memberpress'),
    'BU' => _x('Burgos', 'ui', 'memberpress'),
    'CC' => _x('C&aacute;ceres', 'ui', 'memberpress'),
    'CA' => _x('C&aacute;diz', 'ui', 'memberpress'),
    'S'  => _x('Cantabria', 'ui', 'memberpress'),
    'CS' => _x('Castell&oacute;n', 'ui', 'memberpress'),
    'CE' => _x('Ceuta', 'ui', 'memberpress'),
    'CR' => _x('Ciudad Real', 'ui', 'memberpress'),
    'CO' => _x('C&oacute;rdoba', 'ui', 'memberpress'),
    'CU' => _x('Cuenca', 'ui', 'memberpress'),
    'GI' => _x('Girona', 'ui', 'memberpress'),
    'GR' => _x('Granada', 'ui', 'memberpress'),
    'GU' => _x('Guadalajara', 'ui', 'memberpress'),
    'SS' => _x('Gipuzkoa', 'ui', 'memberpress'),
    'H'  => _x('Huelva', 'ui', 'memberpress'),
    'HU' => _x('Huesca', 'ui', 'memberpress'),
    'J'  => _x('Ja&eacute;n', 'ui', 'memberpress'),
    'LO' => _x('La Rioja', 'ui', 'memberpress'),
    'GC' => _x('Las Palmas', 'ui', 'memberpress'),
    'LE' => _x('Le&oacute;n', 'ui', 'memberpress'),
    'L'  => _x('Lleida', 'ui', 'memberpress'),
    'LU' => _x('Lugo', 'ui', 'memberpress'),
    'M'  => _x('Madrid', 'ui', 'memberpress'),
    'MA' => _x('M&aacute;laga', 'ui', 'memberpress'),
    'ML' => _x('Melilla', 'ui', 'memberpress'),
    'MU' => _x('Murcia', 'ui', 'memberpress'),
    'NA' => _x('Navarra', 'ui', 'memberpress'),
    'OR' => _x('Ourense', 'ui', 'memberpress'),
    'P'  => _x('Palencia', 'ui', 'memberpress'),
    'PO' => _x('Pontevedra', 'ui', 'memberpress'),
    'SA' => _x('Salamanca', 'ui', 'memberpress'),
    'TF' => _x('Santa Cruz de Tenerife', 'ui', 'memberpress'),
    'SG' => _x('Segovia', 'ui', 'memberpress'),
    'SE' => _x('Sevilla', 'ui', 'memberpress'),
    'SO' => _x('Soria', 'ui', 'memberpress'),
    'T'  => _x('Tarragona', 'ui', 'memberpress'),
    'TE' => _x('Teruel', 'ui', 'memberpress'),
    'TO' => _x('Toledo', 'ui', 'memberpress'),
    'V'  => _x('Valencia', 'ui', 'memberpress'),
    'VA' => _x('Valladolid', 'ui', 'memberpress'),
    'BI' => _x('Bizkaia', 'ui', 'memberpress'),
    'ZA' => _x('Zamora', 'ui', 'memberpress'),
    'Z'  => _x('Zaragoza', 'ui', 'memberpress'),
];
