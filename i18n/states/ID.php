<?php

/**
 * Indonesia Provinces
 */

$states['ID'] = [
    'BA' => _x('Bali', 'ui', 'memberpress'),
    'BB' => _x('Bangka Belitung', 'ui', 'memberpress'),
    'BT' => _x('Banten', 'ui', 'memberpress'),
    'BE' => _x('Bengku<PERSON>', 'ui', 'memberpress'),
    'AC' => _x('Daerah Istimewa Aceh', 'ui', 'memberpress'),
    'YO' => _x('Daerah Istimewa Yogyakarta', 'ui', 'memberpress'),
    'JK' => _x('DKI Jakarta', 'ui', 'memberpress'),
    'GO' => _x('Gorontalo', 'ui', 'memberpress'),
    'JA' => _x('Jambi', 'ui', 'memberpress'),
    'JB' => _x('Jawa Barat', 'ui', 'memberpress'),
    'JT' => _x('Jawa Tengah', 'ui', 'memberpress'),
    'JI' => _x('Jawa Timur', 'ui', 'memberpress'),
    'KB' => _x('Kalimantan Barat', 'ui', 'memberpress'),
    'KS' => _x('Kalimantan Selatan', 'ui', 'memberpress'),
    'KT' => _x('Kalimantan Tengah', 'ui', 'memberpress'),
    'KI' => _x('Kalimantan Timur', 'ui', 'memberpress'),
    'KU' => _x('Kalimantan Utara', 'ui', 'memberpress'),
    'KR' => _x('Kepulauan Riau', 'ui', 'memberpress'),
    'LA' => _x('Lampung', 'ui', 'memberpress'),
    'MA' => _x('Maluku', 'ui', 'memberpress'),
    'MU' => _x('Maluku Utara', 'ui', 'memberpress'),
    'NB' => _x('Nusa Tenggara Barat', 'ui', 'memberpress'),
    'NT' => _x('Nusa Tenggara Timur', 'ui', 'memberpress'),
    'PA' => _x('Papua', 'ui', 'memberpress'),
    'PB' => _x('Papua Barat', 'ui', 'memberpress'),
    'RI' => _x('Riau', 'ui', 'memberpress'),
    'SR' => _x('Sulawesi Barat', 'ui', 'memberpress'),
    'SN' => _x('Sulawesi Selatan', 'ui', 'memberpress'),
    'SA' => _x('Sulawesi Utara', 'ui', 'memberpress'),
    'ST' => _x('Sulawesi Tengah', 'ui', 'memberpress'),
    'SG' => _x('Sulawesi Tenggara', 'ui', 'memberpress'),
    'SB' => _x('Sumatera Barat', 'ui', 'memberpress'),
    'SS' => _x('Sumatera Selatan', 'ui', 'memberpress'),
    'SU' => _x('Sumatera Utara', 'ui', 'memberpress'),
];
