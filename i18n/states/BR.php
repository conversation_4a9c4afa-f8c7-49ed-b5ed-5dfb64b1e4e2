<?php

/**
 * Brazillian states
 */

$states['BR'] = [
    'AC' => _x('Acre', 'ui', 'memberpress'),
    'AL' => _x('Alagoas', 'ui', 'memberpress'),
    'AP' => _x('Amap&aacute;', 'ui', 'memberpress'),
    'AM' => _x('Amazonas', 'ui', 'memberpress'),
    'BA' => _x('Bahia', 'ui', 'memberpress'),
    'CE' => _x('Cear&aacute;', 'ui', 'memberpress'),
    'DF' => _x('Distrito Federal', 'ui', 'memberpress'),
    'ES' => _x('Esp&iacute;rito Santo', 'ui', 'memberpress'),
    'GO' => _x('Goi&aacute;s', 'ui', 'memberpress'),
    'MA' => _x('Maranh&atilde;o', 'ui', 'memberpress'),
    'MT' => _x('Mato Grosso', 'ui', 'memberpress'),
    'MS' => _x('Mato Grosso do Sul', 'ui', 'memberpress'),
    'MG' => _x('Minas Gerais', 'ui', 'memberpress'),
    'PA' => _x('Par&aacute;', 'ui', 'memberpress'),
    'PB' => _x('Para&iacute;ba', 'ui', 'memberpress'),
    'PR' => _x('Paran&aacute;', 'ui', 'memberpress'),
    'PE' => _x('Pernambuco', 'ui', 'memberpress'),
    'PI' => _x('Piau&iacute;', 'ui', 'memberpress'),
    'RJ' => _x('Rio de Janeiro', 'ui', 'memberpress'),
    'RN' => _x('Rio Grande do Norte', 'ui', 'memberpress'),
    'RS' => _x('Rio Grande do Sul', 'ui', 'memberpress'),
    'RO' => _x('Rond&ocirc;nia', 'ui', 'memberpress'),
    'RR' => _x('Roraima', 'ui', 'memberpress'),
    'SC' => _x('Santa Catarina', 'ui', 'memberpress'),
    'SP' => _x('S&atilde;o Paulo', 'ui', 'memberpress'),
    'SE' => _x('Sergipe', 'ui', 'memberpress'),
    'TO' => _x('Tocantins', 'ui', 'memberpress'),
];
