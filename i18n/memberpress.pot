# Copyright (C) 2025 Caseproof, LLC
# This file is distributed under the same license as the MemberPress plugin.
msgid ""
msgstr ""
"Project-Id-Version: MemberPress 1.12.3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/memberpress\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-05-19T21:48:52+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: memberpress\n"

#. Plugin Name of the plugin
#: memberpress.php
#: app/controllers/MeprAppCtrl.php:589
#: app/integrations/stripe-tax/Integration.php:488
msgid "MemberPress"
msgstr ""

#. Plugin URI of the plugin
#: memberpress.php
msgid "https://memberpress.com/"
msgstr ""

#. Description of the plugin
#: memberpress.php
msgid "The membership plugin that makes it easy to accept payments for access to your content and digital products."
msgstr ""

#. Author of the plugin
#: memberpress.php
msgid "Caseproof, LLC"
msgstr ""

#. Author URI of the plugin
#: memberpress.php
msgid "http://caseproof.com/"
msgstr ""

#: app/controllers/MeprAccountCtrl.php:69
msgctxt "ui"
msgid "You already have a subscription to this Membership. Please %1$supdate your payment details%2$s on the existing subscription instead of purchasing again."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:295
msgid "Your account has been saved."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:306
msgid "Your password was successfully updated."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:397
msgid "Password update failed, please check that your password meets the minimum strength requirement."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:399
msgid "Password update failed, please be sure your passwords match and try again."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:537
msgid "Your subscription was successfully paused."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:565
msgid "You successfully resumed your subscription."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:586
msgid "Your subscription was successfully cancelled."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:631
#: app/controllers/MeprStripeCtrl.php:652
msgid "Your account information was successfully updated."
msgstr ""

#: app/controllers/MeprAccountCtrl.php:980
#: app/views/account/nav.php:11
#: app/views/account/nav.php:23
#: app/views/readylaunch/account/nav.php:20
#: app/views/readylaunch/account/subscriptions.php:8
msgctxt "ui"
msgid "Subscriptions"
msgstr ""

#: app/controllers/MeprAccountCtrl.php:983
#: app/views/account/nav.php:13
#: app/views/readylaunch/account/nav.php:15
#: app/views/readylaunch/account/payments.php:7
msgctxt "ui"
msgid "Payments"
msgstr ""

#: app/controllers/MeprAccountCtrl.php:986
msgctxt "ui"
msgid "Courses"
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:56
#: app/controllers/MeprSubscriptionsCtrl.php:648
#: app/helpers/MeprAppHelper.php:791
#: app/views/admin/addons/ui.php:38
#: app/views/admin/members/row.php:71
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:422
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:40
#: vendor-prefixed/caseproof/growth-tools/src/App.php:102
msgid "Active"
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:57
#: app/views/admin/addons/ui.php:44
#: app/views/admin/members/row.php:73
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:423
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:46
msgid "Inactive"
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:58
#: app/views/admin/addons/ui.php:87
#: app/views/admin/onboarding/license.php:15
#: app/views/admin/onboarding/license.php:25
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:424
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:106
#: vendor-prefixed/caseproof/growth-tools/src/App.php:103
msgid "Activate"
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:59
#: app/views/admin/addons/ui.php:84
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:425
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:96
#: vendor-prefixed/caseproof/growth-tools/src/App.php:104
msgid "Deactivate"
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:60
#: app/controllers/MeprAddonsCtrl.php:168
msgid "Could not install add-on. Please download from memberpress.com and install manually."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:61
#: app/controllers/MeprAddonsCtrl.php:72
#: app/controllers/MeprAddonsCtrl.php:166
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:263
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:430
msgid "Could not install plugin. Please download and install manually."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:73
msgid "Installed & Activated"
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:86
#: app/controllers/MeprAddonsCtrl.php:123
#: app/controllers/MeprAddonsCtrl.php:152
#: app/controllers/MeprDrmCtrl.php:212
#: app/controllers/MeprDrmCtrl.php:274
#: app/controllers/MeprMigratorCtrl.php:68
#: app/controllers/MeprOnboardingCtrl.php:430
#: app/controllers/MeprOnboardingCtrl.php:524
#: app/controllers/MeprOnboardingCtrl.php:766
#: app/controllers/MeprOnboardingCtrl.php:856
#: app/controllers/MeprOnboardingCtrl.php:921
#: app/controllers/MeprOnboardingCtrl.php:1016
#: app/controllers/MeprOnboardingCtrl.php:1147
#: app/controllers/MeprOnboardingCtrl.php:1154
#: app/controllers/MeprOnboardingCtrl.php:1163
#: app/controllers/MeprOnboardingCtrl.php:1218
#: app/controllers/MeprOnboardingCtrl.php:1226
#: app/controllers/MeprOnboardingCtrl.php:1271
#: app/controllers/MeprOnboardingCtrl.php:1281
#: app/controllers/MeprOnboardingCtrl.php:1341
#: app/controllers/MeprOnboardingCtrl.php:1415
#: app/controllers/MeprOnboardingCtrl.php:1515
#: app/controllers/MeprOptionsCtrl.php:313
#: app/controllers/MeprOptionsCtrl.php:417
#: app/controllers/MeprOptionsCtrl.php:471
#: app/controllers/MeprOptionsCtrl.php:508
#: app/controllers/MeprOptionsCtrl.php:523
#: app/controllers/MeprOptionsCtrl.php:530
#: app/controllers/MeprOptionsCtrl.php:555
#: app/controllers/MeprOptionsCtrl.php:571
#: app/controllers/MeprOptionsCtrl.php:578
#: app/integrations/stripe-tax/Integration.php:735
#: app/integrations/stripe-tax/Integration.php:743
#: app/integrations/stripe-tax/Integration.php:785
#: app/integrations/stripe-tax/Integration.php:793
#: app/lib/MeprUtils.php:4031
#: app/lib/MeprUtils.php:4054
#: app/lib/MeprUtils.php:4060
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:175
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:222
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:249
msgid "Bad request."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:90
#: app/controllers/MeprAddonsCtrl.php:127
#: app/controllers/MeprAddonsCtrl.php:156
#: app/controllers/MeprCoachkitCtrl.php:91
#: app/controllers/MeprCoursesCtrl.php:94
#: app/controllers/MeprDrmCtrl.php:216
#: app/controllers/MeprDrmCtrl.php:278
#: app/controllers/MeprOnboardingCtrl.php:434
#: app/controllers/MeprOnboardingCtrl.php:762
#: app/controllers/MeprOnboardingCtrl.php:860
#: app/controllers/MeprOnboardingCtrl.php:925
#: app/controllers/MeprOnboardingCtrl.php:1275
#: app/controllers/MeprOnboardingCtrl.php:1345
#: app/controllers/MeprOnboardingCtrl.php:1419
#: app/controllers/MeprOptionsCtrl.php:317
#: app/controllers/MeprOptionsCtrl.php:421
#: app/controllers/MeprOptionsCtrl.php:475
#: app/controllers/MeprOptionsCtrl.php:512
#: app/controllers/MeprOptionsCtrl.php:559
#: app/controllers/MeprStripeConnectCtrl.php:370
#: app/controllers/MeprStripeConnectCtrl.php:427
#: app/controllers/MeprStripeConnectCtrl.php:502
#: app/lib/MeprUtils.php:4035
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:179
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:225
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:253
msgid "Sorry, you don't have permission to do this."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:94
#: app/controllers/MeprAddonsCtrl.php:131
#: app/controllers/MeprAddonsCtrl.php:160
#: app/controllers/MeprDrmCtrl.php:220
#: app/controllers/MeprDrmCtrl.php:282
#: app/controllers/MeprOptionsCtrl.php:321
#: app/controllers/MeprOptionsCtrl.php:425
#: app/controllers/MeprOptionsCtrl.php:479
#: app/controllers/MeprOptionsCtrl.php:516
#: app/controllers/MeprOptionsCtrl.php:563
#: app/controllers/MeprStripeCtrl.php:553
#: app/controllers/MeprStripeCtrl.php:614
#: app/controllers/MeprSubscriptionsCtrl.php:406
#: app/controllers/MeprSubscriptionsCtrl.php:452
#: app/integrations/stripe-tax/Integration.php:729
#: app/integrations/stripe-tax/Integration.php:779
#: app/lib/MeprUtils.php:4039
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:183
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:228
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:257
msgid "Security check failed."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:102
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:192
msgid "Could not activate plugin. Please activate from the Plugins page manually."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:104
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:199
msgid "Could not activate add-on. Please activate from the Plugins page manually."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:109
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:208
msgid "Plugin activated."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:111
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:210
msgid "Add-on activated."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:138
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:235
#: vendor-prefixed/caseproof/growth-tools/src/Helper/AddonHelper.php:70
msgid "Plugin deactivated."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:140
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:237
msgid "Add-on deactivated."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:256
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:315
msgid "Plugin installed & activated."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:256
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:316
msgid "Add-on installed & activated."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:264
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:325
msgid "Plugin installed."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:264
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:326
msgid "Add-on installed."
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:355
#: app/views/admin/addons/affiliates.php:69
msgid "Activate Easy Affiliate"
msgstr ""

#: app/controllers/MeprAddonsCtrl.php:359
#: app/views/admin/addons/affiliates.php:90
#: app/views/admin/addons/affiliates.php:92
msgid "Run Setup Wizard"
msgstr ""

#: app/controllers/MeprAntiCardTestingCtrl.php:28
msgid "Card Testing Protection"
msgstr ""

#: app/controllers/MeprAntiCardTestingCtrl.php:33
#: app/controllers/MeprAntiCardTestingCtrl.php:37
msgid "Enable Card Testing Protection"
msgstr ""

#. Translators: %1$s: br tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:40
msgid "Card testing is a type of fraudulent activity where someone tries to determine if stolen card information can be used to make purchases, by repeatedly attempting a purchase with different card numbers until one succeeds.%1$s%1$sBy enabling this protection, MemberPress will permanently block any further payment attempts by any user that has had 5 failed payments in a 2 hour window."
msgstr ""

#: app/controllers/MeprAntiCardTestingCtrl.php:58
#: app/controllers/MeprAntiCardTestingCtrl.php:62
msgid "How To Get Visitor IP?"
msgstr ""

#. Translators: %1$s: br tag, %2$s: open link tag, %3$s: close link tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:65
msgid "Which method should MemberPress use to retrieve the visitor's IP address?%1$s%1$sIt's important to use a method that is compatible with your site. The REMOTE_ADDR method is the most secure but may not be correct if your site is using a front-end proxy.%1$s%1$sCompare the displayed detected IP address with what is displayed on %2$sthis site%3$s to find the correct method for your site."
msgstr ""

#. Translators: %1$s: open strong tag, %2$s: close strong tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:80
msgid "%1$sDefault%2$s - Compatible with most sites, but not as secure as the methods below."
msgstr ""

#. Translators: %1$s: open strong tag, %2$s: close strong tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:93
msgid "%1$sUse PHP's built-in REMOTE_ADDR%2$s - The most secure method if this is compatible with your site."
msgstr ""

#. Translators: %1$s: open strong tag, %2$s: close strong tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:106
msgid "%1$sUse the X-Forwarded-For HTTP header%2$s - Only use this if you're using a front-end proxy or spoofing may result."
msgstr ""

#. Translators: %1$s: open strong tag, %2$s: close strong tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:119
msgid "%1$sUse the X-Real-IP HTTP header%2$s - Only use this if you're using a front-end proxy or spoofing may result."
msgstr ""

#. Translators: %1$s: open strong tag, %2$s: close strong tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:132
msgid "%1$sUse the CF-Connecting-IP HTTP header%2$s - Only use this if you're using Cloudflare."
msgstr ""

#: app/controllers/MeprAntiCardTestingCtrl.php:139
msgid "Detected IP address using the selected method:"
msgstr ""

#: app/controllers/MeprAntiCardTestingCtrl.php:144
#: app/controllers/MeprAntiCardTestingCtrl.php:148
msgid "Blocked IP Addresses"
msgstr ""

#. Translators: %1$s: br tag.
#: app/controllers/MeprAntiCardTestingCtrl.php:151
msgid "The IP addresses listed here are currently banned from making purchases.%1$s%1$sYou can add a new IP address (one per line) to block it, or remove an IP address to unblock it."
msgstr ""

#: app/controllers/MeprAntiCardTestingCtrl.php:305
#: app/controllers/MeprAntiCardTestingCtrl.php:321
msgid "We are not able to complete your purchase at this time. Please contact us for more information."
msgstr ""

#: app/controllers/MeprApiCtrl.php:91
msgid "No credentials have been provided."
msgstr ""

#: app/controllers/MeprApiCtrl.php:113
#: app/lib/MeprUtils.php:1367
msgid "UNAUTHORIZED: %s"
msgstr ""

#: app/controllers/MeprAppCtrl.php:127
msgid "MemberPress Unauthorized Access on the Group Pricing Page"
msgstr ""

#: app/controllers/MeprAppCtrl.php:134
msgid "This Group Pricing Page is Protected"
msgstr ""

#: app/controllers/MeprAppCtrl.php:144
msgid "MemberPress Unauthorized Access"
msgstr ""

#: app/controllers/MeprAppCtrl.php:153
#: app/controllers/MeprAppCtrl.php:177
msgid "This %s is Protected"
msgstr ""

#: app/controllers/MeprAppCtrl.php:167
msgid "MemberPress Unauthorized Access to this %s"
msgstr ""

#: app/controllers/MeprAppCtrl.php:204
msgid "Public"
msgstr ""

#: app/controllers/MeprAppCtrl.php:279
#: app/controllers/MeprRulesCtrl.php:176
msgid "Access"
msgstr ""

#: app/controllers/MeprAppCtrl.php:324
msgid "This Content is Protected by %s MemberPress Access Rule"
msgid_plural "This Content is Protected by %s MemberPress Access Rules"
msgstr[0] ""
msgstr[1] ""

#: app/controllers/MeprAppCtrl.php:331
msgid "Click here to view"
msgstr ""

#: app/controllers/MeprAppCtrl.php:347
msgid "<strong>MemberPress: Your PHP version (%s) is out of date!</strong> This version has reached official End Of Life and as such may expose your site to security vulnerabilities. Please contact your web hosting provider to update to %s or newer"
msgstr ""

#: app/controllers/MeprAppCtrl.php:580
msgid "%d unread message(s)"
msgstr ""

#: app/controllers/MeprAppCtrl.php:597
msgid "Messages"
msgstr ""

#: app/controllers/MeprAppCtrl.php:605
#: app/controllers/MeprAppCtrl.php:1677
#: app/views/admin/reports/main.php:6
msgid "Reports"
msgstr ""

#: app/controllers/MeprAppCtrl.php:612
#: app/controllers/MeprAppCtrl.php:1678
#: app/views/admin/options/form.php:6
#: app/views/admin/options/form.php:559
msgid "Settings"
msgstr ""

#: app/controllers/MeprAppCtrl.php:619
#: app/controllers/MeprAppCtrl.php:1663
#: app/controllers/MeprAppCtrl.php:1709
#: app/controllers/MeprMembersCtrl.php:295
#: app/helpers/MeprProductsHelper.php:191
#: app/views/admin/members/list.php:6
msgid "Members"
msgstr ""

#: app/controllers/MeprAppCtrl.php:626
#: app/controllers/MeprAppCtrl.php:770
#: app/controllers/MeprCouponsCtrl.php:45
msgid "Coupons"
msgstr ""

#: app/controllers/MeprAppCtrl.php:633
#: app/controllers/MeprAppCtrl.php:764
#: app/controllers/MeprMembersCtrl.php:156
#: app/controllers/MeprProductsCtrl.php:64
#: app/controllers/MeprRemindersCtrl.php:138
#: app/views/admin/groups/form.php:62
msgid "Memberships"
msgstr ""

#: app/controllers/MeprAppCtrl.php:640
#: app/controllers/MeprAppCtrl.php:1673
#: app/controllers/MeprAppCtrl.php:1675
#: app/controllers/MeprMembersCtrl.php:145
#: app/controllers/MeprSubscriptionsCtrl.php:691
#: app/views/admin/subscriptions/list.php:9
#: js/blocks/subscriptions/index.js:11
#: js/build/blocks.js:1
msgid "Subscriptions"
msgstr ""

#: app/controllers/MeprAppCtrl.php:647
#: app/controllers/MeprAppCtrl.php:1676
#: app/controllers/MeprMembersCtrl.php:146
#: app/controllers/MeprReportsCtrl.php:243
#: app/controllers/MeprTransactionsCtrl.php:761
#: app/views/admin/reports/main.php:39
#: app/views/admin/reports/main.php:77
#: app/views/admin/transactions/list.php:12
msgid "Transactions"
msgstr ""

#: app/controllers/MeprAppCtrl.php:766
#: app/controllers/MeprGroupsCtrl.php:51
msgid "Groups"
msgstr ""

#: app/controllers/MeprAppCtrl.php:768
#: app/controllers/MeprRulesCtrl.php:79
msgid "Rules"
msgstr ""

#: app/controllers/MeprAppCtrl.php:772
#: app/controllers/MeprAppCtrl.php:1684
#: app/controllers/MeprCoursesCtrl.php:63
msgid "Courses"
msgstr ""

#: app/controllers/MeprAppCtrl.php:827
msgctxt "ui"
msgid "ReadyLaunch™️ General Footer"
msgstr ""

#: app/controllers/MeprAppCtrl.php:828
msgid "Widgets in this area will be shown at the bottom of all ReadyLaunch pages."
msgstr ""

#: app/controllers/MeprAppCtrl.php:839
msgctxt "ui"
msgid "ReadyLaunch™️ Account Footer"
msgstr ""

#: app/controllers/MeprAppCtrl.php:840
msgid "Widgets in this area will be shown at the bottom of ReadyLaunch Account page."
msgstr ""

#: app/controllers/MeprAppCtrl.php:851
msgctxt "ui"
msgid "ReadyLaunch™️ Login Footer"
msgstr ""

#: app/controllers/MeprAppCtrl.php:852
msgid "Widgets in this area will be shown at the bottom of ReadyLaunch Login page."
msgstr ""

#: app/controllers/MeprAppCtrl.php:863
msgctxt "ui"
msgid "ReadyLaunch™️ Registration Footer"
msgstr ""

#: app/controllers/MeprAppCtrl.php:864
msgid "Widgets in this area will be shown at the bottom of ReadyLaunch Registration pages."
msgstr ""

#: app/controllers/MeprAppCtrl.php:921
msgid "We can't display your account form right now. Please come back soon and try again."
msgstr ""

#: app/controllers/MeprAppCtrl.php:947
msgid "the requested resource."
msgstr ""

#: app/controllers/MeprAppCtrl.php:959
msgid "Unauthorized for"
msgstr ""

#: app/controllers/MeprAppCtrl.php:975
msgid "There was a problem with our system. Please come back soon and try again."
msgstr ""

#: app/controllers/MeprAppCtrl.php:1079
msgid "-- Select State --"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1095
msgid "There are no payment methods available that can purchase this product, please contact the site administrator or purchase it separately."
msgstr ""

#: app/controllers/MeprAppCtrl.php:1096
msgid "It looks like your purchase requires %s. No problem! Just click below to switch."
msgstr ""

#: app/controllers/MeprAppCtrl.php:1097
msgid "Switch to %s"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1098
#: app/helpers/MeprAccountHelper.php:82
#: app/lib/MeprBaseGateway.php:651
#: app/lib/MeprBaseGateway.php:707
#: app/views/admin/db/upgrade_needed.php:190
#: app/views/admin/db/upgrade_needed.php:209
#: app/views/admin/members/new_member.php:62
#: app/views/admin/subscriptions/row.php:79
#: app/views/admin/subscriptions/row.php:231
#: app/views/admin/transactions/row.php:171
msgid "Cancel"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1099
msgid "Payment Gateway(s) do not support required order configuration."
msgstr ""

#: app/controllers/MeprAppCtrl.php:1128
msgctxt "ui"
msgid "Done"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1129
msgctxt "ui"
msgid "Today"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1131
msgctxt "ui"
msgid "Jan"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1132
msgctxt "ui"
msgid "Feb"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1133
msgctxt "ui"
msgid "Mar"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1134
msgctxt "ui"
msgid "Apr"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1135
msgctxt "ui"
msgid "May"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1136
msgctxt "ui"
msgid "Jun"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1137
msgctxt "ui"
msgid "Jul"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1138
msgctxt "ui"
msgid "Aug"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1139
msgctxt "ui"
msgid "Sep"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1140
msgctxt "ui"
msgid "Oct"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1141
msgctxt "ui"
msgid "Nov"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1142
msgctxt "ui"
msgid "Dec"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1144
msgctxt "ui"
msgid "Su"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1144
msgctxt "ui"
msgid "Mo"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1144
msgctxt "ui"
msgid "Tu"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1144
msgctxt "ui"
msgid "We"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1144
msgctxt "ui"
msgid "Th"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1144
msgctxt "ui"
msgid "Fr"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1144
msgctxt "ui"
msgid "Sa"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1294
msgid "There was a problem with our system: %s. Please come back soon and try again."
msgstr ""

#: app/controllers/MeprAppCtrl.php:1667
#: app/controllers/MeprAppCtrl.php:1669
msgid "Affiliates"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1675
msgid "Non-Recurring Subscriptions"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1679
msgid "Onboarding"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1680
#: app/views/admin/options/account_login.php:8
msgid "Account Login"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1681
msgid "Add-ons"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1684
#: app/views/admin/support/view.php:123
msgid "MemberPress Courses"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1688
#: app/helpers/MeprOnboardingHelper.php:516
#: app/views/admin/onboarding/features.php:48
msgid "CoachKit™"
msgstr ""

#: app/controllers/MeprAppCtrl.php:1691
#: app/views/admin/header/header.php:10
#: app/views/admin/support/view.php:5
msgid "Support"
msgstr ""

#: app/controllers/MeprBlocksCtrl.php:209
msgid "You are not logged in."
msgstr ""

#: app/controllers/MeprBlocksCtrl.php:213
msgid "You have no Subscriptions yet."
msgstr ""

#: app/controllers/MeprBlocksCtrl.php:263
msgctxt "ui"
msgid "Uh oh, something went wrong. Not a valid Membership form."
msgstr ""

#: app/controllers/MeprBlocksCtrl.php:307
msgid "You are unauthorized to view this content."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:66
#: app/controllers/MeprCheckoutCtrl.php:127
msgid "Notice: %1$s purchase failed. Click <a href=\"%2$s\" target=\"_blank\">here</a> to purchase it separately."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:430
msgid "Sorry, we were unable to find the transaction."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:437
msgid "Sorry, we were unable to find the user."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:496
#: app/controllers/MeprCheckoutCtrl.php:497
#: app/controllers/MeprStripeCtrl.php:208
msgid "The user was unable to be saved."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:511
#: app/controllers/MeprCheckoutCtrl.php:512
msgid "Sorry, we were unable to find the membership."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:620
msgid "Invalid payment method"
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:632
#: app/controllers/MeprCheckoutCtrl.php:1049
msgid "Sorry, we were unable to create a transaction."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:748
msgid "ERROR: Invalid Transaction ID. Use your browser back button and try registering again."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:856
msgid "Sorry, an unknown error occurred."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:1035
msgid "Sorry, we were unable to create a subscription."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:1076
msgid "One of the required products is missing."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:1084
#: app/controllers/MeprPayPalConnectCtrl.php:831
#: app/controllers/MeprPayPalConnectCtrl.php:842
#: app/controllers/MeprStripeCtrl.php:131
#: app/controllers/MeprStripeCtrl.php:382
#: app/gateways/MeprPayPalCommerceGateway.php:992
#: app/gateways/MeprPayPalCommerceGateway.php:1091
#: app/gateways/MeprPayPalStandardGateway.php:1094
#: app/gateways/MeprPayPalStandardGateway.php:1248
#: app/gateways/MeprStripeGateway.php:554
msgid "Product not found"
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:1092
msgid "You don't have access to purchase %s."
msgstr ""

#: app/controllers/MeprCheckoutCtrl.php:1098
msgid "The product %s cannot be purchased at this time."
msgstr ""

#: app/controllers/MeprCoachkitCtrl.php:40
msgid "MemberPress CoachKit™ has been activated successfully!"
msgstr ""

#: app/controllers/MeprCoachkitCtrl.php:104
msgid "CoachKit™ has been installed and activated successfully. Enjoy!"
msgstr ""

#: app/controllers/MeprCoachkitCtrl.php:104
msgid "CoachKit™ could not be installed. Please check your license settings, or contact MemberPress support for help."
msgstr ""

#: app/controllers/MeprCoachkitCtrl.php:109
msgid "CoachKit™ has been activated successfully. Enjoy!"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:46
msgid "Coupon"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:47
#: app/controllers/MeprGroupsCtrl.php:53
#: app/controllers/MeprProductsCtrl.php:66
#: app/controllers/MeprRemindersCtrl.php:213
#: app/controllers/MeprRulesCtrl.php:81
#: app/views/admin/members/list.php:8
#: app/views/admin/subscriptions/list.php:9
#: app/views/admin/transactions/list.php:12
msgid "Add New"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:48
msgid "Add New Coupon"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:49
msgid "Edit Coupon"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:50
msgid "New Coupon"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:51
msgid "View Coupon"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:52
msgid "Search Coupons"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:53
msgid "No Coupons found"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:54
msgid "No Coupons found in Trash"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:55
msgid "Parent Coupon:"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:78
msgid "Code"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:79
#: app/controllers/MeprCouponsCtrl.php:178
#: app/gateways/MeprArtificialGateway.php:630
msgid "Description"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:80
#: app/views/admin/members/new_member.php:165
msgid "Created"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:81
msgid "Discount"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:82
msgid "Mode"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:83
msgid "Starts"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:84
#: app/views/admin/members/new_member.php:179
msgid "Expires"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:85
msgid "Usage Count"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:86
msgid "Applies To"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:112
#: app/controllers/MeprCouponsCtrl.php:117
#: app/views/admin/coupons/form.php:30
#: app/views/admin/coupons/form.php:104
msgid "%"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:124
msgid "Immediately"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:127
#: app/controllers/MeprCouponsCtrl.php:138
#: app/views/admin/members/row.php:140
msgid "Expired"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:135
#: app/controllers/MeprUsersCtrl.php:658
#: app/controllers/MeprUsersCtrl.php:660
#: app/helpers/MeprSubscriptionsHelper.php:92
#: app/helpers/MeprTransactionsHelper.php:155
#: app/helpers/MeprTransactionsHelper.php:246
#: app/views/admin/members/row.php:219
#: app/views/admin/subscriptions/row.php:194
#: app/views/admin/subscriptions/row.php:202
#: app/views/admin/transactions/row.php:40
msgid "Never"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:146
msgid "Unlimited"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:152
msgid "Trial: %1$s days for %2$s"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:154
msgctxt "ui"
msgid "First Payment"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:156
msgctxt "ui"
msgid "Standard"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:158
#: app/views/account/subscriptions.php:87
msgctxt "ui"
msgid "None"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:177
msgid "Coupon Options"
msgstr ""

#: app/controllers/MeprCouponsCtrl.php:395
#: app/controllers/MeprRulesCtrl.php:780
msgid "Please select at least one Membership before saving."
msgstr ""

#: app/controllers/MeprCoursesCtrl.php:107
msgid "Courses has been installed and activated successfully. Enjoy!"
msgstr ""

#: app/controllers/MeprCoursesCtrl.php:107
msgid "Courses could not be installed. Please check your license settings, or contact MemberPress support for help."
msgstr ""

#: app/controllers/MeprCoursesCtrl.php:112
msgid "Courses has been activated successfully. Enjoy!"
msgstr ""

#: app/controllers/MeprDbCtrl.php:45
msgid "MemberPress Member Data Migrate Interval"
msgstr ""

#: app/controllers/MeprDbCtrl.php:100
#: app/controllers/MeprDbCtrl.php:144
msgid "You're unauthorized to access this resource."
msgstr ""

#: app/controllers/MeprDbCtrl.php:113
msgid "Your Upgrade has completed successfully"
msgstr ""

#: app/controllers/MeprDbCtrl.php:126
msgid "No need to upgrade your database"
msgstr ""

#: app/controllers/MeprDbCtrl.php:162
msgid "MemberPress is currently upgrading your database"
msgstr ""

#: app/controllers/MeprDbCtrl.php:182
msgid "No MemberPress database upgrade is in progress"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:87
msgid "Please select an option"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:108
msgid "I no longer need the plugin"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:111
msgid "I'm switching to a different plugin"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:112
msgid "Please share which plugin"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:115
msgid "I couldn't get the plugin to work"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:118
msgid "It's a temporary deactivation"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:121
#: app/controllers/MeprReportsCtrl.php:250
#: app/views/admin/members/row.php:150
msgid "Other"
msgstr ""

#: app/controllers/MeprDeactivationSurveyCtrl.php:122
msgid "Please share the reason"
msgstr ""

#: app/controllers/MeprDrmCtrl.php:194
#: app/controllers/MeprDrmCtrl.php:198
msgid "Sorry, you are not allowed to access this page."
msgstr ""

#: app/controllers/MeprDrmCtrl.php:212
#: app/controllers/MeprDrmCtrl.php:220
#: app/controllers/MeprDrmCtrl.php:274
#: app/controllers/MeprOptionsCtrl.php:209
#: app/controllers/MeprOptionsCtrl.php:313
#: app/controllers/MeprOptionsCtrl.php:321
msgid "An error occurred during activation: %s"
msgstr ""

#: app/controllers/MeprDrmCtrl.php:253
msgid "!"
msgstr ""

#: app/controllers/MeprDrmCtrl.php:282
msgid "An error occurred: %s"
msgstr ""

#: app/controllers/MeprDrmCtrl.php:288
#: app/controllers/MeprDrmCtrl.php:302
#: app/controllers/MeprOnboardingCtrl.php:773
msgid "Invalid request."
msgstr ""

#: app/controllers/MeprDrmCtrl.php:306
msgid "Not allowed."
msgstr ""

#: app/controllers/MeprDrmCtrl.php:401
msgid "Every 10 minutes"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:30
#: app/controllers/MeprSubscriptionsCtrl.php:360
#: app/controllers/MeprSubscriptionsCtrl.php:383
#: app/controllers/MeprSubscriptionsCtrl.php:413
#: app/controllers/MeprSubscriptionsCtrl.php:459
#: app/controllers/MeprSubscriptionsCtrl.php:510
#: app/controllers/MeprTransactionsCtrl.php:465
#: app/controllers/MeprTransactionsCtrl.php:497
#: app/controllers/MeprTransactionsCtrl.php:525
#: app/controllers/MeprTransactionsCtrl.php:558
#: app/controllers/MeprTransactionsCtrl.php:622
#: app/controllers/MeprTransactionsCtrl.php:650
#: app/controllers/MeprUpdateCtrl.php:883
msgid "You do not have access."
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:34
msgid "Email couldn't be set to default"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:65
msgid "You do not have access to send a test email."
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:69
msgid "Can't send your email ... refresh the page and try it again."
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:115
msgid "John"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:116
msgid "Doe"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:117
msgid "John Doe"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:119
msgid "111 Cool Avenue"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:120
msgid "New York, NY 10005"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:121
msgid "United States"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:122
msgid "User Meta Field: $1"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:123
#: app/controllers/MeprEmailsCtrl.php:125
msgid "Bronze Edition"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:130
#: app/controllers/MeprEmailsCtrl.php:137
msgid "Credit Card (Stripe)"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:138
msgid "%s / month"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:152
#: app/emails/MeprAdminSubExpiresReminderEmail.php:43
#: app/emails/MeprUserSubExpiresReminderEmail.php:40
#: app/helpers/MeprRemindersHelper.php:73
msgid "Subscription Expiring"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:153
msgid "Subscription Expiring in 2 Days"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:158
#: app/controllers/MeprTransactionsCtrl.php:585
#: app/integrations/avalara/Integration.php:94
#: app/integrations/stripe-tax/Integration.php:223
#: app/views/admin/reports/month_table.php:15
#: app/views/admin/reports/skeleton_table.php:15
#: app/views/admin/reports/year_table.php:15
msgid "Tax"
msgstr ""

#: app/controllers/MeprEmailsCtrl.php:177
msgid "Your test email was successfully sent."
msgstr ""

#: app/controllers/MeprExportCtrl.php:55
#: app/controllers/MeprExportCtrl.php:71
#: app/controllers/MeprExportCtrl.php:87
msgid "Membership Data"
msgstr ""

#: app/controllers/MeprExportCtrl.php:59
msgid "Address"
msgstr ""

#: app/controllers/MeprExportCtrl.php:75
#: app/views/admin/taxes/vat_profile_fields.php:40
msgid "VAT Number"
msgstr ""

#: app/controllers/MeprExportCtrl.php:91
msgid "Geo Location Country"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:52
#: js/blocks/pro-pricing-table/edit.js:57
#: js/build/blocks.js:1
msgid "Group"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:54
msgid "Add New Group"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:55
msgid "Edit Group"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:56
msgid "New Group"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:57
msgid "View Group"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:58
msgid "Search Group"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:59
msgid "No Group found"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:60
msgid "No Group found in Trash"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:61
msgid "Parent Group:"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:171
#: app/controllers/MeprProductsCtrl.php:148
#: app/controllers/MeprRulesCtrl.php:172
msgid "ID"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:172
msgid "Group Title"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:173
#: app/controllers/MeprOptionsCtrl.php:191
#: app/controllers/MeprProductsCtrl.php:151
#: app/views/admin/options/custom_fields_options.php:12
#: app/views/admin/products/advanced.php:80
#: app/views/admin/products/advanced.php:95
msgid "URL"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:174
msgid "Memberships in Group"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:246
msgid "Group Options"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:247
#: app/controllers/MeprProductsCtrl.php:230
msgid "Custom Page Template"
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:449
msgid "Unknown error has occured."
msgstr ""

#: app/controllers/MeprGroupsCtrl.php:469
msgid "This membership already belongs to another group. If you assign it to this group, it will be removed from the other."
msgstr ""

#: app/controllers/MeprLoginCtrl.php:43
msgid "Logout"
msgstr ""

#: app/controllers/MeprLoginCtrl.php:47
#: app/controllers/MeprRulesCtrl.php:523
#: app/controllers/MeprRulesCtrl.php:994
#: app/helpers/MeprOnboardingHelper.php:595
#: app/lib/activation.php:19
#: app/lib/MeprAccountLinksWidget.php:76
#: app/lib/MeprLoginWidget.php:87
#: app/lib/MeprLoginWidget.php:102
#: app/views/admin/options/form.php:68
#: app/views/admin/readylaunch/options.php:135
msgid "Login"
msgstr ""

#: app/controllers/MeprLoginCtrl.php:511
msgid "An Unknown Error Occurred"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:69
msgid "MemberPress Member Data Update Interval"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:139
#: app/controllers/MeprSubscriptionsCtrl.php:646
#: app/controllers/MeprTransactionsCtrl.php:579
#: app/lib/MeprMembersTable.php:79
#: app/lib/MeprSubscriptionsTable.php:122
#: app/lib/MeprSubscriptionsTable.php:129
#: app/lib/MeprTransactionsTable.php:80
msgid "Id"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:141
#: app/lib/MeprMembersTable.php:75
#: app/lib/MeprSubscriptionsTable.php:120
#: app/lib/MeprSubscriptionsTable.php:127
#: app/lib/MeprTransactionsTable.php:78
#: app/views/admin/members/new_member.php:16
msgid "Username"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:142
#: app/controllers/MeprOptionsCtrl.php:190
#: app/lib/MeprMembersTable.php:76
#: app/views/admin/members/new_member.php:24
#: app/views/admin/options/custom_fields_options.php:11
msgid "Email"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:143
#: app/controllers/MeprTransactionsCtrl.php:582
#: app/views/admin/members/new_member.php:142
msgid "Status"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:144
#: app/controllers/MeprSubscriptionsCtrl.php:652
#: app/controllers/MeprTransactionsCtrl.php:587
msgid "Name"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:157
msgid "Inactive Memberships"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:158
#: app/controllers/MeprUsersCtrl.php:583
msgid "Last Login"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:159
msgid "Logins"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:160
msgid "Value"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:161
#: app/controllers/MeprUsersCtrl.php:582
msgid "Registered"
msgstr ""

#: app/controllers/MeprMembersCtrl.php:272
msgid "Your new member was created successfully."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:401
msgid "The username field can't be blank."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:405
msgid "This username is already taken."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:409
msgid "The username must be valid."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:413
msgid "The email field can't be blank."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:417
msgid "This email is already being used by another user."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:421
msgid "A valid email must be entered."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:426
msgid "The transaction amount must be set."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:430
msgid "The transaction amount must be a number."
msgstr ""

#: app/controllers/MeprMembersCtrl.php:434
#: app/controllers/MeprTransactionsCtrl.php:391
msgid "The Transaction Number is required, and must contain only letters, numbers, underscores and hyphens."
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:35
msgid "The migration has not yet completed, are you sure you want to leave this page?"
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:36
msgid "Migration complete"
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:48
msgid "Migrator"
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:80
msgid "Migrator not found"
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:118
msgid "Migrate from LearnDash to MemberPress Courses?"
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:119
msgid "We noticed that you have some LearnDash courses that could be migrated to MemberPress Courses automatically. Click the button below to get started."
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:121
msgid "Let's Do It"
msgstr ""

#: app/controllers/MeprMigratorCtrl.php:122
#: app/views/admin/admin-notification.php:6
#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:197
msgid "Dismiss"
msgstr ""

#: app/controllers/MeprNHWCtrl.php:109
msgid "MemberPress: WARNING! Your License has Expired."
msgstr ""

#: app/controllers/MeprNHWCtrl.php:111
#: app/controllers/MeprNHWCtrl.php:128
msgid "The license that came with your No Hassle Membership & Course Platform expired at the end of 2023. Without an active license key, your frontend will remain unaffected and your members will still have access. However, you will no longer be able to access the MemberPress backend, update courses, manage memberships, add new members etc. To prevent this from happening, please purchase a new license."
msgstr ""

#: app/controllers/MeprNHWCtrl.php:112
#: app/helpers/MeprDrmHelper.php:411
#: app/helpers/MeprDrmHelper.php:479
msgid "Let us know if you need assistance."
msgstr ""

#: app/controllers/MeprNHWCtrl.php:113
#: app/controllers/MeprNHWCtrl.php:125
#: app/helpers/MeprDrmHelper.php:412
#: app/helpers/MeprDrmHelper.php:428
#: app/helpers/MeprDrmHelper.php:480
#: app/helpers/MeprDrmHelper.php:492
msgid "Critical"
msgstr ""

#: app/controllers/MeprNHWCtrl.php:118
#: app/controllers/MeprNHWCtrl.php:133
#: app/controllers/MeprNHWCtrl.php:202
msgid "<a href=\"https://buy.nohassleplatform.com/nhp-pro-update/\">Visit No Hassle Websites for Exclusive Pricing</a>."
msgstr ""

#: app/controllers/MeprNHWCtrl.php:119
#: app/controllers/MeprNHWCtrl.php:134
#: app/controllers/MeprNHWCtrl.php:203
#: app/helpers/MeprDrmHelper.php:402
#: app/helpers/MeprDrmHelper.php:418
#: app/helpers/MeprDrmHelper.php:434
#: app/helpers/MeprDrmHelper.php:486
#: app/helpers/MeprDrmHelper.php:501
msgid "<a href=\""
msgstr ""

#: app/controllers/MeprNHWCtrl.php:120
#: app/controllers/MeprNHWCtrl.php:135
#: app/helpers/MeprDrmHelper.php:403
#: app/helpers/MeprDrmHelper.php:419
#: app/helpers/MeprDrmHelper.php:435
#: app/helpers/MeprDrmHelper.php:487
#: app/helpers/MeprDrmHelper.php:502
#: app/views/admin/drm/nhw/notices/medium_warning.php:12
#: app/views/admin/drm/nhw/notices/medium_warning.php:18
#: app/views/admin/drm/notices/low_warning.php:17
#: app/views/admin/drm/notices/medium_warning.php:12
#: app/views/admin/drm/notices/medium_warning.php:18
msgid "That’s it!"
msgstr ""

#: app/controllers/MeprNHWCtrl.php:126
#: app/helpers/MeprDrmHelper.php:424
#: app/helpers/MeprDrmHelper.php:493
msgid "ALERT! MemberPress Backend is Deactivated"
msgstr ""

#: app/controllers/MeprNHWCtrl.php:137
#: app/helpers/MeprDrmHelper.php:504
msgid "We’re here to help you get things back up and running. Let us know if you need assistance."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:68
#: app/views/admin/onboarding/license.php:12
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:151
msgid "Activate License"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:74
msgid "Enable Features"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:80
msgid "Create or Select Content"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:86
#: app/views/admin/onboarding/membership.php:10
#: app/views/admin/onboarding/membership.php:42
msgid "Create Membership"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:92
msgid "Protect Content"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:98
msgid "Payment Options"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:104
msgid "Finish Setup"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:110
#: app/helpers/MeprAppHelper.php:229
#: app/helpers/MeprTransactionsHelper.php:445
#: app/views/admin/members/row.php:134
#: app/views/admin/reports/month_table.php:11
#: app/views/admin/reports/skeleton_table.php:11
#: app/views/admin/reports/year_table.php:11
#: app/views/admin/transactions/row.php:168
#: app/views/admin/transactions/search_box.php:14
#: app/views/admin/transactions/trans_form.php:72
msgid "Complete"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:159
msgid "Are you sure? MemberPress will not be functional if this License Key is deactivated."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:162
msgid "An error occurred"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:166
#: app/controllers/MeprOnboardingCtrl.php:461
#: app/views/admin/onboarding/parts/content_popup.php:23
msgid "Course Name"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:167
#: app/controllers/MeprOnboardingCtrl.php:461
#: app/views/admin/onboarding/parts/content_popup.php:29
#: app/views/admin/readylaunch/pricing.php:25
#: app/views/admin/readylaunch/pricing.php:29
msgid "Page Title"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:168
#: app/helpers/MeprOnboardingHelper.php:468
#: app/views/admin/onboarding/content-search-results.php:13
#: app/views/admin/onboarding/parts/content_popup.php:9
msgid "Course"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:169
#: app/helpers/MeprOnboardingHelper.php:468
msgid "Page"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:170
msgid "This may take a couple of minutes"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:174
msgid "An error occurred when installing an add-on, please download and install the add-ons manually."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:453
#: app/controllers/MeprOnboardingCtrl.php:891
#: app/controllers/MeprOnboardingCtrl.php:932
#: app/controllers/MeprOnboardingCtrl.php:1302
#: app/controllers/MeprOnboardingCtrl.php:1352
msgid "Post not found."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:777
msgid "Invalid content."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:870
msgid "Price must be greater than zero for the Billing."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:902
#: app/lib/MeprUsage.php:211
#: app/models/MeprProduct.php:478
#: app/views/admin/products/price_box.php:73
msgid "Sign Up"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:1287
#: app/models/MeprRule.php:376
msgid "A Single %s"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:1375
#: app/controllers/MeprOptionsCtrl.php:491
msgid "The correct edition of MemberPress has been installed successfully."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:1377
#: app/controllers/MeprOptionsCtrl.php:493
msgid "Failed to install the correct edition of MemberPress, please download it from memberpress.com and install it manually."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:1381
#: app/controllers/MeprOptionsCtrl.php:497
msgid "License data not found"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:1399
#: app/controllers/MeprOptionsCtrl.php:457
msgid "Failed to get filesystem access"
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:1455
#: app/views/admin/onboarding/parts/finish.php:142
msgid "Unable to install. Please download and install manually."
msgstr ""

#: app/controllers/MeprOnboardingCtrl.php:1522
#: app/controllers/MeprPayPalConnectCtrl.php:821
#: app/controllers/MeprPayPalConnectCtrl.php:866
#: app/controllers/MeprStripeCtrl.php:125
#: app/controllers/MeprStripeCtrl.php:155
#: app/controllers/MeprStripeCtrl.php:571
#: app/controllers/MeprStripeCtrl.php:632
msgid "Invalid payment gateway"
msgstr ""

#. Translators: %1$s open link tag, %2$s: close link tag.
#: app/controllers/MeprOnboardingCtrl.php:1619
msgid "Hey, it looks like you started setting up MemberPress but didn't finish, %1$sclick here to continue%2$s."
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:68
msgid "Database Was Upgraded"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:76
msgid "Tax rates have been cleared"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:130
#: app/views/admin/options/options_saved.php:4
msgid "Options saved."
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:179
#: app/views/admin/options/custom_fields_row.php:7
#: app/views/admin/options/gateway.php:13
msgid "Name:"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:180
#: app/views/admin/options/custom_fields_row.php:10
msgid "Type:"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:181
#: app/views/admin/options/custom_fields_row.php:13
msgid "Default Value(s):"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:182
#: app/views/admin/options/custom_fields_row.php:20
msgid "Show at Signup"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:183
#: app/views/admin/options/custom_fields_row.php:23
msgid "Show in Account"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:184
#: app/views/admin/options/custom_fields_row.php:26
msgid "Required"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:185
#: app/views/admin/options/custom_fields_options.php:10
msgid "Text"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:186
#: app/views/admin/options/custom_fields_options.php:15
msgid "Textarea"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:187
#: app/views/admin/options/custom_fields_options.php:16
msgid "Checkbox"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:188
#: app/views/admin/options/custom_fields_options.php:17
msgid "Dropdown"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:189
#: app/views/admin/options/custom_fields_options.php:18
msgid "Multi-Select"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:192
#: app/views/admin/options/custom_fields_options.php:13
msgid "Phone"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:193
#: app/views/admin/options/custom_fields_options.php:19
msgid "Radio Buttons"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:194
#: app/views/admin/options/custom_fields_options.php:20
msgid "Checkboxes"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:195
#: app/views/admin/options/custom_fields_options.php:21
msgid "File Upload"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:196
#: app/controllers/MeprReportsCtrl.php:128
#: app/views/admin/options/custom_fields_options.php:14
#: app/views/admin/reports/month_table.php:8
#: app/views/admin/reports/skeleton_table.php:8
#: app/views/admin/reports/year_table.php:8
#: js/blocks/subscriptions/edit.js:23
#: js/build/blocks.js:1
msgid "Date"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:197
#: app/views/admin/options/custom_fields_row.php:39
#: app/views/admin/options/custom_fields_row.php:52
msgid "Option Name:"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:198
#: app/views/admin/options/custom_fields_row.php:42
#: app/views/admin/options/custom_fields_row.php:55
msgid "Option Value:"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:199
msgid "Add Option"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:203
msgid "Are you sure you want to delete this Tax Rate?"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:204
msgid "WARNING: Do not remove this Payment Method if you have active subscriptions using it. Doing so will prevent you from being notified of recurring payments for those subscriptions, which means your members will lose access to their paid content. Are you sure you want to delete this Payment Method?"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:210
#: app/views/admin/drm/modal.php:120
#: app/views/admin/drm/modal_fee.php:70
#: app/views/admin/drm/nhw/modal.php:75
msgid "Invalid response."
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:211
#: app/views/admin/drm/modal.php:135
#: app/views/admin/drm/modal_fee.php:85
#: app/views/admin/drm/nhw/modal.php:90
msgid "Ajax error."
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:213
msgid "Are you sure? MemberPress will not be functional on %s if this License Key is deactivated."
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:214
#: app/controllers/MeprOptionsCtrl.php:417
#: app/controllers/MeprOptionsCtrl.php:425
msgid "An error occurred during deactivation: %s"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:219
msgid "Unable to verify Stripe Tax status"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:231
msgid "Copy to Clipboard"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:232
msgid "Copied!"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:233
msgid "Oops, Copy Failed!"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:271
msgid "Unauthorized"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:280
msgid "No gateways were found"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:360
msgid "The license information is not available, try refreshing the page."
msgstr ""

#. Translators: %1$s: the product name, %2$s: open link tag, %3$s: close link tag.
#: app/controllers/MeprOptionsCtrl.php:391
msgid "This License Key has expired, but you have an active license for %1$s, %2$sclick here%3$s to activate using this license instead."
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:590
msgid "Unable to create domain association folder in domain root"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:594
msgid "Unable to copy the domain association file"
msgstr ""

#: app/controllers/MeprOptionsCtrl.php:662
msgid "Unable to activate payment method `%s`"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:147
#: app/controllers/MeprPayPalConnectCtrl.php:726
#: app/helpers/MeprOptionsHelper.php:63
msgid "PayPal"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:167
msgid "MemberPress - PayPal Connect Security"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:214
#: app/controllers/MeprStripeConnectCtrl.php:170
#: app/controllers/MeprStripeConnectCtrl.php:333
msgid "MemberPress Security Notice"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:215
msgid "Your current PayPal payment connection is out of date and may become insecure. Please click the button below to upgrade your PayPal payment method now."
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:216
msgid "Upgrade PayPal Standard to Fix this Error Now"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:245
msgid "Your MemberPress PayPal Connection is incomplete"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:246
msgid "Your PayPal connection in MemberPress must be connected in order to accept PayPal payments. Please click the button below to finish connecting your PayPal payment method now."
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:247
msgid "Connect PayPal Payment Method"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:616
#: app/controllers/MeprPayPalConnectCtrl.php:665
msgid "You have disconnected your PayPal. You should login to your PayPal account and go to Developer settings to delete the app created by this gateway unless you have active recurring subscriptions that were created with this gateway"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:692
#: app/controllers/MeprStripeConnectCtrl.php:355
msgid "Sorry, updating your credentials failed. (security)"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:696
msgid "You do not have sufficient permission to perform this operation"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:815
#: app/controllers/MeprReadyLaunchCtrl.php:307
#: app/controllers/MeprStripeCtrl.php:115
msgid "Transaction not found"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:870
#: app/controllers/MeprStripeCtrl.php:545
#: app/controllers/MeprStripeCtrl.php:606
#: app/lib/migrators/MeprMigratorLearnDash.php:70
msgid "Bad request"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:917
msgid "You have reverted PayPal to legacy gateway"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:930
msgid "MemberPress is securely connected to PayPal"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:933
#: app/controllers/MeprPayPalConnectCtrl.php:949
#: app/controllers/MeprStripeConnectCtrl.php:290
#: app/controllers/MeprStripeConnectCtrl.php:306
msgid "Security"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:938
msgid "Your MemberPress PayPal connection is complete and secure."
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:946
msgid "MemberPress is not securely connected to PayPal"
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:954
msgid "Your current PayPal payment connection is out of date and may become insecure or stop working. Please click the button below to re-connect your PayPal payment method now."
msgstr ""

#: app/controllers/MeprPayPalConnectCtrl.php:956
msgid "Re-connect PayPal Payments to Fix this Error Now"
msgstr ""

#: app/controllers/MeprPopupCtrl.php:110
msgid "An unknown error occurred."
msgstr ""

#: app/controllers/MeprPopupCtrl.php:144
#: app/controllers/MeprReportsCtrl.php:85
msgid "Forbidden"
msgstr ""

#: app/controllers/MeprPopupCtrl.php:148
msgid "Must specify a popup"
msgstr ""

#: app/controllers/MeprPopupCtrl.php:154
msgid "Invalid popup"
msgstr ""

#: app/controllers/MeprPopupCtrl.php:159
msgid "The popup was successfully delayed"
msgstr ""

#: app/controllers/MeprPopupCtrl.php:162
msgid "The popup was successfully stopped"
msgstr ""

#: app/controllers/MeprPostStatesCtrl.php:32
#: app/views/admin/options/form.php:59
msgid "MemberPress Thank You Page"
msgstr ""

#: app/controllers/MeprPostStatesCtrl.php:36
#: app/views/admin/options/form.php:63
msgid "MemberPress Account Page"
msgstr ""

#: app/controllers/MeprPostStatesCtrl.php:40
#: app/views/admin/options/form.php:67
msgid "MemberPress Login Page"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:65
#: app/controllers/MeprReportsCtrl.php:239
#: app/controllers/MeprSubscriptionsCtrl.php:650
#: app/controllers/MeprTransactionsCtrl.php:583
#: app/models/MeprRule.php:221
#: app/views/admin/members/new_member.php:70
#: app/views/admin/onboarding/membership.php:18
msgid "Membership"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:67
msgid "Add New Membership"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:68
msgid "Edit Membership"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:69
msgid "New Membership"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:70
msgid "View Membership"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:71
msgid "Search Membership"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:72
msgid "No Membership found"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:73
msgid "No Membership found in Trash"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:74
msgid "Parent Membership:"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:104
msgctxt "taxonomy general name"
msgid "Categories"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:105
msgctxt "taxonomy singular name"
msgid "Category"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:106
msgid "Search Categories"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:107
msgid "All Categories"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:108
msgid "Parent Category"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:109
msgid "Parent Category:"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:110
msgid "Edit Category"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:111
msgid "Update Category"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:112
msgid "Add New Category"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:113
msgid "New Category Name"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:114
#: app/controllers/MeprProductsCtrl.php:1230
msgid "Categories"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:115
msgid "Separate Categories with commas"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:116
msgid "Add or remove Categories"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:117
msgid "Choose from the most used"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:118
msgid "Popular Categories"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:119
msgid "Not Found"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:120
msgid "No Categories"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:121
msgid "Categories list"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:122
msgid "Categories list navigation"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:149
msgid "Membership Title"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:150
#: app/controllers/MeprSubscriptionsCtrl.php:651
msgid "Terms"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:228
msgid "Membership Terms"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:232
msgid "Membership Options"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:315
#: app/helpers/MeprOnboardingHelper.php:599
#: app/lib/activation.php:11
#: app/models/MeprOptions.php:844
msgid "Your subscription has been set up successfully."
msgstr ""

#: app/controllers/MeprProductsCtrl.php:664
#: app/controllers/MeprProductsCtrl.php:738
msgid "There was a problem with our payment system. Please come back soon and try again."
msgstr ""

#: app/controllers/MeprProductsCtrl.php:704
msgid "%1$sYou have already subscribed to this item. %2$sClick here to access it%3$s"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:785
#: app/helpers/MeprProductsHelper.php:67
#: app/helpers/MeprProductsHelper.php:77
msgid "Remove Benefit"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:1016
msgid "An unknown error has occurred"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:1022
msgid "Please save membership first to see the Price here."
msgstr ""

#: app/controllers/MeprProductsCtrl.php:1150
msgid "Show all %s"
msgstr ""

#: app/controllers/MeprProductsCtrl.php:1160
msgid "Filter"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:81
msgid "Please select group"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:87
msgid "No group found"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:156
msgid "Please select membership"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:162
msgid "No membership found"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:410
msgid "ReadyLaunch™"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:500
msgid "Welcome Image should be uploaded if Show Welcome Image button is checked"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:736
#: app/models/MeprUser.php:1170
#: app/models/MeprUser.php:1257
msgid "You must enter both your First and Last name"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:742
#: app/models/MeprUser.php:1174
msgid "You must enter a valid email address"
msgstr ""

#: app/controllers/MeprReadyLaunchCtrl.php:748
#: app/models/MeprUser.php:1180
msgid "This email is already in use by another member"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:135
msgid "Reminder Title"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:136
msgid "Send Notice to Admin"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:137
msgid "Send Reminder to Member"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:211
msgid "Reminders"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:212
msgid "Reminder"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:214
msgid "Add New Reminder"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:215
msgid "Edit Reminder"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:216
msgid "New Reminder"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:217
msgid "View Reminder"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:218
msgid "Search Reminders"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:219
msgid "No Reminders found"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:220
msgid "No Reminders found in Trash"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:221
msgid "Parent Reminder:"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:245
msgid "Trigger"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:252
#: app/views/admin/options/form.php:24
msgid "Emails"
msgstr ""

#: app/controllers/MeprRemindersCtrl.php:391
msgid "MemberPress Reminders Worker"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:132
#: app/controllers/MeprReportsCtrl.php:328
#: app/controllers/MeprReportsCtrl.php:473
msgid "Completed"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:141
#: app/controllers/MeprReportsCtrl.php:337
#: app/controllers/MeprReportsCtrl.php:482
#: app/helpers/MeprAppHelper.php:225
#: app/helpers/MeprAppHelper.php:238
#: app/helpers/MeprAppHelper.php:794
#: app/helpers/MeprTransactionsHelper.php:446
#: app/views/admin/members/row.php:107
#: app/views/admin/reports/month_table.php:9
#: app/views/admin/reports/skeleton_table.php:9
#: app/views/admin/reports/year_table.php:9
#: app/views/admin/subscriptions/form.php:72
#: app/views/admin/subscriptions/row.php:225
#: app/views/admin/subscriptions/search_box.php:13
#: app/views/admin/transactions/row.php:165
#: app/views/admin/transactions/search_box.php:13
#: app/views/admin/transactions/trans_form.php:73
msgid "Pending"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:150
#: app/controllers/MeprReportsCtrl.php:346
#: app/controllers/MeprReportsCtrl.php:491
#: app/helpers/MeprAppHelper.php:227
#: app/helpers/MeprTransactionsHelper.php:447
#: app/views/admin/reports/month_table.php:10
#: app/views/admin/reports/skeleton_table.php:10
#: app/views/admin/reports/year_table.php:10
#: app/views/admin/transactions/row.php:166
#: app/views/admin/transactions/search_box.php:16
#: app/views/admin/transactions/trans_form.php:74
msgid "Failed"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:159
#: app/controllers/MeprReportsCtrl.php:355
#: app/controllers/MeprReportsCtrl.php:500
#: app/controllers/MeprTransactionsCtrl.php:412
#: app/helpers/MeprAppHelper.php:231
#: app/helpers/MeprTransactionsHelper.php:448
#: app/views/admin/reports/month_table.php:12
#: app/views/admin/reports/month_table.php:14
#: app/views/admin/reports/skeleton_table.php:12
#: app/views/admin/reports/skeleton_table.php:14
#: app/views/admin/reports/year_table.php:12
#: app/views/admin/reports/year_table.php:14
#: app/views/admin/transactions/row.php:167
#: app/views/admin/transactions/search_box.php:15
#: app/views/admin/transactions/trans_form.php:75
msgid "Refunded"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:186
#: app/controllers/MeprReportsCtrl.php:382
#: app/controllers/MeprReportsCtrl.php:526
msgid "Completed:"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:194
#: app/controllers/MeprReportsCtrl.php:390
#: app/controllers/MeprReportsCtrl.php:534
msgid "Pending:"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:202
#: app/controllers/MeprReportsCtrl.php:398
#: app/controllers/MeprReportsCtrl.php:542
msgid "Failed:"
msgstr ""

#: app/controllers/MeprReportsCtrl.php:210
#: app/controllers/MeprReportsCtrl.php:406
#: app/controllers/MeprReportsCtrl.php:550
msgid "Refunded:"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:80
msgid "Rule"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:82
msgid "Add New Rule"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:83
msgid "Edit Rule"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:84
msgid "New Rule"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:85
#: js/blocks/protected-content/index.js:71
#: js/build/blocks.js:1
msgid "View Rule"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:86
msgid "Search Rules"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:87
msgid "No Rules found"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:88
msgid "No Rules found in Trash"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:89
msgid "Parent Rule:"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:117
msgid "All Content: "
msgstr ""

#: app/controllers/MeprRulesCtrl.php:173
#: js/blocks/subscriptions/edit.js:24
#: js/build/blocks.js:1
msgid "Title"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:174
msgid "Type"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:175
msgid "Content"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:177
msgid "Drip time"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:178
msgid "Expiration time"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:219
#: app/helpers/MeprRulesHelper.php:277
msgid "Except"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:226
#: app/controllers/MeprRulesCtrl.php:238
msgid "%1$s %2$s after %3$s"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:232
#: app/controllers/MeprRulesCtrl.php:244
msgid "--"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:552
msgid "Content & Access"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:553
msgid "Drip / Expiration"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:554
#: app/views/admin/options/form.php:105
#: js/blocks/protected-content/index.js:94
#: js/build/blocks.js:1
msgid "Unauthorized Access"
msgstr ""

#: app/controllers/MeprRulesCtrl.php:703
#: app/controllers/MeprRulesCtrl.php:723
msgid "Error"
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:171
msgid "Your current Stripe payment connection is out of date and may become insecure. Please click the button below to re-connect your Stripe payment method now."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:172
#: app/controllers/MeprStripeConnectCtrl.php:313
#: app/controllers/MeprStripeConnectCtrl.php:337
msgid "Re-connect Stripe Payments to Fix this Error Now"
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:206
msgid "Your MemberPress.com account and Stripe gateway have been disconnected. Please re-connect the Stripe gateway by clicking the button below in order to start taking payments again."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:207
msgid "Re-connect Stripe"
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:231
msgid "Your payment method was successfully connected to your Stripe account."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:235
msgid "Your payment method's Stripe Connect keys were successfully updated."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:239
msgid "Your Stripe tokens were successfully refreshed."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:243
msgid "You successfully disconnected this payment method from your Stripe account."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:271
msgid "MemberPress - Stripe Connect Security"
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:287
msgid "MemberPress is securely connected to Stripe"
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:295
msgid "Your MemberPress Stripe connection is complete and secure."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:303
msgid "MemberPress is not securely connected to Stripe"
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:311
msgid "Your current Stripe payment connection is out of date and may become insecure or stop working. Please click the button below to re-connect your Stripe payment method now."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:335
msgid "Your current Stripe payment connection is out of date and may become insecure. Please click the link below to re-connect your Stripe payment method now."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:365
msgid "Sorry, updating your credentials failed. (pmt)"
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:379
msgid "Sorry, this only works with Stripe."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:417
#: app/controllers/MeprStripeConnectCtrl.php:422
#: app/controllers/MeprStripeConnectCtrl.php:447
msgid "Sorry, the refresh failed."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:492
#: app/controllers/MeprStripeConnectCtrl.php:497
#: app/controllers/MeprStripeConnectCtrl.php:510
msgid "Sorry, the disconnect failed."
msgstr ""

#: app/controllers/MeprStripeConnectCtrl.php:609
msgid "You successfully stored a new payment method yo."
msgstr ""

#: app/controllers/MeprStripeCtrl.php:95
#: app/gateways/MeprStripeGateway.php:2753
msgid "An error occurred, please DO NOT submit the form again as you may be double charged. Please contact us for further assistance instead."
msgstr ""

#: app/controllers/MeprStripeCtrl.php:137
#: app/gateways/MeprStripeGateway.php:3631
msgid "User not found"
msgstr ""

#: app/controllers/MeprStripeCtrl.php:216
msgid "Sorry, we were unable to find the product."
msgstr ""

#: app/controllers/MeprStripeCtrl.php:287
#: app/controllers/MeprStripeCtrl.php:369
#: app/controllers/MeprStripeCtrl.php:396
#: app/controllers/MeprStripeCtrl.php:559
#: app/controllers/MeprStripeCtrl.php:620
#: app/gateways/MeprPayPalCommerceGateway.php:1002
#: app/gateways/MeprPayPalCommerceGateway.php:2026
#: app/gateways/MeprPayPalCommerceGateway.php:2042
#: app/gateways/MeprPayPalStandardGateway.php:1104
#: app/gateways/MeprStripeGateway.php:539
#: app/gateways/MeprStripeGateway.php:565
#: app/gateways/MeprStripeGateway.php:675
#: app/gateways/MeprStripeGateway.php:5081
#: app/gateways/MeprStripeGateway.php:5107
#: app/gateways/MeprStripeGateway.php:5128
msgid "Subscription not found"
msgstr ""

#: app/controllers/MeprStripeCtrl.php:454
msgid "PaymentIntent not found"
msgstr ""

#. Translators: %1$s: product name, %2$s: open link tag, %3$s: close link tag.
#: app/controllers/MeprStripeCtrl.php:521
msgid "You are already subscribed to %1$s, %2$sclick here%3$s to view your subscriptions."
msgstr ""

#: app/controllers/MeprStripeCtrl.php:531
#: app/models/MeprProduct.php:531
msgctxt "ui"
msgid "You don't have access to purchase this item."
msgstr ""

#: app/controllers/MeprStripeCtrl.php:549
#: app/controllers/MeprStripeCtrl.php:610
msgid "Sorry, you must be logged in to do this."
msgstr ""

#: app/controllers/MeprStripeCtrl.php:565
#: app/controllers/MeprStripeCtrl.php:626
msgid "This subscription is for another user."
msgstr ""

#: app/controllers/MeprStripeCtrl.php:639
msgid "The payment setup was unsuccessful, please try another payment method."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:130
msgid "A subscription was created successfully."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:132
msgid "There was a problem creating the subscription"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:161
msgid "The subscription was updated successfully."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:163
msgid "There was a problem updating the subscription"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:233
msgid "The Subscription ID must contain only letters, numbers, underscores and hyphens"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:236
msgid "The Subscription ID is required"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:239
msgid "The username is required"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:243
#: app/controllers/MeprSubscriptionsCtrl.php:250
#: app/controllers/MeprTransactionsCtrl.php:329
#: app/controllers/MeprTransactionsCtrl.php:337
msgid "You must enter a valid username or email address"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:254
msgid "Membership is required"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:258
msgid "A valid membership is required"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:262
msgid "The sub-total is required"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:265
msgid "The sub-total must be a number"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:268
msgid "The trial days must be a number"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:284
msgid "A Subscription should be cancelled (at the Gateway or here) by you, or by the Member on their Account page before being removed. Deleting an Active Subscription can cause future recurring payments not to be tracked properly. Are you sure you want to delete this Subscription?"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:285
msgid "The Subscription could not be deleted. Please try again later."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:286
msgid "This will cancel all future payments for this subscription. Are you sure you want to cancel this Subscription?"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:287
#: app/gateways/MeprStripeGateway.php:2648
msgid "The Subscription could not be cancelled here. Please login to your gateway's virtual terminal to cancel it."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:288
msgid "The Subscription was successfully cancelled."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:289
#: app/helpers/MeprAppHelper.php:242
#: app/views/admin/members/row.php:106
#: app/views/admin/subscriptions/form.php:74
#: app/views/admin/subscriptions/row.php:228
msgid "Stopped"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:290
msgid ""
"This will stop all payments for this subscription until the user logs into their account and resumes.\n"
"\n"
"Are you sure you want to pause this Subscription?"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:291
msgid "The Subscription could not be paused here. Please login to your gateway's virtual terminal to pause it."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:292
msgid "The Subscription was successfully paused."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:293
#: app/helpers/MeprAppHelper.php:244
#: app/helpers/MeprAppHelper.php:795
#: app/views/admin/members/row.php:108
#: app/views/admin/subscriptions/form.php:73
#: app/views/admin/subscriptions/row.php:227
#: app/views/admin/subscriptions/search_box.php:15
msgid "Paused"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:294
msgid ""
"This will resume payments for this subscription.\n"
"\n"
"Are you sure you want to resume this Subscription?"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:295
msgid "The Subscription could not be resumed here. Please login to your gateway's virtual terminal to resume it."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:296
msgid "The Subscription was successfully resumed."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:297
msgid "The Subscription could not be resumed automatically because the customer needs to authorize the transaction. Do you want to send the customer an email with a link to pay the invoice?"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:298
msgid "The email has been sent to the customer. The Subscription will resume when the invoice has been paid."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:299
msgid ""
"An error occurred sending the email to the customer:\n"
"\n"
"%s"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:300
msgid "The response from the server was invalid or malformed"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:301
msgid "Ajax error"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:302
#: app/helpers/MeprAppHelper.php:240
#: app/views/admin/members/row.php:105
#: app/views/admin/readylaunch/options.php:66
#: app/views/admin/subscriptions/form.php:71
#: app/views/admin/subscriptions/row.php:226
#: app/views/admin/subscriptions/search_box.php:14
msgid "Enabled"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:332
#: app/controllers/MeprSubscriptionsCtrl.php:340
#: app/controllers/MeprTransactionsCtrl.php:469
#: app/controllers/MeprTransactionsCtrl.php:483
msgid "Save Failed"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:364
msgid "Could not delete subscription"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:387
msgid "Could not pause subscription"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:420
#: app/controllers/MeprSubscriptionsCtrl.php:466
msgid "Could not resume subscription"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:475
msgid "Subscription not found."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:493
msgid "The subscription's payment method does not support this."
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:514
msgid "Could not cancel subscription"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:647
#: app/controllers/MeprTransactionsCtrl.php:581
#: app/lib/MeprSubscriptionsTable.php:119
#: app/lib/MeprTransactionsTable.php:77
msgid "Subscription"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:649
msgid "Auto Rebill"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:653
#: app/controllers/MeprTransactionsCtrl.php:588
msgid "User"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:654
#: app/controllers/MeprTransactionsCtrl.php:589
msgid "Gateway"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:655
#: app/controllers/MeprSubscriptionsCtrl.php:664
#: app/controllers/MeprTransactionsCtrl.php:580
#: app/lib/MeprSubscriptionsTable.php:126
#: app/lib/MeprTransactionsTable.php:76
msgid "Transaction"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:656
#: app/controllers/MeprTransactionsCtrl.php:590
msgid "Created On"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:657
#: app/controllers/MeprTransactionsCtrl.php:591
msgid "Expires On"
msgstr ""

#: app/controllers/MeprSubscriptionsCtrl.php:665
#: app/views/admin/onboarding/membership.php:28
#: app/views/admin/onboarding/membership.php:79
msgid "Price"
msgstr ""

#: app/controllers/MeprSummaryEmailCtrl.php:37
msgid "MemberPress Summary Email"
msgstr ""

#: app/controllers/MeprSummaryEmailCtrl.php:114
msgid "[MemberPress] Your summary report for the week of %s"
msgstr ""

#: app/controllers/MeprTaxesCtrl.php:31
#: app/views/admin/taxes/options.php:7
msgid "Taxes"
msgstr ""

#: app/controllers/MeprTaxesCtrl.php:161
#: app/controllers/MeprTaxesCtrl.php:167
msgid "A valid tax rate id must be set"
msgstr ""

#: app/controllers/MeprTaxesCtrl.php:172
msgid "This tax rate was successfully deleted"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:66
msgid "MemberPress Send Transaction Expire Events"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:243
msgid "A transaction was created successfully."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:307
msgid "The transaction was successfully updated."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:324
msgid "The username must be set."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:343
msgid "The amount must be set."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:347
msgid "The amount must be a number."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:356
msgid "This is not a subscription for membership '%1$s' but for '%2$s'"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:367
msgid "This is not a subscription for user '%1$s' but for '%2$s'"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:386
msgid "This subscription was not found."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:408
msgid "Deleting Transactions could cause the associated member to lose access to protected content. Are you sure you want to delete this Transaction?"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:409
msgid "The Transaction could not be deleted. Please try again later."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:410
msgid "This will refund the transaction at the gateway level. This action is not reversable. Are you sure you want to refund this Transaction?"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:411
msgid "This will refund the transaction and cancel the subscription associated with this transaction at the gateway level. This action is not reversable. Are you sure you want to refund this Transaction and cancel it's Subscription?"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:413
msgid "Your transaction was successfully refunded."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:414
msgid "The Transaction could not be refunded. Please issue the refund by logging into your gateway's virtual terminal"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:415
msgid "Your transaction was refunded and subscription was cancelled successfully."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:416
msgid "The Transaction could not be refunded and/or Subscription could not be cancelled. Please issue the refund by logging into your gateway's virtual terminal"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:421
msgid "Click to copy"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:501
#: app/controllers/MeprTransactionsCtrl.php:529
msgid "Could not refund transaction"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:562
msgid "Could not delete transaction"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:584
msgid "Net"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:586
#: app/controllers/MeprVatTaxCtrl.php:574
msgid "Total"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:626
#: app/controllers/MeprTransactionsCtrl.php:654
msgid "Could not send email. Please try again later."
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:637
msgid "Email sent"
msgstr ""

#: app/controllers/MeprTransactionsCtrl.php:662
msgid "Welcome Email sent"
msgstr ""

#: app/controllers/MeprUpdateCtrl.php:108
#: app/views/admin/db/upgrade_error.php:43
msgid "Rollback MemberPress"
msgstr ""

#: app/controllers/MeprUpdateCtrl.php:240
msgid "You don't have sufficient permissions to rollback MemberPress."
msgstr ""

#: app/controllers/MeprUpdateCtrl.php:464
msgid "License key deactivated"
msgstr ""

#: app/controllers/MeprUpdateCtrl.php:630
msgid "Check for Update"
msgstr ""

#: app/controllers/MeprUpdateCtrl.php:821
#: app/lib/MeprAddonUpdates.php:228
msgid "You had an HTTP error connecting to Caseproof's Mothership API"
msgstr ""

#: app/controllers/MeprUpdateCtrl.php:831
#: app/lib/MeprAddonUpdates.php:238
msgid "Your License Key was invalid"
msgstr ""

#: app/controllers/MeprUpdateCtrl.php:887
msgid "Edge updates couldn't be updated."
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/controllers/MeprUpdateCtrl.php:965
#: app/lib/MeprAddonUpdates.php:297
msgid "To restore automatic updates, %1$sinstall the correct edition%2$s of MemberPress."
msgstr ""

#: app/controllers/MeprUsageCtrl.php:35
msgid "MemberPress Snapshot Interval"
msgstr ""

#: app/controllers/MeprUsageCtrl.php:57
msgid "Your site is attempting to send too many snapshots, we'll put an end to that."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:184
msgid "This user hasn't purchased any memberships - so no email will be sent."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:195
msgid "Message Sent"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:197
msgid "There was an issue sending the email"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:200
msgid "Why you creepin'?"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:202
msgid "Cannot resend message"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:484
#: app/controllers/MeprUsersCtrl.php:488
#: app/controllers/MeprUsersCtrl.php:501
#: app/controllers/MeprUsersCtrl.php:520
msgid "%s is required."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:509
msgid "%s file type not allowed."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:512
msgid "%s could not be uploaded."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:528
msgid "%s is not a valid email address."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:534
msgid "%s is not a valid URL."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:540
msgid "%s is not a valid date."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:581
msgid "Active Memberships"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:584
msgid "# Logins"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:641
#: app/helpers/MeprAppHelper.php:143
#: app/helpers/MeprAppHelper.php:172
#: app/helpers/MeprAppHelper.php:233
#: app/helpers/MeprAppHelper.php:246
#: app/helpers/MeprAppHelper.php:796
#: app/helpers/MeprAppHelper.php:802
#: app/helpers/MeprTransactionsHelper.php:153
#: app/lib/MeprUtils.php:1688
#: app/models/MeprSubscription.php:645
#: app/models/MeprSubscription.php:957
#: app/models/MeprTransaction.php:789
#: app/views/admin/onboarding/active_license.php:33
#: app/views/admin/subscriptions/row.php:142
#: app/views/admin/subscriptions/row.php:200
#: app/views/admin/subscriptions/row.php:204
#: app/views/admin/subscriptions/row.php:250
#: app/views/admin/taxes/vat_profile_fields.php:32
#: app/views/admin/transactions/edit_trans.php:14
#: app/views/admin/transactions/row.php:59
#: app/views/admin/transactions/row.php:181
#: app/views/admin/users/extra_profile_fields.php:52
msgid "Unknown"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:650
#: app/helpers/MeprOptionsHelper.php:26
#: app/models/MeprSubscription.php:1047
#: app/models/MeprTransaction.php:807
#: app/views/admin/members/row.php:75
#: app/views/admin/products/price_box.php:41
#: app/views/admin/transactions/row.php:128
msgid "None"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:712
msgid "You cannot register with this form. Please use the registration page found on the website instead."
msgstr ""

#: app/controllers/MeprUsersCtrl.php:743
msgid "MemberPress Settings"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:824
#: app/views/account/payments.php:40
#: app/views/account/subscriptions.php:47
#: app/views/account/subscriptions.php:51
#: app/views/readylaunch/account/subscriptions.php:52
#: app/views/readylaunch/account/subscriptions.php:56
msgctxt "ui"
msgid "Unknown"
msgstr ""

#: app/controllers/MeprUsersCtrl.php:828
#: app/views/account/subscriptions.php:33
#: app/views/account/subscriptions.php:49
#: app/views/readylaunch/account/subscriptions.php:38
#: app/views/readylaunch/account/subscriptions.php:54
msgctxt "ui"
msgid "Never"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:127
msgid "VAT number is required"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:133
msgid "Your VAT number is invalid"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:331
msgid "VAT (%s)"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:506
msgid "Export VAT Totals"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:507
msgid "Export VAT by Country"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:535
#: app/controllers/MeprVatTaxCtrl.php:536
#: app/controllers/MeprVatTaxCtrl.php:538
#: app/controllers/MeprVatTaxCtrl.php:573
msgid "Country Code"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:536
#: app/controllers/MeprVatTaxCtrl.php:538
#: app/views/admin/taxes/options.php:104
msgid "Country"
msgstr ""

#: app/controllers/MeprVatTaxCtrl.php:650
msgid "VAT number is invalid"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:37
msgid "This password doesn't meet the minimum strength requirement. %s."
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:53
msgid "Weak"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:54
msgid "Medium"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:55
msgid "Strong"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:56
msgid "Very Strong"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:57
msgid "Unbreakable"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:59
#: app/controllers/MeprZxcvbnCtrl.php:119
msgid "Password Strength"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:142
msgid "Password must be \"Medium\" or stronger"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:145
msgid "Password must be \"Strong\" or stronger"
msgstr ""

#: app/controllers/MeprZxcvbnCtrl.php:148
msgid "Password must be \"Very Strong\" or stronger"
msgstr ""

#: app/data/taxes/vat_countries.php:7
msgid "Austria"
msgstr ""

#: app/data/taxes/vat_countries.php:13
msgid "Belgium"
msgstr ""

#: app/data/taxes/vat_countries.php:19
msgid "Bulgaria"
msgstr ""

#: app/data/taxes/vat_countries.php:24
msgid "Cyprus"
msgstr ""

#: app/data/taxes/vat_countries.php:30
msgid "Czech Republic"
msgstr ""

#: app/data/taxes/vat_countries.php:36
msgid "Germany"
msgstr ""

#: app/data/taxes/vat_countries.php:42
msgid "Denmark"
msgstr ""

#: app/data/taxes/vat_countries.php:48
msgid "Estonia"
msgstr ""

#: app/data/taxes/vat_countries.php:54
msgid "Greece"
msgstr ""

#: app/data/taxes/vat_countries.php:60
msgid "Spain"
msgstr ""

#: app/data/taxes/vat_countries.php:66
msgid "Finland"
msgstr ""

#: app/data/taxes/vat_countries.php:72
msgid "France"
msgstr ""

#: app/data/taxes/vat_countries.php:78
msgid "Croatia"
msgstr ""

#: app/data/taxes/vat_countries.php:84
msgid "United Kingdom"
msgstr ""

#: app/data/taxes/vat_countries.php:90
msgid "Hungary"
msgstr ""

#: app/data/taxes/vat_countries.php:96
msgid "Ireland"
msgstr ""

#: app/data/taxes/vat_countries.php:102
msgid "Italy"
msgstr ""

#: app/data/taxes/vat_countries.php:108
msgid "Lithuania"
msgstr ""

#: app/data/taxes/vat_countries.php:114
msgid "Luxembourg"
msgstr ""

#: app/data/taxes/vat_countries.php:120
msgid "Latvia"
msgstr ""

#: app/data/taxes/vat_countries.php:126
msgid "Malta"
msgstr ""

#: app/data/taxes/vat_countries.php:132
msgid "Netherlands"
msgstr ""

#: app/data/taxes/vat_countries.php:138
msgid "Poland"
msgstr ""

#: app/data/taxes/vat_countries.php:144
msgid "Portugal"
msgstr ""

#: app/data/taxes/vat_countries.php:150
msgid "Romania"
msgstr ""

#: app/data/taxes/vat_countries.php:156
msgid "Sweden"
msgstr ""

#: app/data/taxes/vat_countries.php:162
msgid "Slovenia"
msgstr ""

#: app/data/taxes/vat_countries.php:168
msgid "Slovakia"
msgstr ""

#: app/data/taxes/vat_countries.php:174
msgid "French Guiana"
msgstr ""

#: app/data/taxes/vat_countries.php:180
msgid "Saint Martin (French part)"
msgstr ""

#: app/data/taxes/vat_countries.php:186
msgid "Martinique"
msgstr ""

#: app/data/taxes/vat_countries.php:192
msgid "Reunion"
msgstr ""

#: app/data/taxes/vat_countries.php:198
msgid "Mayotte"
msgstr ""

#: app/data/taxes/vat_countries.php:204
msgid "Saint Pierre and Miquelon"
msgstr ""

#: app/data/taxes/vat_countries.php:210
msgid "Guadeloupe"
msgstr ""

#: app/data/taxes/vat_countries.php:216
msgid "Monaco"
msgstr ""

#: app/emails/MeprAdminCancelledSubEmail.php:20
#: app/emails/MeprUserCancelledSubEmail.php:17
msgid "<b>Cancelled Subscription</b> Notice"
msgstr ""

#: app/emails/MeprAdminCancelledSubEmail.php:21
msgid "This email is sent to you when a subscription is cancelled."
msgstr ""

#: app/emails/MeprAdminCancelledSubEmail.php:25
msgid "** Subscription {$subscr_num} Was Cancelled"
msgstr ""

#: app/emails/MeprAdminCcExpiresReminderEmail.php:20
msgid "Credit Card Expires Reminder Email to Admin"
msgstr ""

#: app/emails/MeprAdminCcExpiresReminderEmail.php:21
#: app/emails/MeprAdminMemberSignupReminderEmail.php:21
#: app/emails/MeprAdminSignupAbandonedReminderEmail.php:21
#: app/emails/MeprAdminSubExpiresReminderEmail.php:21
#: app/emails/MeprAdminSubRenewsReminderEmail.php:21
#: app/emails/MeprAdminSubTrialEndsReminderEmail.php:21
msgid "This email is sent to the admin when triggered for a user."
msgstr ""

#: app/emails/MeprAdminCcExpiresReminderEmail.php:25
#: app/emails/MeprAdminMemberSignupReminderEmail.php:25
#: app/emails/MeprAdminSignupAbandonedReminderEmail.php:25
#: app/emails/MeprAdminSubExpiresReminderEmail.php:25
#: app/emails/MeprAdminSubRenewsReminderEmail.php:25
msgid "** %1$s Reminder Sent to %2$s"
msgstr ""

#: app/emails/MeprAdminCcExpiresReminderEmail.php:43
#: app/emails/MeprUserCcExpiresReminderEmail.php:40
#: app/helpers/MeprRemindersHelper.php:109
msgid "Credit Card Expiring"
msgstr ""

#: app/emails/MeprAdminCcExpiresReminderEmail.php:44
#: app/emails/MeprUserCcExpiresReminderEmail.php:41
msgid "Credit Card Expiring in 2 Days"
msgstr ""

#: app/emails/MeprAdminCcExpiringEmail.php:20
#: app/emails/MeprUserCcExpiringEmail.php:17
msgid "<b>Credit Card Expiring</b> Notice"
msgstr ""

#: app/emails/MeprAdminCcExpiringEmail.php:21
msgid "This email is sent to you when a member's credit card is expiring"
msgstr ""

#: app/emails/MeprAdminCcExpiringEmail.php:25
msgid "** Credit Card Expiring For {$subscr_num}"
msgstr ""

#: app/emails/MeprAdminDowngradedSubEmail.php:20
#: app/emails/MeprUserDowngradedSubEmail.php:17
msgid "<b>Downgraded Subscription</b> Notice"
msgstr ""

#: app/emails/MeprAdminDowngradedSubEmail.php:21
msgid "This email is sent to you when a subscription is downgraded."
msgstr ""

#: app/emails/MeprAdminDowngradedSubEmail.php:25
msgid "** Subscription {$subscr_num} Downgraded"
msgstr ""

#: app/emails/MeprAdminFailedTxnEmail.php:20
#: app/emails/MeprUserFailedTxnEmail.php:17
msgid "<b>Failed Transaction</b> Notice"
msgstr ""

#: app/emails/MeprAdminFailedTxnEmail.php:21
msgid "This email is sent to you when a transaction fails."
msgstr ""

#: app/emails/MeprAdminFailedTxnEmail.php:25
msgid "** Transaction Failed"
msgstr ""

#: app/emails/MeprAdminMemberSignupReminderEmail.php:20
msgid "Member Signup Reminder Email to Admin"
msgstr ""

#: app/emails/MeprAdminMemberSignupReminderEmail.php:42
#: app/emails/MeprUserMemberSignupReminderEmail.php:39
#: app/helpers/MeprRemindersHelper.php:131
msgid "Member Signed Up"
msgstr ""

#: app/emails/MeprAdminMemberSignupReminderEmail.php:43
#: app/emails/MeprUserMemberSignupReminderEmail.php:40
msgid "Member Signed Up 2 days ago"
msgstr ""

#: app/emails/MeprAdminNewOneOffEmail.php:20
msgid "<b>New One-Time Subscription</b> Notice"
msgstr ""

#: app/emails/MeprAdminNewOneOffEmail.php:21
msgid "This email is sent to you when a new non-recurring subscription is created."
msgstr ""

#: app/emails/MeprAdminNewOneOffEmail.php:25
msgid "** New One-Time Subscription: {$trans_num}"
msgstr ""

#: app/emails/MeprAdminNewSubEmail.php:20
msgid "<b>New Recurring Subscription</b> Notice"
msgstr ""

#: app/emails/MeprAdminNewSubEmail.php:21
msgid "This email is sent to you when a subscription is created."
msgstr ""

#: app/emails/MeprAdminNewSubEmail.php:25
msgid "** New Recurring Subscription: {$subscr_num}"
msgstr ""

#: app/emails/MeprAdminReceiptEmail.php:20
#: app/emails/MeprUserReceiptEmail.php:17
msgid "<b>Payment Receipt</b> Notice"
msgstr ""

#: app/emails/MeprAdminReceiptEmail.php:21
msgid "This email is sent to you when a payment comes through on your membership site"
msgstr ""

#: app/emails/MeprAdminReceiptEmail.php:25
msgid "** Payment of {$payment_amount} from {$user_full_name}"
msgstr ""

#: app/emails/MeprAdminRefundedTxnEmail.php:20
#: app/emails/MeprUserRefundedTxnEmail.php:17
msgid "<b>Refunded Transaction</b> Notice"
msgstr ""

#: app/emails/MeprAdminRefundedTxnEmail.php:21
msgid "This email is sent to you when a transaction is refunded."
msgstr ""

#: app/emails/MeprAdminRefundedTxnEmail.php:25
msgid "** Transaction {$trans_num} Was Refunded"
msgstr ""

#: app/emails/MeprAdminResumedSubEmail.php:20
#: app/emails/MeprUserResumedSubEmail.php:17
msgid "<b>Resumed Subscription</b> Notice"
msgstr ""

#: app/emails/MeprAdminResumedSubEmail.php:21
msgid "This email is sent to you when a subscription is resumed."
msgstr ""

#: app/emails/MeprAdminResumedSubEmail.php:25
msgid "** Subscription {$subscr_num} Resumed"
msgstr ""

#: app/emails/MeprAdminSignupAbandonedReminderEmail.php:20
msgid "Signup Abandoned Reminder Email to Admin"
msgstr ""

#: app/emails/MeprAdminSignupAbandonedReminderEmail.php:42
#: app/emails/MeprUserSignupAbandonedReminderEmail.php:39
#: app/helpers/MeprRemindersHelper.php:145
#: app/models/MeprReminder.php:189
msgid "Sign Up Abandoned"
msgstr ""

#: app/emails/MeprAdminSignupAbandonedReminderEmail.php:43
#: app/emails/MeprUserSignupAbandonedReminderEmail.php:40
msgid "Sign Up Abandoned 2 days ago"
msgstr ""

#: app/emails/MeprAdminSignupEmail.php:20
msgid "<b>New Signup</b> Notice"
msgstr ""

#: app/emails/MeprAdminSignupEmail.php:21
msgid "This email is sent to you when a user registers for your membership site and their first transaction completes."
msgstr ""

#: app/emails/MeprAdminSignupEmail.php:25
msgid "** New Signup: {$username}"
msgstr ""

#: app/emails/MeprAdminSubExpiresReminderEmail.php:20
msgid "Subscription Expires Reminder Email to Admin"
msgstr ""

#: app/emails/MeprAdminSubExpiresReminderEmail.php:44
#: app/emails/MeprUserSubExpiresReminderEmail.php:41
msgid "Subscription Expiring in 2 days"
msgstr ""

#: app/emails/MeprAdminSubRenewsReminderEmail.php:20
msgid "Subscription Renews Reminder Email to Admin"
msgstr ""

#: app/emails/MeprAdminSubRenewsReminderEmail.php:43
#: app/emails/MeprUserSubRenewsReminderEmail.php:40
#: app/helpers/MeprRemindersHelper.php:91
msgid "Subscription Renewing"
msgstr ""

#: app/emails/MeprAdminSubRenewsReminderEmail.php:44
msgid "Subscription Renewing in 2 Days"
msgstr ""

#: app/emails/MeprAdminSubTrialEndsReminderEmail.php:20
msgid "Subscription Trial Ending Reminder Email to Admin"
msgstr ""

#: app/emails/MeprAdminSubTrialEndsReminderEmail.php:25
msgid "A Member's Subscription Trial Period is Ending Soon"
msgstr ""

#: app/emails/MeprAdminSubTrialEndsReminderEmail.php:43
#: app/emails/MeprUserSubTrialEndsReminderEmail.php:39
#: app/models/MeprReminder.php:191
msgid "Subscription Trial Ending"
msgstr ""

#: app/emails/MeprAdminSubTrialEndsReminderEmail.php:44
msgid "Subscription Trial Ending in 2 Days"
msgstr ""

#: app/emails/MeprAdminSuspendedSubEmail.php:20
#: app/emails/MeprUserSuspendedSubEmail.php:17
msgid "<b>Paused Subscription</b> Notice"
msgstr ""

#: app/emails/MeprAdminSuspendedSubEmail.php:21
msgid "This email is sent to you when a subscription is paused."
msgstr ""

#: app/emails/MeprAdminSuspendedSubEmail.php:25
msgid "** Subscription {$subscr_num} Paused"
msgstr ""

#: app/emails/MeprAdminUpgradedSubEmail.php:20
#: app/emails/MeprUserUpgradedSubEmail.php:17
msgid "<b>Upgraded Subscription</b> Notice"
msgstr ""

#: app/emails/MeprAdminUpgradedSubEmail.php:21
msgid "This email is sent to you when a subscription is upgraded."
msgstr ""

#: app/emails/MeprAdminUpgradedSubEmail.php:25
msgid "** Subscription {$subscr_num} Upgraded"
msgstr ""

#: app/emails/MeprUserCancelledSubEmail.php:18
msgid "This email is sent to the user when a subscription is cancelled."
msgstr ""

#: app/emails/MeprUserCancelledSubEmail.php:22
msgid "** Your Automatic Payments Have Been Stopped"
msgstr ""

#: app/emails/MeprUserCcExpiresReminderEmail.php:17
msgid "Credit Card Expires Reminder Email to User"
msgstr ""

#: app/emails/MeprUserCcExpiresReminderEmail.php:18
#: app/emails/MeprUserMemberSignupReminderEmail.php:18
#: app/emails/MeprUserSignupAbandonedReminderEmail.php:18
#: app/emails/MeprUserSubExpiresReminderEmail.php:18
#: app/emails/MeprUserSubRenewsReminderEmail.php:18
#: app/emails/MeprUserSubTrialEndsReminderEmail.php:18
msgid "This email is sent to the user when triggered."
msgstr ""

#: app/emails/MeprUserCcExpiresReminderEmail.php:22
#: app/emails/MeprUserSubExpiresReminderEmail.php:22
#: app/emails/MeprUserSubRenewsReminderEmail.php:22
msgid "** Your %1$s"
msgstr ""

#: app/emails/MeprUserCcExpiringEmail.php:18
msgid "This email is sent to a member when their credit card is expiring"
msgstr ""

#: app/emails/MeprUserCcExpiringEmail.php:22
msgid "** Your Credit Card is Expiring"
msgstr ""

#: app/emails/MeprUserDowngradedSubEmail.php:18
msgid "This email is sent to the user when they downgrade a subscription."
msgstr ""

#: app/emails/MeprUserDowngradedSubEmail.php:22
msgid "** You've downgraded your subscription"
msgstr ""

#: app/emails/MeprUserFailedTxnEmail.php:18
msgid "This email is sent to the user when a transaction of theirs fails."
msgstr ""

#: app/emails/MeprUserFailedTxnEmail.php:22
msgid "** Your Transaction Failed"
msgstr ""

#: app/emails/MeprUserMemberSignupReminderEmail.php:17
msgid "Member Signup Reminder Email to User"
msgstr ""

#: app/emails/MeprUserMemberSignupReminderEmail.php:22
msgid "** Thanks for signing up"
msgstr ""

#: app/emails/MeprUserProductWelcomeEmail.php:20
msgid "Membership-Specific Welcome Email to User"
msgstr ""

#: app/emails/MeprUserProductWelcomeEmail.php:21
msgid "This email is sent when this membership is purchased."
msgstr ""

#: app/emails/MeprUserProductWelcomeEmail.php:26
msgid "** Thanks for Purchasing {$product_name}"
msgstr ""

#: app/emails/MeprUserReceiptEmail.php:18
msgid "This email is sent to a user when payment completes for one of your memberships in her behalf."
msgstr ""

#: app/emails/MeprUserReceiptEmail.php:22
msgid "** Payment Receipt"
msgstr ""

#: app/emails/MeprUserRefundedTxnEmail.php:18
msgid "This email is sent to the user when a transaction is refunded."
msgstr ""

#: app/emails/MeprUserRefundedTxnEmail.php:22
msgid "** Your Transaction Was Refunded"
msgstr ""

#: app/emails/MeprUserResumedSubEmail.php:18
msgid "This email is sent to the user when they resume a subscription."
msgstr ""

#: app/emails/MeprUserResumedSubEmail.php:22
msgid "** Your subscription has resumed"
msgstr ""

#: app/emails/MeprUserSignupAbandonedReminderEmail.php:17
msgid "Signup Abandoned Reminder Email to User"
msgstr ""

#: app/emails/MeprUserSignupAbandonedReminderEmail.php:22
msgid "** Please complete your signup"
msgstr ""

#: app/emails/MeprUserStripeInvoiceEmail.php:17
msgid "<b>Stripe Failed Payment</b> Notice"
msgstr ""

#: app/emails/MeprUserStripeInvoiceEmail.php:18
msgid "This email is sent to the user when a Stripe subscription payment of theirs fails, with a link to pay the outstanding invoice."
msgstr ""

#: app/emails/MeprUserStripeInvoiceEmail.php:22
msgid "** Your Subscription Payment Failed"
msgstr ""

#: app/emails/MeprUserSubExpiresReminderEmail.php:17
msgid "Subscription Expires Reminder Email to User"
msgstr ""

#: app/emails/MeprUserSubRenewsReminderEmail.php:17
msgid "Subscription Renews Reminder Email to User"
msgstr ""

#: app/emails/MeprUserSubRenewsReminderEmail.php:41
msgid "Subscription Renewing in 2 days"
msgstr ""

#: app/emails/MeprUserSubTrialEndsReminderEmail.php:17
msgid "Subscription Trial Ending Reminder Email to User"
msgstr ""

#: app/emails/MeprUserSubTrialEndsReminderEmail.php:22
msgid "Your Subscription Trial Period is Ending Soon"
msgstr ""

#: app/emails/MeprUserSubTrialEndsReminderEmail.php:40
msgid "Subscription Trial Ending in 2 days"
msgstr ""

#: app/emails/MeprUserSuspendedSubEmail.php:18
msgid "This email is sent to the user when one of their subscriptions is paused."
msgstr ""

#: app/emails/MeprUserSuspendedSubEmail.php:22
msgid "** You've paused your subscription"
msgstr ""

#: app/emails/MeprUserUpgradedSubEmail.php:18
msgid "This email is sent to the user when they upgrade a subscription."
msgstr ""

#: app/emails/MeprUserUpgradedSubEmail.php:22
msgid "** You've upgraded your subscription"
msgstr ""

#: app/emails/MeprUserWelcomeEmail.php:17
msgid "<b>Welcome Email</b>"
msgstr ""

#: app/emails/MeprUserWelcomeEmail.php:18
msgid "This email is sent welcome a new user when she initially signs up for your membership site with a completed purchase."
msgstr ""

#: app/emails/MeprUserWelcomeEmail.php:22
msgid "** Welcome to {$blog_name}"
msgstr ""

#: app/gateways/authorizenet/client.php:132
msgid "Can not refund the payment. The transaction may not have been settled"
msgstr ""

#: app/gateways/authorizenet/client.php:202
msgid "Profile does not have a payment source"
msgstr ""

#: app/gateways/authorizenet/client.php:253
msgid "Can not complete the payment."
msgstr ""

#: app/gateways/authorizenet/client.php:364
msgid "Can not cancel subscription"
msgstr ""

#: app/gateways/authorizenet/client.php:416
msgid "Can not update subscription"
msgstr ""

#: app/gateways/authorizenet/client.php:530
msgid "You have subscribed to a membership which has the same pricing term. Subscription can not be created with Authorize.net"
msgstr ""

#: app/gateways/authorizenet/client.php:617
msgid "Can not complete the payment"
msgstr ""

#: app/gateways/authorizenet/client.php:690
msgid "Your email is already registered on the gateway. Please contact us."
msgstr ""

#: app/gateways/MeprArtificialGateway.php:577
#: app/gateways/MeprAuthorizeGateway.php:1241
#: app/gateways/MeprPayPalProGateway.php:1103
#: app/views/admin/drm/modal.php:35
#: app/views/admin/drm/modal_fee.php:21
#: app/views/admin/drm/nhw/modal.php:26
msgid "Submit"
msgstr ""

#: app/gateways/MeprArtificialGateway.php:578
#: app/gateways/MeprAuthorizeGateway.php:1242
#: app/gateways/MeprAuthorizeGateway.php:1510
#: app/gateways/MeprAuthorizeProfileGateway.php:682
#: app/gateways/MeprAuthorizeProfileGateway.php:883
#: app/gateways/MeprPayPalProGateway.php:1104
#: app/gateways/MeprPayPalProGateway.php:1340
#: app/gateways/MeprStripeGateway.php:2981
#: app/gateways/MeprStripeGateway.php:3180
#: app/views/account/home.php:62
#: app/views/account/password.php:38
#: app/views/admin/emails/options.php:36
#: app/views/admin/options/active_license.php:21
#: app/views/admin/options/edge_updates.php:5
#: app/views/admin/readylaunch/account.php:59
#: app/views/admin/readylaunch/coaching.php:59
#: app/views/admin/readylaunch/login.php:61
#: app/views/admin/readylaunch/thankyou.php:61
#: app/views/admin/subscriptions/row.php:44
#: app/views/admin/taxes/stripe_tax_options.php:51
#: app/views/admin/transactions/row.php:74
#: app/views/admin/users/extra_profile_fields.php:80
#: app/views/checkout/form.php:195
#: app/views/checkout/spc_form.php:146
#: app/views/checkout/spc_form.php:162
#: app/views/checkout/spc_form.php:192
#: app/views/checkout/spc_form.php:231
#: app/views/readylaunch/account/password.php:41
#: app/views/readylaunch/checkout/form.php:189
#: app/views/readylaunch/checkout/form.php:236
#: app/views/readylaunch/checkout/form.php:264
#: app/views/readylaunch/checkout/form.php:286
msgid "Loading..."
msgstr ""

#: app/gateways/MeprArtificialGateway.php:581
#: app/gateways/MeprStripeGateway.php:2983
#: app/gateways/MeprStripeGateway.php:3182
#: app/views/checkout/MeprStripeGateway/payment_form.php:27
#: app/views/checkout/payment_form.php:32
msgid "Javascript is disabled in your browser. You will not be able to complete your purchase until you either enable JavaScript in your browser, or switch to a browser that supports it."
msgstr ""

#: app/gateways/MeprArtificialGateway.php:615
msgid "Admin Must Manually Complete Transactions"
msgstr ""

#: app/gateways/MeprArtificialGateway.php:620
msgid "Send Welcome email when \"Admin Must Manually Complete Transactions\" is enabled"
msgstr ""

#: app/gateways/MeprArtificialGateway.php:625
msgid "Do not cancel old plan on upgrades when \"Admin Must Manually Complete Transactions\" is enabled"
msgstr ""

#: app/gateways/MeprArtificialGateway.php:672
msgid "This action is not possible with the payment method used with this Subscription"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:77
#: app/gateways/MeprAuthorizeProfileGateway.php:102
msgid "Pay with your credit card via Authorize.net"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:249
#: app/gateways/MeprAuthorizeGateway.php:592
#: app/gateways/MeprAuthorizeGateway.php:653
#: app/gateways/MeprPayPalProGateway.php:341
#: app/gateways/MeprStripeGateway.php:1060
#: app/gateways/MeprStripeGateway.php:3546
msgid "Payment was unsuccessful, please check your payment details and try again."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:865
#: app/gateways/MeprAuthorizeGateway.php:870
#: app/gateways/MeprAuthorizeProfileGateway.php:725
#: app/gateways/MeprAuthorizeProfileGateway.php:730
msgid "Your payment details are invalid, please check them and try again."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:980
msgid "This subscription is invalid."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1164
#: app/gateways/MeprPayPalProGateway.php:1077
#: app/lib/MeprMembersTable.php:77
#: app/views/admin/members/new_member.php:32
msgid "First Name"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1169
#: app/gateways/MeprPayPalProGateway.php:1082
#: app/lib/MeprMembersTable.php:78
#: app/views/admin/members/new_member.php:40
msgid "Last Name"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1181
#: app/gateways/MeprAuthorizeGateway.php:1457
#: app/gateways/MeprAuthorizeProfileGateway.php:824
#: app/gateways/MeprPayPalProGateway.php:1031
#: app/gateways/MeprPayPalProGateway.php:1294
msgid "Credit Card Number"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1182
#: app/gateways/MeprPayPalProGateway.php:1032
msgid "Invalid Credit Card Number"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1200
#: app/gateways/MeprAuthorizeGateway.php:1474
#: app/gateways/MeprAuthorizeProfileGateway.php:843
#: app/gateways/MeprPayPalProGateway.php:1050
#: app/gateways/MeprPayPalProGateway.php:1311
msgid "Expiration"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1201
#: app/gateways/MeprAuthorizeGateway.php:1475
#: app/gateways/MeprAuthorizeProfileGateway.php:844
#: app/gateways/MeprPayPalProGateway.php:1051
#: app/gateways/MeprPayPalProGateway.php:1312
msgid "Invalid Expiration"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1226
#: app/gateways/MeprAuthorizeGateway.php:1493
#: app/gateways/MeprAuthorizeProfileGateway.php:863
#: app/gateways/MeprPayPalProGateway.php:1069
#: app/gateways/MeprPayPalProGateway.php:1330
msgid "CVC"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1227
#: app/gateways/MeprAuthorizeGateway.php:1494
#: app/gateways/MeprAuthorizeProfileGateway.php:864
#: app/gateways/MeprPayPalProGateway.php:1070
#: app/gateways/MeprPayPalProGateway.php:1331
msgid "Invalid CVC Code"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1234
msgid "ZIP/Post Code"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1287
#: app/gateways/MeprPayPalProGateway.php:1151
msgid "An unknown error has occurred."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1309
#: app/gateways/MeprPayPalProGateway.php:1159
msgid "Your first name and last name must not be blank."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1313
#: app/gateways/MeprAuthorizeGateway.php:1533
#: app/gateways/MeprAuthorizeProfileGateway.php:906
#: app/gateways/MeprPayPalProGateway.php:1163
#: app/gateways/MeprPayPalProGateway.php:1364
msgid "You must enter your Credit Card number."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1315
#: app/gateways/MeprAuthorizeGateway.php:1535
#: app/gateways/MeprAuthorizeProfileGateway.php:908
#: app/gateways/MeprPayPalProGateway.php:1165
#: app/gateways/MeprPayPalProGateway.php:1366
msgid "Your credit card number is invalid."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1319
#: app/gateways/MeprAuthorizeGateway.php:1539
#: app/gateways/MeprAuthorizeProfileGateway.php:912
#: app/gateways/MeprPayPalProGateway.php:1169
#: app/gateways/MeprPayPalProGateway.php:1370
msgid "You must enter your CVV code."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1342
#: app/gateways/MeprAuthorizeProfileGateway.php:194
msgid "API Login ID*:"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1346
#: app/gateways/MeprAuthorizeProfileGateway.php:200
msgid "Transaction Key*:"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1350
#: app/gateways/MeprAuthorizeProfileGateway.php:206
msgid "Signature Key*:"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1354
#: app/gateways/MeprAuthorizeProfileGateway.php:219
msgid "Use Authorize.net Sandbox"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1357
msgid "Send Authorize.net Debug Emails"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1360
#: app/gateways/MeprPayPalProGateway.php:1212
#: app/views/admin/gateways/stripe/checkboxes.php:89
msgid "Force SSL"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1363
#: app/gateways/MeprAuthorizeProfileGateway.php:223
msgid "Webhook URL:"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1369
msgid "Silent Post URL:"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1375
msgid "MD5 Hash Value:"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1398
msgid "Login Name field cannot be blank."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1405
msgid "Transaction Key field cannot be blank."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1412
msgid "Signature Key field cannot be blank."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1454
#: app/gateways/MeprAuthorizeProfileGateway.php:821
#: app/gateways/MeprPayPalProGateway.php:1291
msgid "Update your Credit Card information below"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1501
#: app/gateways/MeprAuthorizeProfileGateway.php:872
#: app/views/checkout/MeprAuthorizeGateway/payment_gateway_fields.php:16
msgid "Zip code for Card"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1509
#: app/gateways/MeprAuthorizeProfileGateway.php:881
#: app/gateways/MeprPayPalProGateway.php:1339
msgid "Update Credit Card"
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1529
#: app/gateways/MeprAuthorizeProfileGateway.php:902
#: app/gateways/MeprPayPalProGateway.php:1360
msgid "An unknown error has occurred. Please try again."
msgstr ""

#: app/gateways/MeprAuthorizeGateway.php:1619
#: app/gateways/MeprAuthorizeGateway.php:1621
#: app/gateways/MeprAuthorizeGateway.php:1731
#: app/gateways/MeprAuthorizeGateway.php:1733
msgid "You had an HTTP error connecting to %1$s: %2$s"
msgstr ""

#: app/gateways/MeprAuthorizeProfileGateway.php:212
msgid "Public Key*:"
msgstr ""

#: app/gateways/MeprAuthorizeProfileGateway.php:680
#: app/gateways/MeprPayPalCommerceGateway.php:1214
#: app/gateways/MeprStripeGateway.php:2980
#: app/gateways/MeprStripeGateway.php:3179
msgctxt "ui"
msgid "Submit"
msgstr ""

#: app/gateways/MeprAuthorizeWebhooks.php:74
msgid "MeprAuthorizeAPI Error: Unable to retrieve transaction details. Check your logs for errors."
msgstr ""

#: app/gateways/MeprAuthorizeWebhooks.php:86
msgid "This is not a valid Webhook! Check your settings."
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:149
#: app/gateways/MeprPayPalGateway.php:84
#: app/gateways/MeprPayPalStandardGateway.php:62
msgid "Pay via your PayPal account"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:364
msgid "Refund request has been done unsuccessfully"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:556
#: app/gateways/MeprPayPalGateway.php:835
#: app/gateways/MeprPayPalProGateway.php:783
#: app/gateways/MeprPayPalStandardGateway.php:829
#: app/gateways/MeprStripeGateway.php:2328
msgid "This subscription has already been paused."
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:560
#: app/gateways/MeprPayPalGateway.php:839
#: app/gateways/MeprPayPalProGateway.php:787
#: app/gateways/MeprPayPalStandardGateway.php:833
#: app/gateways/MeprStripeGateway.php:2332
msgid "Sorry, subscriptions cannot be paused during a free trial."
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1006
#: app/gateways/MeprPayPalStandardGateway.php:1108
msgid "Multiple subscriptions are not supported"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1080
#: app/gateways/MeprPayPalCommerceGateway.php:1370
msgid "Could not create PayPal subscription"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1161
#: app/gateways/MeprPayPalCommerceGateway.php:1177
msgid "Could not create PayPal Order"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1298
msgid "Could not create PayPal product"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1522
msgid "Could not create Plan"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1627
#: app/gateways/MeprPayPalGateway.php:1225
#: app/gateways/MeprPayPalStandardGateway.php:1358
msgid "Updating your PayPal Account Information"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1628
#: app/gateways/MeprPayPalGateway.php:1226
#: app/gateways/MeprPayPalStandardGateway.php:1359
msgid "To update your PayPal Account Information, please go to %1$sPayPal.com%2$s, login and edit your account information there."
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1951
msgid "On request"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:1969
msgid "There was a problem, try logging in directly at PayPal to update the status of your recurring profile."
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:2086
msgid "Payer has not yet approved the Order for payment"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:2089
msgid "Order could not be captured"
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:2159
#: app/gateways/MeprPayPalGateway.php:1424
#: app/gateways/MeprPayPalProGateway.php:1570
#: app/gateways/MeprPayPalStandardGateway.php:1771
msgid "Your payment at PayPal was cancelled."
msgstr ""

#: app/gateways/MeprPayPalCommerceGateway.php:2160
#: app/gateways/MeprPayPalGateway.php:1425
#: app/gateways/MeprPayPalProGateway.php:1571
#: app/gateways/MeprPayPalStandardGateway.php:1772
msgid "You can retry your purchase by %1$sclicking here%2$s."
msgstr ""

#: app/gateways/MeprPayPalGateway.php:505
#: app/gateways/MeprPayPalStandardGateway.php:633
msgid "The refund was unsuccessful. Please login at PayPal and refund the transaction there."
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1094
msgid "The connection to PayPal failed"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1098
msgid "There was a problem completing the transaction"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1156
#: app/gateways/MeprPayPalProGateway.php:1194
msgid "API Username*:"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1160
#: app/gateways/MeprPayPalProGateway.php:1198
msgid "API Password*:"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1164
#: app/gateways/MeprPayPalProGateway.php:1202
msgid "Signature*:"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1168
#: app/gateways/MeprPayPalProGateway.php:1206
#: app/gateways/MeprPayPalStandardGateway.php:1305
msgid "Use PayPal Sandbox"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1171
#: app/gateways/MeprPayPalProGateway.php:1209
#: app/gateways/MeprPayPalStandardGateway.php:1308
#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:147
msgid "Send PayPal Debug Emails"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1174
#: app/gateways/MeprPayPalProGateway.php:1215
#: app/gateways/MeprPayPalStandardGateway.php:1311
msgid "PayPal IPN URL:"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1197
#: app/gateways/MeprPayPalProGateway.php:1238
msgid "PayPal API Username field can't be blank."
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1202
#: app/gateways/MeprPayPalProGateway.php:1243
msgid "PayPal API Password field can't be blank."
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1207
#: app/gateways/MeprPayPalProGateway.php:1248
msgid "PayPal Signature field can't be blank."
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1321
#: app/gateways/MeprPayPalProGateway.php:1460
#: app/gateways/MeprPayPalStandardGateway.php:1450
#: app/gateways/MeprStripeGateway.php:3726
msgid "You had an HTTP error connecting to %s"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1438
msgid "Your payment at PayPal failed."
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1439
msgid "You can retry your purchase by %1$sclicking here%2$s. If you continue having troubles paying, please contact PayPal support to find out why the payments are not being approved."
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1470
#: app/gateways/MeprPayPalProGateway.php:1602
msgid "** PayPal Payment ERROR on %s"
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1471
msgid ""
"Your PayPal account isn't set up to sell Digital Goods.\n"
"\n"
"PayPal is no longer accepting new signups for Digital Goods (via Express Checkout).\n"
"\n"
"If your PayPal account is not newly opened, you may be able to contact PayPal's 2nd or 3rd tier support engineers and get Digital Goods enabled for Express Checkout. A little persistence here is sometimes what it takes to make it happen.\n"
"\n"
"If their support cannot, or will not activate Digital Goods for you, then you will need to switch MemberPress to use the PayPal Standard gateway integration instead.\n"
"\n"
"<a href=\"https://memberpress.com/docs/paypal-standard/\">MemberPress - PayPal Standard Gateway Integration Instructions</a>\n"
"\n"
"Thanks,\n"
"\n"
"The MemberPress Team\n"
""
msgstr ""

#: app/gateways/MeprPayPalGateway.php:1540
#: app/gateways/MeprPayPalStandardGateway.php:1482
msgid "There was a problem cancelling, try logging in directly at PayPal to update the status of your recurring profile."
msgstr ""

#: app/gateways/MeprPayPalProGateway.php:86
msgid "Pay with your credit card via PayPal"
msgstr ""

#: app/gateways/MeprPayPalProGateway.php:391
msgid "The payment was unsuccessful. %s"
msgstr ""

#: app/gateways/MeprPayPalProGateway.php:483
msgid "The refund was unsuccessful. %s"
msgstr ""

#: app/gateways/MeprPayPalProGateway.php:742
msgid "Updating the Credit Card was unsuccessful. %s"
msgstr ""

#: app/gateways/MeprPayPalProGateway.php:1603
msgid ""
"Your PayPal account isn't setup to sell Digital Goods.\n"
"\n"
"Your recurring billing profiles and transactions won't complete properly until this problem is fixed.\n"
"\n"
"Follow these instructions to enable Digital Goods:\n"
"1) Sign in to your PayPal account (must be signed in before visiting the link below)\n"
"2) Visit https://www.paypal.com/us/webapps/mpp/digital in your browser\n"
"3) Follow the steps PayPal provides here to add Digital Goods to your account\n"
"\n"
"If you still have issues getting this to work, please contact customer support at https://memberpress.com/support/.\n"
"\n"
"Thanks,\n"
"\n"
"The MemberPress Team\n"
""
msgstr ""

#: app/gateways/MeprPayPalProGateway.php:1674
msgid "There was a problem cancelling. %s"
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1283
msgid "Primary PayPal Email*:"
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1289
msgid "Advanced Mode"
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1293
msgid "API Username:"
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1297
msgid "API Password:"
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1301
msgid "Signature:"
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1316
msgid "Return URL:"
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1341
msgid "Primary PayPal Email field can't be blank."
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1551
msgctxt "ui"
msgid "Something unexpected has occurred. Please contact us for assistance."
msgstr ""

#: app/gateways/MeprPayPalStandardGateway.php:1646
msgctxt "ui"
msgid "Your payment amount was lower than expected. Please contact us for assistance if necessary."
msgstr ""

#: app/gateways/MeprStripeGateway.php:19
msgid "Pay with your credit card via Stripe"
msgstr ""

#: app/gateways/MeprStripeGateway.php:2406
msgid "This subscription has already been resumed."
msgstr ""

#: app/gateways/MeprStripeGateway.php:2471
msgid "The subscription is locked to a different currency. Please contact us to resume your subscription."
msgstr ""

#: app/gateways/MeprStripeGateway.php:2474
#: app/gateways/MeprStripeGateway.php:3602
msgid "Customer not found"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/gateways/MeprStripeGateway.php:2551
msgid "The subscription could not be resumed automatically, %1$sclick here%2$s to complete the payment."
msgstr ""

#: app/gateways/MeprStripeGateway.php:2639
msgid "This subscription has already been cancelled."
msgstr ""

#: app/gateways/MeprStripeGateway.php:2749
msgid "Please complete payment information"
msgstr ""

#: app/gateways/MeprStripeGateway.php:2754
msgid "The response from the server was invalid"
msgstr ""

#: app/gateways/MeprStripeGateway.php:2755
msgid "An error occurred, please try again"
msgstr ""

#. Translators: %1$s: open strong tag, %2$s: close strong tag, %3$s: error message.
#: app/gateways/MeprStripeGateway.php:2758
msgid "%1$sERROR%2$s: %3$s"
msgstr ""

#: app/gateways/MeprStripeGateway.php:2968
#: app/views/checkout/MeprStripeGateway/payment_form.php:15
msgid "Pay with your Credit Card via Stripe Checkout"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3089
msgid "All Stripe keys must be filled in."
msgstr ""

#: app/gateways/MeprStripeGateway.php:3168
msgid "Link is set up as the payment method for this subscription. You can change the default payment method by logging in to <a href=\"https://link.co\">Link.co</a>."
msgstr ""

#: app/gateways/MeprStripeGateway.php:3169
msgid "Or"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3170
msgid "If you do not wish to use Link for this subscription, you can enter the details for a different payment method below."
msgstr ""

#: app/gateways/MeprStripeGateway.php:3172
msgid "Update your payment information below"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3371
msgid "No Webhook Signature"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3381
msgid "Incorrect Webhook Signature"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3387
msgid "No `event` set"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3397
msgid "Request was sent to the wrong site?"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3403
msgid "No payment method like that exists on this site"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3413
msgid "Webhook not supported"
msgstr ""

#. Translators: In this string, %s is the Blog Name/Title.
#: app/gateways/MeprStripeGateway.php:3638
msgid "[%s] Confirm Subscription Payment"
msgstr ""

#: app/gateways/MeprStripeGateway.php:3650
msgid "The invoice does not require action from the customer."
msgstr ""

#: app/gateways/MeprStripeGateway.php:3737
msgid "There was an issue with the credit card processor. Try again later."
msgstr ""

#. Translators: %d: the product ID.
#. Translators: %d: product ID.
#: app/gateways/MeprStripeGateway.php:4220
#: app/gateways/MeprStripeGateway.php:4935
msgid "Product %d"
msgstr ""

#: app/gateways/MeprStripeGateway.php:5243
msgid "MemberPress Test Payment"
msgstr ""

#: app/helpers/MeprAccountHelper.php:43
#: app/views/shortcodes/list_users_subscriptions.php:32
msgctxt "ui"
msgid "Subscribe"
msgstr ""

#: app/helpers/MeprAccountHelper.php:67
#: app/lib/MeprBaseGateway.php:636
msgid "Please select a new plan"
msgstr ""

#: app/helpers/MeprAccountHelper.php:81
#: app/lib/MeprBaseGateway.php:650
msgid "Select Plan"
msgstr ""

#: app/helpers/MeprAccountHelper.php:89
#: app/helpers/MeprAccountHelper.php:91
#: app/lib/MeprBaseGateway.php:661
msgid "Change Plan"
msgstr ""

#: app/helpers/MeprAppHelper.php:71
#: app/helpers/MeprAppHelper.php:382
#: app/models/MeprOptions.php:1268
#: app/models/MeprOptions.php:1269
#: app/models/MeprTransaction.php:1216
msgid "Free"
msgstr ""

#: app/helpers/MeprAppHelper.php:360
msgctxt "ui"
msgid " (price includes taxes)"
msgstr ""

#: app/helpers/MeprAppHelper.php:380
msgid "Free forever"
msgstr ""

#: app/helpers/MeprAppHelper.php:384
msgid "Free for a %1$s"
msgstr ""

#: app/helpers/MeprAppHelper.php:386
msgid "Free for %1$d %2$s"
msgstr ""

#: app/helpers/MeprAppHelper.php:409
msgid " (prorated)"
msgstr ""

#: app/helpers/MeprAppHelper.php:418
msgid "free"
msgstr ""

#: app/helpers/MeprAppHelper.php:430
#: app/helpers/MeprAppHelper.php:432
msgid " (proration)"
msgstr ""

#: app/helpers/MeprAppHelper.php:447
msgid "%1$s %2$s for %3$s%4$s "
msgstr ""

#: app/helpers/MeprAppHelper.php:449
msgid "%1$s %2$s for %3$s%4$s then "
msgstr ""

#: app/helpers/MeprAppHelper.php:454
msgid "%1$s%2$s once and "
msgstr ""

#: app/helpers/MeprAppHelper.php:465
#: app/helpers/MeprAppHelper.php:492
msgid " for %1$d %2$s"
msgstr ""

#: app/helpers/MeprAppHelper.php:470
msgid "%1$d payment of "
msgid_plural "%1$d payments of "
msgstr[0] ""
msgstr[1] ""

#: app/helpers/MeprAppHelper.php:482
msgid "%1$s / %2$s"
msgstr ""

#: app/helpers/MeprAppHelper.php:484
msgid "%1$s / %2$d %3$s"
msgstr ""

#: app/helpers/MeprAppHelper.php:512
msgid " for access until %s"
msgstr ""

#: app/helpers/MeprAppHelper.php:522
msgid " with coupon %s"
msgstr ""

#: app/helpers/MeprAppHelper.php:603
msgid "-- Select Country --"
msgstr ""

#: app/helpers/MeprAppHelper.php:728
msgid "Export table as CSV (%s records)"
msgstr ""

#: app/helpers/MeprAppHelper.php:730
msgid "Export all as CSV (%s records)"
msgstr ""

#: app/helpers/MeprAppHelper.php:792
msgid "Canceled"
msgstr ""

#: app/helpers/MeprAppHelper.php:793
msgid "Lapsed"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:55
#: app/lib/MeprUtils.php:97
msgid "Jan"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:56
#: app/lib/MeprUtils.php:98
msgid "Feb"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:57
#: app/lib/MeprUtils.php:99
msgid "Mar"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:58
#: app/lib/MeprUtils.php:100
msgid "Apr"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:59
#: app/helpers/MeprReportsHelper.php:79
#: app/lib/MeprUtils.php:101
#: app/lib/MeprUtils.php:116
msgid "May"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:60
#: app/lib/MeprUtils.php:102
msgid "Jun"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:61
#: app/lib/MeprUtils.php:103
msgid "Jul"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:62
#: app/lib/MeprUtils.php:104
msgid "Aug"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:63
#: app/lib/MeprUtils.php:105
msgid "Sept"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:64
#: app/lib/MeprUtils.php:106
msgid "Oct"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:65
#: app/lib/MeprUtils.php:107
msgid "Nov"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:66
#: app/lib/MeprUtils.php:108
msgid "Dec"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:87
msgid "Using Coupon &ndash; %s"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:89
msgid "Have a coupon?"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:224
#: app/helpers/MeprTransactionsHelper.php:549
#: app/views/admin/products/form.php:41
msgid "Lifetime"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:225
#: app/helpers/MeprProductsHelper.php:43
#: app/views/admin/reports/main.php:14
msgid "Yearly"
msgstr ""

#: app/helpers/MeprCouponsHelper.php:226
#: app/helpers/MeprProductsHelper.php:42
#: app/views/admin/reports/main.php:13
msgid "Monthly"
msgstr ""

#: app/helpers/MeprDrmHelper.php:386
msgid "This is an automated message from %s."
msgstr ""

#: app/helpers/MeprDrmHelper.php:392
msgid "MemberPress: Did You Forget Something?"
msgstr ""

#: app/helpers/MeprDrmHelper.php:394
msgid "Oops! It looks like your MemberPress license key is missing. Here's how to fix the problem fast and easy:"
msgstr ""

#: app/helpers/MeprDrmHelper.php:395
msgid "We’re here if you need any help."
msgstr ""

#: app/helpers/MeprDrmHelper.php:396
msgid "Alert"
msgstr ""

#: app/helpers/MeprDrmHelper.php:401
#: app/helpers/MeprDrmHelper.php:417
#: app/helpers/MeprDrmHelper.php:433
msgid "Grab your key from your <a href=\""
msgstr ""

#: app/helpers/MeprDrmHelper.php:408
#: app/helpers/MeprDrmHelper.php:476
msgid "MemberPress: WARNING! Your Business is at Risk"
msgstr ""

#: app/helpers/MeprDrmHelper.php:410
msgid "To continue using MemberPress without interruption, you need to enter your license key right away. Here’s how:"
msgstr ""

#: app/helpers/MeprDrmHelper.php:426
msgid "Because your license key is inactive, you can no longer manage MemberPress on the backend (e.g., you can't do things like issue customer refunds or add new members). Fortunately, this problem is easy to fix!"
msgstr ""

#: app/helpers/MeprDrmHelper.php:427
msgid "We're here to help you get things up and running. Let us know if you need assistance."
msgstr ""

#: app/helpers/MeprDrmHelper.php:468
msgid "This is an automated message from %1$s. If you continue getting these messages, please try deactivating and then re-activating your license key on %2$s."
msgstr ""

#: app/helpers/MeprDrmHelper.php:478
msgid "Your MemberPress license key is expired, but is required to continue using MemberPress. Fortunately, it’s easy to renew your license key. Just do the following:"
msgstr ""

#: app/helpers/MeprDrmHelper.php:485
#: app/helpers/MeprDrmHelper.php:500
msgid "Go to MemberPress.com and make your selection. <a href=\""
msgstr ""

#: app/helpers/MeprDrmHelper.php:495
msgid "Without an active license key, MemberPress cannot be managed on the backend. Your frontend will remain intact, but you can’t: Issue customer refunds, Add new members, Manage memberships. Fortunately, this problem is easy to fix by doing the following: "
msgstr ""

#: app/helpers/MeprGroupsHelper.php:62
msgid "None / Custom"
msgstr ""

#: app/helpers/MeprGroupsHelper.php:100
#: app/helpers/MeprOptionsHelper.php:588
#: app/helpers/MeprOptionsHelper.php:633
#: app/helpers/MeprOptionsHelper.php:663
#: app/helpers/MeprTransactionsHelper.php:548
#: app/integrations/two-factor/Integration.php:325
#: app/views/admin/products/advanced.php:22
msgid "Default"
msgstr ""

#: app/helpers/MeprGroupsHelper.php:146
#: app/views/readylaunch/groups/front_groups_page.php:70
msgid "Most Popular"
msgstr ""

#: app/helpers/MeprGroupsHelper.php:243
#: app/helpers/MeprOptionsHelper.php:48
#: app/helpers/MeprUsersHelper.php:201
#: app/views/readylaunch/groups/front_groups_page.php:113
msgid "View"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:205
msgid "Unable to install. You can %1$spurchase it here%2$s, afterwards you can install it from the add-ons page."
msgstr ""

#. Translators: %s: account name.
#: app/helpers/MeprOnboardingHelper.php:386
msgid "Connected to: %s"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:390
msgid "Connected"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:397
msgid "Pro feature"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:405
#: app/views/admin/onboarding/content.php:56
#: app/views/admin/onboarding/membership.php:35
#: app/views/admin/onboarding/rules.php:30
#: app/views/admin/readylaunch/account.php:41
#: app/views/admin/readylaunch/coaching.php:41
#: app/views/admin/readylaunch/login.php:40
#: app/views/admin/readylaunch/thankyou.php:40
msgid "Remove"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:425
#: app/views/admin/onboarding/membership.php:56
msgid "One-time"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:426
#: app/views/admin/onboarding/membership.php:62
msgid "Recurring (Monthly)"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:427
msgid "Recurring (Anually)"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:509
#: app/views/admin/onboarding/features.php:31
msgid "Course Creator"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:510
#: app/views/admin/onboarding/features.php:64
msgid "Digital Downloads"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:511
#: app/views/admin/onboarding/features.php:80
msgid "Member Community"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:512
#: app/views/admin/onboarding/features.php:96
msgid "Zapier Integration"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:513
#: app/views/admin/onboarding/features.php:112
msgid "Gifting"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:514
#: app/views/admin/onboarding/features.php:128
msgid "Corporate Accounts"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:515
#: app/views/admin/onboarding/features.php:144
msgid "Affiliate Program"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:599
#: app/lib/activation.php:11
#: app/views/admin/options/form.php:60
#: app/views/admin/products/registration.php:80
msgid "Thank You"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:603
#: app/lib/activation.php:15
#: app/lib/MeprAccountLinksWidget.php:92
#: app/views/admin/options/form.php:21
#: app/views/admin/options/form.php:64
#: app/views/admin/readylaunch/options.php:150
msgid "Account"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:621
msgid "Membership registration"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:631
msgid "MemberPress login"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:641
msgid "Manage account"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:744
msgid "Pro"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:746
msgid "Upgrade to Pro"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:747
msgid "To unlock selected features, upgrade to Pro."
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:750
msgid "Plus"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:752
msgid "Upgrade to Plus"
msgstr ""

#: app/helpers/MeprOnboardingHelper.php:753
msgid "To unlock selected features, upgrade to Plus."
msgstr ""

#: app/helpers/MeprOptionsHelper.php:29
msgid "- Auto Create New Page -"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:47
#: app/views/admin/emails/options.php:20
#: app/views/admin/emails/options.php:21
#: app/views/admin/members/row.php:55
#: app/views/admin/rules/rules_meta_box.php:13
#: app/views/admin/rules/rules_meta_box.php:30
#: app/views/admin/rules/rules_meta_box.php:46
#: app/views/admin/subscriptions/row.php:73
#: app/views/admin/transactions/row.php:76
msgid "Edit"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:64
#: app/helpers/MeprTransactionsHelper.php:68
#: app/models/MeprOptions.php:1275
#: app/models/MeprOptions.php:1276
msgid "Manual"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:195
msgid "Stripe (Recommended)"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:199
msgid "PayPal (Recommended)"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:577
msgid "Choose the excerpt type:"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:579
msgid "Excerpts:"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:589
#: app/helpers/MeprOptionsHelper.php:634
#: app/helpers/MeprOptionsHelper.php:665
#: app/views/admin/members/new_member.php:59
#: app/views/admin/options/form.php:231
#: js/blocks/protected-content/index.js:83
#: js/build/blocks.js:1
msgid "Hide"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:591
msgid "More Tag"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:592
msgid "Post Excerpt"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:593
#: app/helpers/MeprOptionsHelper.php:635
#: app/helpers/MeprProductsHelper.php:47
#: app/views/admin/products/advanced.php:23
#: app/views/admin/products/price_box.php:40
#: app/views/admin/products/stripe_tax.php:12
msgid "Custom"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:606
msgid "Show the first %s characters of your content"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:630
msgid "Unauthorized Message:"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:639
msgid "Enter your custom unauthorized message here:"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:660
msgid "Login Form:"
msgstr ""

#: app/helpers/MeprOptionsHelper.php:664
#: app/views/admin/members/new_member.php:59
#: app/views/admin/options/form.php:232
#: app/views/admin/options/form.php:439
#: app/views/admin/options/form.php:457
#: js/blocks/protected-content/index.js:82
#: js/build/blocks.js:1
msgid "Show"
msgstr ""

#: app/helpers/MeprProductsHelper.php:22
#: app/views/admin/products/form.php:60
#: app/views/admin/products/form.php:184
#: app/views/admin/reminders/trigger.php:10
msgid "months"
msgstr ""

#: app/helpers/MeprProductsHelper.php:23
#: app/views/admin/products/form.php:59
#: app/views/admin/products/form.php:183
#: app/views/admin/reminders/trigger.php:9
msgid "weeks"
msgstr ""

#: app/helpers/MeprProductsHelper.php:44
msgid "Weekly"
msgstr ""

#: app/helpers/MeprProductsHelper.php:45
msgid "Every 3 Months"
msgstr ""

#: app/helpers/MeprProductsHelper.php:46
msgid "Every 6 Months"
msgstr ""

#: app/helpers/MeprProductsHelper.php:92
msgid "Add Benefit"
msgstr ""

#: app/helpers/MeprProductsHelper.php:189
msgid "Everyone"
msgstr ""

#: app/helpers/MeprProductsHelper.php:190
msgid "Guests"
msgstr ""

#: app/helpers/MeprProductsHelper.php:192
msgid "No One (Disabled)"
msgstr ""

#: app/helpers/MeprProductsHelper.php:211
msgid "no active memberships"
msgstr ""

#: app/helpers/MeprProductsHelper.php:212
msgid "any membership"
msgstr ""

#: app/helpers/MeprProductsHelper.php:213
msgid "subscribed to this membership before"
msgstr ""

#: app/helpers/MeprProductsHelper.php:214
msgid "NOT subscribed to this membership before"
msgstr ""

#: app/helpers/MeprProductsHelper.php:215
msgid "NOT subscribed to any membership before"
msgstr ""

#: app/helpers/MeprProductsHelper.php:237
msgid "who currently have"
msgstr ""

#: app/helpers/MeprProductsHelper.php:238
msgid "who had"
msgstr ""

#: app/helpers/MeprProductsHelper.php:468
msgid " (renewal for %1$s to %2$s)"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:75
msgid "Subscription is expiring in %1$d %2$s"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:81
msgid "Subscription Expired"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:83
msgid "Subscription expired %1$d %2$s ago"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:93
msgid "Subscription is renewing in %1$d %2$s"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:99
msgid "Subscription Renewed"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:101
msgid "Subscription renewed %1$d %2$s ago"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:111
msgid "Credit Card is Expiring in %1$d %2$s"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:117
msgid "Credit Card Expired"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:119
msgid "Credit Card Expired %1$d %2$s ago"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:133
msgid "Member Signed Up %1$d %2$s ago"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:147
msgid "Sign Up Abandoned %1$d %2$s ago"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:155
msgid "Subscription Trial Period is Ending Soon"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:157
msgid "Subscription trial period is ending in %1$d %2$s"
msgstr ""

#: app/helpers/MeprRemindersHelper.php:199
#: app/views/admin/coupons/form.php:263
msgid "Hold the Control Key (Command Key on the Mac) in order to select or deselect multiple memberships"
msgstr ""

#: app/helpers/MeprReportsHelper.php:31
#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:199
msgid "All"
msgstr ""

#: app/helpers/MeprReportsHelper.php:54
msgid "Month:"
msgstr ""

#: app/helpers/MeprReportsHelper.php:56
#: app/helpers/MeprReportsHelper.php:111
msgid "Year:"
msgstr ""

#: app/helpers/MeprReportsHelper.php:58
#: app/helpers/MeprReportsHelper.php:113
#: app/helpers/MeprReportsHelper.php:159
msgid "Membership:"
msgstr ""

#: app/helpers/MeprReportsHelper.php:61
#: app/helpers/MeprReportsHelper.php:116
#: app/helpers/MeprReportsHelper.php:162
#: app/views/admin/members/search_box.php:20
#: app/views/admin/subscriptions/search_box.php:26
#: app/views/admin/table_controls.php:22
#: app/views/admin/transactions/search_box.php:26
msgid "Go"
msgstr ""

#: app/helpers/MeprReportsHelper.php:75
#: app/lib/MeprUtils.php:112
msgid "January"
msgstr ""

#: app/helpers/MeprReportsHelper.php:76
#: app/lib/MeprUtils.php:113
msgid "February"
msgstr ""

#: app/helpers/MeprReportsHelper.php:77
#: app/lib/MeprUtils.php:114
msgid "March"
msgstr ""

#: app/helpers/MeprReportsHelper.php:78
#: app/lib/MeprUtils.php:115
msgid "April"
msgstr ""

#: app/helpers/MeprReportsHelper.php:80
#: app/lib/MeprUtils.php:117
msgid "June"
msgstr ""

#: app/helpers/MeprReportsHelper.php:81
#: app/lib/MeprUtils.php:118
msgid "July"
msgstr ""

#: app/helpers/MeprReportsHelper.php:82
#: app/lib/MeprUtils.php:119
msgid "August"
msgstr ""

#: app/helpers/MeprReportsHelper.php:83
#: app/lib/MeprUtils.php:120
msgid "September"
msgstr ""

#: app/helpers/MeprReportsHelper.php:84
#: app/lib/MeprUtils.php:121
msgid "October"
msgstr ""

#: app/helpers/MeprReportsHelper.php:85
#: app/lib/MeprUtils.php:122
msgid "November"
msgstr ""

#: app/helpers/MeprReportsHelper.php:86
#: app/lib/MeprUtils.php:123
msgid "December"
msgstr ""

#: app/helpers/MeprRulesHelper.php:22
#: app/helpers/MeprRulesHelper.php:70
msgid "Rule Type cannot be blank"
msgstr ""

#: app/helpers/MeprRulesHelper.php:23
msgid "- Please Select -"
msgstr ""

#: app/helpers/MeprRulesHelper.php:71
msgid "- Select Type -"
msgstr ""

#: app/helpers/MeprRulesHelper.php:176
msgid "Begin Typing Name"
msgstr ""

#: app/helpers/MeprRulesHelper.php:178
msgid "Member Name must be between 1-100 characters"
msgstr ""

#: app/helpers/MeprRulesHelper.php:190
msgid "Enter Capability"
msgstr ""

#: app/helpers/MeprRulesHelper.php:265
#: app/helpers/MeprRulesHelper.php:319
msgid "Content cannot be blank"
msgstr ""

#: app/helpers/MeprRulesHelper.php:266
msgid "Regular Expression"
msgstr ""

#: app/helpers/MeprRulesHelper.php:280
msgid "All Except IDs"
msgstr ""

#: app/helpers/MeprRulesHelper.php:281
msgid "If you want to exclude all except some Pages or Posts, list their IDs here in a comma separated list. Example: 102, 32, 546"
msgstr ""

#: app/helpers/MeprRulesHelper.php:295
msgid "There is not yet any content to select for this rule type."
msgstr ""

#: app/helpers/MeprRulesHelper.php:319
msgid "Begin Typing Title"
msgstr ""

#: app/helpers/MeprRulesHelper.php:403
#: app/models/MeprRule.php:1601
msgid "member registers"
msgstr ""

#: app/helpers/MeprRulesHelper.php:404
msgid "fixed date"
msgstr ""

#: app/helpers/MeprRulesHelper.php:405
#: app/models/MeprRule.php:1607
msgid "member purchases any membership for this rule"
msgstr ""

#: app/helpers/MeprRulesHelper.php:408
#: app/helpers/MeprRulesHelper.php:412
msgid "member purchases"
msgstr ""

#: app/helpers/MeprSubscriptionsHelper.php:119
#: app/helpers/MeprSubscriptionsHelper.php:202
#: app/helpers/MeprTransactionsHelper.php:74
#: app/helpers/MeprTransactionsHelper.php:209
#: app/views/admin/subscriptions/search_box.php:22
#: app/views/admin/transactions/search_box.php:22
msgid "%1$s (%2$s)"
msgstr ""

#: app/helpers/MeprTransactionsHelper.php:330
#: app/helpers/MeprTransactionsHelper.php:335
#: app/helpers/MeprTransactionsHelper.php:340
#: app/helpers/MeprTransactionsHelper.php:651
#: app/helpers/MeprTransactionsHelper.php:656
#: app/helpers/MeprTransactionsHelper.php:661
msgid "Coupon Code '%s'"
msgstr ""

#: app/helpers/MeprTransactionsHelper.php:514
msgid "Now"
msgstr ""

#: app/helpers/MeprTransactionsHelper.php:589
msgid "Initial Payment"
msgstr ""

#: app/helpers/MeprTransactionsHelper.php:591
msgid "Subscription Payment"
msgstr ""

#: app/helpers/MeprTransactionsHelper.php:594
msgid "Payment"
msgstr ""

#. Translators: %1$s: gateway name, %2$s: transaction ID.
#: app/helpers/MeprTransactionsHelper.php:838
msgid "%1$s Transaction ID: %2$s"
msgstr ""

#. Translators: %s: transaction ID.
#: app/helpers/MeprTransactionsHelper.php:845
msgid "Transaction ID: %s"
msgstr ""

#: app/helpers/MeprUsersHelper.php:55
msgid "F j, Y, g:i a"
msgstr ""

#: app/helpers/MeprUsersHelper.php:177
msgid "A URL must be prefixed with a protocol (eg. http://)"
msgstr ""

#: app/helpers/MeprUsersHelper.php:203
msgid "Replace"
msgstr ""

#: app/helpers/MeprUsersHelper.php:504
msgid "(required)"
msgstr ""

#: app/helpers/MeprUsersHelper.php:509
msgid "%1$s:%2$s"
msgstr ""

#: app/integrations/bbpress/Integration.php:116
msgid "Forum Profile"
msgstr ""

#: app/integrations/ifmenu/Integration.php:33
msgid "Any Membership"
msgstr ""

#: app/integrations/ifmenu/Integration.php:34
#: app/integrations/ifmenu/Integration.php:52
msgid "Active on Membership"
msgstr ""

#: app/integrations/ifmenu/Integration.php:70
msgid "Active Membership Rule"
msgstr ""

#: app/integrations/powerpress/Integration.php:28
msgid "MemberPress Active Member"
msgstr ""

#: app/integrations/powerpress/Integration.php:33
msgid "MemberPress Membership: %s"
msgstr ""

#: app/integrations/powerpress/Integration.php:40
msgid "MemberPress Rule: %s"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:207
msgid "Sales Tax"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:220
msgid "Chicago Lease Tax"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:470
msgid "MemberPress - Stripe Tax Payment Method"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:485
msgid "MemberPress is correctly configured to use Stripe Tax"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:493
msgid "The connection between MemberPress and Stripe Tax is correctly configured."
msgstr ""

#: app/integrations/stripe-tax/Integration.php:502
msgid "MemberPress is not correctly configured to use Stripe Tax"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:508
msgid "The connection between MemberPress and Stripe Tax is not correctly configured. In the MemberPress settings, select a Stripe payment method to use for Stripe Tax."
msgstr ""

#: app/integrations/stripe-tax/Integration.php:514
msgid "Configure MemberPress Taxes"
msgstr ""

#. Translators: %1$s open link tag, %2$s: close link tag.
#: app/integrations/stripe-tax/Integration.php:542
msgid "The connection between MemberPress and Stripe Tax is not correctly configured. To fix this issue, %1$sclick here%2$s to visit the MemberPress Tax settings, where you can select a Stripe payment method to use for Stripe Tax."
msgstr ""

#: app/integrations/stripe-tax/Integration.php:616
msgid "MemberPress makes taxes fast and easy! Enable automatic tax rate calculation with the Stripe Tax API."
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/integrations/stripe-tax/Integration.php:621
msgid "Stripe Tax API pricing starts at 0.50 USD for each transaction calculated within your registered tax location (includes 10 calculation API calls per transaction; 0.05 USD per additional call). To learn more visit the %1$sStripe Tax pricing page%2$s."
msgstr ""

#: app/integrations/stripe-tax/Integration.php:628
msgid "Activate Automatic Tax Calculations with Stripe"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:629
msgid "I don't need to collect taxes"
msgstr ""

#: app/integrations/stripe-tax/Integration.php:635
msgid "Great, if this ever changes you can easily enable this in Settings &rarr; Taxes."
msgstr ""

#: app/integrations/stripe-tax/Integration.php:638
#: app/views/admin/onboarding/payments.php:100
msgid "Stripe Tax is now enabled"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/integrations/stripe-tax/Integration.php:643
msgid "In the Stripe dashboard, please ensure that a %1$sRegistration is added%2$s for each location where tax should be collected."
msgstr ""

#: app/integrations/stripe-tax/Integration.php:651
#: app/views/admin/onboarding/payments.php:115
msgid "Stripe Tax could not be enabled"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag, %3$s: open link tag, %4$s: close link tag.
#: app/integrations/stripe-tax/Integration.php:656
#: app/views/admin/onboarding/payments.php:105
#: app/views/admin/onboarding/payments.php:120
#: app/views/admin/taxes/stripe_tax_options.php:58
msgid "In the Stripe dashboard, please ensure that %1$sStripe Tax is enabled%2$s and that a %3$sRegistration is added%4$s for each location where tax should be collected."
msgstr ""

#: app/integrations/stripe-tax/Integration.php:665
msgid "If you have more than one Stripe payment method, you can configure which payment method to use for Stripe Tax at MemberPress &rarr; Settings &rarr; Taxes."
msgstr ""

#. Translators: %1$s open link tag, %2$s: close link tag, %3$s open link tag, %4$s: close link tag.
#: app/integrations/stripe-tax/Integration.php:691
msgid "Stripe Tax was deactivated because it is not enabled in the Stripe dashboard. Please ensure that %1$sStripe Tax is enabled%2$s at Stripe, then go to %3$sMemberPress &rarr; Settings &rarr; Taxes%4$s to reactivate Stripe Tax."
msgstr ""

#: app/integrations/taxjar/Integration.php:109
msgid "The TaxJar API returned the following error after a recent request: "
msgstr ""

#: app/integrations/taxjar/Integration.php:111
msgid "Please resolve the issue to continue using the TaxJar API successfully, or contact MemberPress for support."
msgstr ""

#: app/integrations/taxjar/Integration.php:113
msgid "TaxJar API Error: Bad Request"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/integrations/taxjar/Integration.php:137
msgid "You must use a valid 2-character state code as your %1$sBusiness Address%2$s state field for TaxJar to work properly."
msgstr ""

#: app/integrations/taxjar/Integration.php:235
msgid "tax"
msgstr ""

#: app/integrations/two-factor/Integration.php:70
#: app/integrations/two-factor/Integration.php:116
msgctxt "ui"
msgid "2FA"
msgstr ""

#: app/integrations/two-factor/Integration.php:233
msgid "Two-Factor options updated."
msgstr ""

#: app/integrations/two-factor/Integration.php:248
msgid "Two-Factor Authentication"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/integrations/two-factor/Integration.php:265
msgid "To update your Two-Factor options, you must first %1$srevalidate your session%2$s."
msgstr ""

#: app/integrations/two-factor/Integration.php:281
msgid "To prevent being locked out of your account, consider enabling a backup method like Recovery Codes in case you lose access to your primary authentication method."
msgstr ""

#: app/integrations/two-factor/Integration.php:299
msgid "Enable %s"
msgstr ""

#: app/integrations/two-factor/Integration.php:322
msgid "Primary Method"
msgstr ""

#: app/integrations/two-factor/Integration.php:336
msgid "Select the primary method to use for two-factor authentication when signing into this site."
msgstr ""

#: app/integrations/two-factor/Integration.php:341
msgid "Save Options"
msgstr ""

#: app/integrations/user-roles/MeprUserRoles.php:358
msgid "User Roles for this Membership"
msgstr ""

#: app/integrations/user-roles/MeprUserRoles.php:362
msgid "Enable Membership User Roles"
msgstr ""

#: app/integrations/user-roles/MeprUserRoles.php:363
msgid "These Roles will be added to and removed from the user automatically based on their current subscription status to this Membership level. When they are not active on any Memberships, their Role will go back to the Role you have set in your WordPress -> Settings -> General page."
msgstr ""

#: app/integrations/user-roles/MeprUserRoles.php:374
msgid "Hold the Control Key (Command Key on the Mac) in order to select or deselect multiple roles"
msgstr ""

#: app/jobs/MeprAuthorizeRetryJob.php:32
msgid "No subscription data available"
msgstr ""

#: app/jobs/MeprAuthorizeRetryJob.php:43
msgid "There was a problem with the Authorize.net API request."
msgstr ""

#: app/jobs/MeprEmailJob.php:18
msgid "\"class\" cannot be blank"
msgstr ""

#: app/jobs/MeprEmailJob.php:22
msgid "\"to\" cannot be blank"
msgstr ""

#: app/jobs/MeprUpdateStripeMetadataJob.php:18
msgid "MeprUpdateStripeMetadataJob: transaction_id wasn't set."
msgstr ""

#: app/lib/MeprAccountLinksWidget.php:16
msgid "Place account links on any page with a sidebar region"
msgstr ""

#: app/lib/MeprAccountLinksWidget.php:96
#: app/lib/MeprLoginWidget.php:107
#: app/lib/MeprSubscriptionsWidget.php:133
#: app/views/admin/products/price_box.php:25
msgid "Title:"
msgstr ""

#. Translators: Accessibility text.
#: app/lib/MeprBaseDrm.php:155
msgid "(opens in a new tab)"
msgstr ""

#: app/lib/MeprBaseEmail.php:235
msgid "No email recipient has been set."
msgstr ""

#: app/lib/MeprBaseEmail.php:418
#: app/views/admin/users/extra_profile_fields.php:12
msgid "Privacy Policy"
msgstr ""

#: app/lib/MeprBaseGateway.php:508
msgctxt "ui"
msgid "You already completed your payment to this subscription. %1$sClick here to view your subscriptions%2$s."
msgstr ""

#: app/lib/MeprBaseGateway.php:511
msgid "Sorry but we can't process your payment at this time. Try back later."
msgstr ""

#: app/lib/MeprBaseGateway.php:627
#: app/views/admin/subscriptions/edit.php:27
#: app/views/admin/transactions/edit_trans.php:34
msgid "Update"
msgstr ""

#: app/lib/MeprBaseGateway.php:658
#: app/views/account/subscriptions.php:196
#: app/views/readylaunch/account/subscriptions.php:106
msgctxt "ui"
msgid "Re-Subscribe"
msgstr ""

#: app/lib/MeprBaseGateway.php:661
msgid "Other Memberships"
msgstr ""

#: app/lib/MeprBaseGateway.php:677
msgid "Are you sure you want to pause this subscription?"
msgstr ""

#: app/lib/MeprBaseGateway.php:677
#: app/views/admin/subscriptions/row.php:63
msgid "Pause"
msgstr ""

#: app/lib/MeprBaseGateway.php:686
msgid "Are you sure you want to resume this subscription?"
msgstr ""

#: app/lib/MeprBaseGateway.php:688
#: app/lib/MeprBaseGateway.php:702
#: app/models/MeprSubscription.php:677
#: app/models/MeprSubscription.php:685
#: app/models/MeprSubscription.php:686
#: app/models/MeprSubscription.php:1067
msgid "Yes"
msgstr ""

#: app/lib/MeprBaseGateway.php:689
#: app/lib/MeprBaseGateway.php:703
#: app/models/MeprSubscription.php:679
#: app/models/MeprSubscription.php:1068
msgid "No"
msgstr ""

#: app/lib/MeprBaseGateway.php:692
#: app/views/admin/subscriptions/row.php:66
msgid "Resume"
msgstr ""

#: app/lib/MeprBaseGateway.php:699
msgid "Are you sure you want to cancel this subscription?"
msgstr ""

#. Translators: In this string, %1$s is the Blog Name/Title and %2$s is the Name of the Payment Method.
#: app/lib/MeprBaseGateway.php:784
msgid "[%1$s] %2$s Debug Email"
msgstr ""

#: app/lib/MeprBaseModel.php:358
#: app/lib/MeprBaseModel.php:373
msgid "%s must not be empty"
msgstr ""

#: app/lib/MeprBaseModel.php:388
msgid "%s must be true or false"
msgstr ""

#: app/lib/MeprBaseModel.php:403
msgid "%s must be an array"
msgstr ""

#: app/lib/MeprBaseModel.php:419
msgid "%1$s must be %2$s"
msgstr ""

#: app/lib/MeprBaseModel.php:419
msgid "or"
msgstr ""

#: app/lib/MeprBaseModel.php:434
msgid "%1$s (%2$s) must be a valid url"
msgstr ""

#: app/lib/MeprBaseModel.php:451
msgid "%1$s (%2$s) must be a valid representation of currency"
msgstr ""

#: app/lib/MeprBaseModel.php:468
msgid "%1$s (%2$s) must be a valid number"
msgstr ""

#: app/lib/MeprBaseModel.php:483
msgid "%1$s (%2$s) must be a valid email"
msgstr ""

#: app/lib/MeprBaseModel.php:498
msgid "%1$s (%2$s) must be a valid phone number"
msgstr ""

#: app/lib/MeprBaseModel.php:513
msgid "%1$s (%2$s) must be a valid IP Address"
msgstr ""

#: app/lib/MeprBaseModel.php:528
msgid "%1$s (%2$s) must be a valid date"
msgstr ""

#: app/lib/MeprBaseModel.php:543
msgid "%1$s (%2$s) must be a valid timestamp"
msgstr ""

#: app/lib/MeprBaseModel.php:559
msgid "%1$s (%2$s) must match the regex pattern: %3$s"
msgstr ""

#: app/lib/MeprCptCtrl.php:126
msgid "%1$s updated. <a href=\"%2$s\">View %3$s</a>"
msgstr ""

#: app/lib/MeprCptCtrl.php:132
msgid "%1$s updated."
msgstr ""

#: app/lib/MeprCptCtrl.php:135
msgid "Custom field updated."
msgstr ""

#: app/lib/MeprCptCtrl.php:136
msgid "Custom field deleted."
msgstr ""

#: app/lib/MeprCptCtrl.php:137
msgid "%s updated."
msgstr ""

#: app/lib/MeprCptCtrl.php:138
msgid "%1$s restored to revision from %2$s"
msgstr ""

#: app/lib/MeprCptCtrl.php:142
msgid "%1$s published. <a href=\"%2$s\">View %3$s</a>"
msgstr ""

#: app/lib/MeprCptCtrl.php:148
msgid "%1$s published."
msgstr ""

#: app/lib/MeprCptCtrl.php:151
msgid "%s saved."
msgstr ""

#: app/lib/MeprCptCtrl.php:155
msgid "%1$s submitted. <a target=\"_blank\" href=\"%2$s\">Preview %3$s</a>"
msgstr ""

#: app/lib/MeprCptCtrl.php:161
msgid "%1$s scheduled for: <strong>%2$s</strong>. <a target=\"_blank\" href=\"%3$s\">Preview %4$s</a>"
msgstr ""

#: app/lib/MeprCptCtrl.php:168
msgid "%1$s draft updated. <a target=\"_blank\" href=\"%2$s\">Preview %3$s</a>"
msgstr ""

#: app/lib/MeprCptCtrl.php:174
msgid "%s submitted."
msgstr ""

#: app/lib/MeprCptCtrl.php:176
msgid "%1$s scheduled for: <strong>%2$s</strong>."
msgstr ""

#: app/lib/MeprCptCtrl.php:180
msgid "%s draft updated."
msgstr ""

#: app/lib/MeprCptModel.php:137
msgid "This was unable to be saved."
msgstr ""

#: app/lib/MeprCptModel.php:175
msgid "This was unable to be deleted."
msgstr ""

#: app/lib/MeprCtrlFactory.php:40
msgid "Ctrl wasn't found"
msgstr ""

#: app/lib/MeprDrmInvalid.php:63
msgid "MemberPress - Invalid License"
msgstr ""

#: app/lib/MeprDrmNokey.php:65
msgid "MemberPress - Licence Key Missing"
msgstr ""

#: app/lib/MeprEmailFactory.php:24
msgid "Email wasn't found"
msgstr ""

#: app/lib/MeprEmailFactory.php:33
msgid "Not a valid email object: %1$s is not an instance of %2$s"
msgstr ""

#: app/lib/MeprGatewayFactory.php:23
msgid "Gateway wasn't found"
msgstr ""

#: app/lib/MeprGatewayFactory.php:31
msgid "Not a valid gateway"
msgstr ""

#: app/lib/MeprJobFactory.php:20
msgid "Job class wasn't found for %s"
msgstr ""

#: app/lib/MeprJobFactory.php:29
msgid "%s is not a valid job object."
msgstr ""

#: app/lib/MeprJobFactory.php:43
msgid "Job Paths %s"
msgstr ""

#: app/lib/MeprJobs.php:72
msgid "MemberPress Jobs Worker"
msgstr ""

#: app/lib/MeprJobs.php:77
msgid "MemberPress Jobs Cleanup"
msgstr ""

#: app/lib/MeprJobs.php:104
msgid "Starting Job - %1$s (%2$s): %3$s"
msgstr ""

#: app/lib/MeprJobs.php:106
msgid "Job Completed - %1$s (%2$s)"
msgstr ""

#: app/lib/MeprJobs.php:109
msgid "No class was specified in the job config"
msgstr ""

#: app/lib/MeprJobs.php:110
msgid "Job Failed: No class"
msgstr ""

#: app/lib/MeprJobs.php:114
msgid "Job Failed: %s"
msgstr ""

#: app/lib/MeprJobs.php:452
msgid "Background Jobs"
msgstr ""

#: app/lib/MeprJobs.php:457
msgid "Asynchronous Emails"
msgstr ""

#: app/lib/MeprJobs.php:460
msgid "Send Emails Asynchronously in the Background"
msgstr ""

#: app/lib/MeprJobs.php:461
msgid "This option will allow you to send all MemberPress emails asynchronously. This option can increase the speed & performance of the checkout process but may also result in a delay in when emails are recieved. <strong>Note:</strong> This option requires wp-cron to be enabled and working."
msgstr ""

#: app/lib/MeprLoginWidget.php:16
msgid "Place a MemberPress Login on any page with a sidebar region"
msgstr ""

#: app/lib/MeprLoginWidget.php:112
msgid "Use Login Redirect URL?"
msgstr ""

#. Translators: %1$s: the model type, %2$s: the model title, %3$s: the model ID, %4$s: the error message.
#: app/lib/MeprMigrator.php:120
msgid "Failed to migrate %1$s \"%2$s\" [ID: %3$s]: %4$s"
msgstr ""

#: app/lib/MeprNotifications.php:242
msgid "Notification Icon"
msgstr ""

#: app/lib/MeprSubscriptionsTable.php:121
#: app/lib/MeprSubscriptionsTable.php:128
#: app/lib/MeprTransactionsTable.php:79
msgid "User Email"
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:16
msgid "Show a list of the current member's Subscriptions with optional links to each Membership's \"Membership Access URL\" setting."
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:124
msgid "Your Subscriptions"
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:137
#: js/blocks/subscriptions/edit.js:62
#: js/build/blocks.js:1
msgid "Not Logged In Message:"
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:141
#: js/blocks/subscriptions/edit.js:68
#: js/build/blocks.js:1
msgid "No Subscriptions Message:"
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:145
#: js/blocks/subscriptions/edit.js:74
#: js/build/blocks.js:1
msgid "Top Description (optional):"
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:149
#: js/blocks/subscriptions/edit.js:80
#: js/build/blocks.js:1
msgid "Bottom Description (optional):"
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:154
msgid "Use Membership Access URLs?"
msgstr ""

#: app/lib/MeprSubscriptionsWidget.php:155
msgid "Makes the Subscription name clickable, pointing to the Membership Access URL you have set in the Membership settings (Advanced tab)."
msgstr ""

#: app/lib/MeprUtils.php:176
msgid "Hour"
msgid_plural "Hours"
msgstr[0] ""
msgstr[1] ""

#: app/lib/MeprUtils.php:178
msgid "Day"
msgid_plural "Days"
msgstr[0] ""
msgstr[1] ""

#: app/lib/MeprUtils.php:180
msgid "Week"
msgid_plural "Weeks"
msgstr[0] ""
msgstr[1] ""

#: app/lib/MeprUtils.php:182
msgid "Month"
msgid_plural "Months"
msgstr[0] ""
msgstr[1] ""

#: app/lib/MeprUtils.php:184
msgid "Year"
msgid_plural "Years"
msgstr[0] ""
msgstr[1] ""

#: app/lib/MeprUtils.php:1791
#: app/lib/MeprUtils.php:1804
msgid "Your CSV file must contain the column: %s"
msgstr ""

#: app/lib/MeprUtils.php:2333
msgid "*** MemberPress Error: %s"
msgstr ""

#: app/lib/MeprUtils.php:2345
msgid "*** MemberPress Debug: %s"
msgstr ""

#: app/lib/MeprUtils.php:3518
msgctxt "ui"
msgid "Checked"
msgstr ""

#: app/lib/MeprUtils.php:3988
msgid "NEW"
msgstr ""

#. Translators: %s: the table name.
#: app/lib/migrators/MeprMigratorLearnDash.php:105
msgid "Migration not started: table `%s` not found, try updating LearnDash to the latest version first."
msgstr ""

#: app/lib/migrators/MeprMigratorLearnDash.php:116
msgid "Please update the MemberPress Courses plugin to the latest version and try again."
msgstr ""

#: app/lib/migrators/MeprMigratorLearnDash.php:212
msgid "Please activate the MemberPress Courses plugin and try again."
msgstr ""

#: app/lib/migrators/MeprMigratorLearnDash.php:216
msgid "Please install and activate the MemberPress Courses plugin and try again."
msgstr ""

#: app/lib/migrators/MeprMigratorLearnDash.php:413
#: app/lib/migrators/MeprMigratorLearnDash.php:619
#: app/lib/migrators/MeprMigratorLearnDash.php:853
msgid "Untitled"
msgstr ""

#: app/lib/migrators/MeprMigratorLearnDash.php:549
msgid "Failed to migrate course section \"%1$s\": %2$s"
msgstr ""

#: app/lib/migrators/MeprMigratorLearnDash.php:579
msgid "Materials"
msgstr ""

#: app/lib/migrators/MeprMigratorLearnDash.php:956
msgid "Lessons"
msgstr ""

#: app/models/MeprCoupon.php:304
msgid "None Selected"
msgstr ""

#: app/models/MeprEvent.php:278
msgid "An unsupported Event type was used"
msgstr ""

#: app/models/MeprOptions.php:261
msgid "Welcome to %s"
msgstr ""

#: app/models/MeprOptions.php:346
msgctxt "ui"
msgid "I have read and agree to the Terms Of Service"
msgstr ""

#: app/models/MeprOptions.php:350
#: app/models/MeprOptions.php:923
msgctxt "ui"
msgid "This site collects names, emails and other user information. I consent to the terms set forth in the %Privacy Policy%."
msgstr ""

#: app/models/MeprOptions.php:416
msgid "You are unauthorized to view this page."
msgstr ""

#: app/models/MeprOptions.php:755
msgid "The Membership Pages Slug must be set"
msgstr ""

#: app/models/MeprOptions.php:759
msgid "The Group Pages Slug must be set"
msgstr ""

#: app/models/MeprOptions.php:763
msgid "The Membership Pages Slug must only contain letters, numbers and dashes."
msgstr ""

#: app/models/MeprOptions.php:767
msgid "The Group Pages Slug must only contain letters, numbers and dashes."
msgstr ""

#: app/models/MeprOptions.php:771
msgid "At least one Admin Email Address must be set"
msgstr ""

#: app/models/MeprOptions.php:776
msgid "The Admin Email Address field must contain 1 or more valid email addresses"
msgstr ""

#: app/models/MeprOptions.php:921
msgctxt "ui"
msgid "I have read and agree to the Terms of Service"
msgstr ""

#: app/models/MeprOptions.php:1435
msgctxt "ui"
msgid "Address Line 1"
msgstr ""

#: app/models/MeprOptions.php:1443
msgctxt "ui"
msgid "Address Line 2"
msgstr ""

#: app/models/MeprOptions.php:1451
msgctxt "ui"
msgid "City"
msgstr ""

#: app/models/MeprOptions.php:1459
msgctxt "ui"
msgid "Country"
msgstr ""

#: app/models/MeprOptions.php:1467
msgctxt "ui"
msgid "State/Province"
msgstr ""

#: app/models/MeprOptions.php:1475
msgctxt "ui"
msgid "Zip/Postal Code"
msgstr ""

#: app/models/MeprReminder.php:181
msgid "Subscription Expires"
msgstr ""

#: app/models/MeprReminder.php:183
msgid "Subscription Renews"
msgstr ""

#: app/models/MeprReminder.php:185
msgid "Credit Card Expires"
msgstr ""

#: app/models/MeprReminder.php:187
msgid "Member Signs Up"
msgstr ""

#: app/models/MeprReminder.php:224
msgid "%1$d %2$s %3$s %4$s"
msgstr ""

#: app/models/MeprReminder.php:275
#: app/views/admin/members/search_box.php:5
#: app/views/admin/subscriptions/search_box.php:5
#: app/views/admin/transactions/search_box.php:5
msgid "All Memberships"
msgstr ""

#: app/models/MeprRule.php:225
msgid "Member"
msgstr ""

#: app/models/MeprRule.php:229
msgid "Role"
msgstr ""

#: app/models/MeprRule.php:233
msgid "Capability"
msgstr ""

#: app/models/MeprRule.php:248
msgid "Is"
msgstr ""

#: app/models/MeprRule.php:353
msgid "All Posts"
msgstr ""

#: app/models/MeprRule.php:354
msgid "A Single Post"
msgstr ""

#: app/models/MeprRule.php:355
msgid "Posts Categorized"
msgstr ""

#: app/models/MeprRule.php:356
msgid "Posts Tagged"
msgstr ""

#: app/models/MeprRule.php:359
msgid "All Pages"
msgstr ""

#: app/models/MeprRule.php:360
msgid "A Single Page"
msgstr ""

#: app/models/MeprRule.php:361
msgid "Child Pages of"
msgstr ""

#: app/models/MeprRule.php:375
msgid "All %s"
msgstr ""

#: app/models/MeprRule.php:380
msgid "Child %s of"
msgstr ""

#: app/models/MeprRule.php:399
msgid "All Content Tagged"
msgstr ""

#: app/models/MeprRule.php:401
msgid "All Content Categorized"
msgstr ""

#: app/models/MeprRule.php:403
msgid "All Content with %1$s"
msgstr ""

#: app/models/MeprRule.php:420
msgid "%1$s Tagged"
msgstr ""

#: app/models/MeprRule.php:424
msgid "%1$s Categorized"
msgstr ""

#: app/models/MeprRule.php:427
msgid "%1$s with %2$s"
msgstr ""

#: app/models/MeprRule.php:432
msgid "All Content"
msgstr ""

#: app/models/MeprRule.php:442
msgid "Partial"
msgstr ""

#: app/models/MeprRule.php:443
msgid "Custom URI"
msgstr ""

#: app/models/MeprRule.php:1583
msgid "day(s)"
msgstr ""

#: app/models/MeprRule.php:1584
msgid "week(s)"
msgstr ""

#: app/models/MeprRule.php:1585
msgid "month(s)"
msgstr ""

#: app/models/MeprRule.php:1586
msgid "year(s)"
msgstr ""

#: app/models/MeprRule.php:1611
msgid "member purchases %s"
msgstr ""

#: app/models/MeprSubscription.php:2916
msgid "MemberPress database migration failed: %1$s %2$s"
msgstr ""

#: app/models/MeprUser.php:917
msgid "The user was unable to be saved: %s"
msgstr ""

#: app/models/MeprUser.php:978
msgid "This user was unable to be deleted."
msgstr ""

#. Translators: In this string, %s is the Blog Name/Title.
#: app/models/MeprUser.php:1066
msgid "[%s] Password Reset"
msgstr ""

#. Translators: In this string, %s is the Blog Name/Title.
#: app/models/MeprUser.php:1084
msgid "[%s] Set Your New Password"
msgstr ""

#. Translators: In this string, %s is the Blog Name/Title.
#: app/models/MeprUser.php:1126
msgid "[%s] Password Lost/Changed"
msgstr ""

#. Translators: In this string, %s is the Blog Name/Title.
#: app/models/MeprUser.php:1140
msgctxt "ui"
msgid "[%s] Your new Password"
msgstr ""

#: app/models/MeprUser.php:1207
#: app/models/MeprUser.php:1359
msgid "Username must not be blank"
msgstr ""

#: app/models/MeprUser.php:1211
msgid "Username must only contain letters, numbers, spaces and/or underscores"
msgstr ""

#: app/models/MeprUser.php:1218
msgid "This username has already been taken. If you are an existing user, please %1$sLogin%2$s first. You will be redirected back here to complete your sign-up afterwards."
msgstr ""

#: app/models/MeprUser.php:1223
msgid "Email must be a real and properly formatted email address"
msgstr ""

#: app/models/MeprUser.php:1230
msgid "This email address has already been used. If you are an existing user, please %1$sLogin%2$s to complete your purchase. You will be redirected back here to complete your sign-up afterwards."
msgstr ""

#: app/models/MeprUser.php:1235
#: app/models/MeprUser.php:1424
msgid "You must enter a Password."
msgstr ""

#: app/models/MeprUser.php:1239
#: app/models/MeprUser.php:1428
msgid "You must enter a Password Confirmation."
msgstr ""

#: app/models/MeprUser.php:1243
#: app/models/MeprUser.php:1432
msgid "Your Password and Password Confirmation don't match."
msgstr ""

#: app/models/MeprUser.php:1250
msgid "Only humans are allowed to register."
msgstr ""

#: app/models/MeprUser.php:1261
msgid "Your coupon code is invalid."
msgstr ""

#: app/models/MeprUser.php:1265
msgid "You must agree to the Terms of Service"
msgstr ""

#: app/models/MeprUser.php:1269
msgid "You must agree to the Privacy Policy"
msgstr ""

#: app/models/MeprUser.php:1289
msgid "There are no active Payment Methods right now ... please contact the system administrator for help."
msgstr ""

#: app/models/MeprUser.php:1308
msgid "Invalid Payment Method"
msgstr ""

#: app/models/MeprUser.php:1324
msgid "Required add-on(s) cannot be removed from this sale."
msgstr ""

#: app/models/MeprUser.php:1328
msgid "One of the required add-ons cannot be removed from this sale."
msgstr ""

#: app/models/MeprUser.php:1374
#: app/models/MeprUser.php:1380
msgid "Your username or password was incorrect"
msgstr ""

#: app/models/MeprUser.php:1399
msgid "You must enter a Username or Email"
msgstr ""

#: app/models/MeprUser.php:1417
msgid "Your password must meet the minimum strength requirement."
msgstr ""

#: app/models/MeprUser.php:2146
msgid "%1$s"
msgstr ""

#: app/models/MeprUser.php:2146
msgid "%2$s, %3$s %4$s%5$s"
msgstr ""

#: app/views/account/home.php:29
#: app/views/checkout/form.php:47
#: app/views/checkout/spc_form.php:48
msgctxt "ui"
msgid "First Name:"
msgstr ""

#: app/views/account/home.php:31
#: app/views/checkout/form.php:49
#: app/views/checkout/spc_form.php:50
#: app/views/readylaunch/account/home.php:123
#: app/views/readylaunch/checkout/form.php:61
msgctxt "ui"
msgid "First Name Required"
msgstr ""

#: app/views/account/home.php:37
#: app/views/checkout/form.php:55
#: app/views/checkout/spc_form.php:56
msgctxt "ui"
msgid "Last Name:"
msgstr ""

#: app/views/account/home.php:39
#: app/views/checkout/form.php:57
#: app/views/checkout/spc_form.php:58
#: app/views/readylaunch/account/home.php:125
#: app/views/readylaunch/checkout/form.php:63
msgctxt "ui"
msgid "Last Name Required"
msgstr ""

#: app/views/account/home.php:49
#: app/views/checkout/form.php:92
#: app/views/checkout/spc_form.php:93
#: app/views/readylaunch/account/home.php:133
#: app/views/readylaunch/checkout/form.php:109
msgctxt "ui"
msgid "Email:*"
msgstr ""

#: app/views/account/home.php:50
#: app/views/checkout/form.php:93
#: app/views/checkout/spc_form.php:94
#: app/views/readylaunch/account/home.php:134
#: app/views/readylaunch/checkout/form.php:110
msgctxt "ui"
msgid "Invalid Email"
msgstr ""

#: app/views/account/home.php:61
msgctxt "ui"
msgid "Save Profile"
msgstr ""

#: app/views/account/home.php:69
#: app/views/readylaunch/account/home.php:64
#: app/views/readylaunch/layout/app.php:65
msgctxt "ui"
msgid "Change Password"
msgstr ""

#: app/views/account/logged_in_template.php:6
#: app/views/account/logged_in_widget.php:6
#: app/views/readylaunch/layout/app.php:63
msgctxt "ui"
msgid "Account"
msgstr ""

#: app/views/account/logged_in_template.php:8
#: app/views/account/logged_in_widget.php:7
#: app/views/account/nav.php:16
#: app/views/readylaunch/account/nav.php:27
#: app/views/readylaunch/layout/app.php:67
msgctxt "ui"
msgid "Logout"
msgstr ""

#: app/views/account/logged_out_template.php:6
#: app/views/account/logged_out_widget.php:6
#: app/views/readylaunch/login/form.php:83
#: app/views/readylaunch/shared/unauthorized.php:4
#: app/views/readylaunch/shared/unauthorized_message.php:55
#: app/views/shared/unauthorized.php:6
#: app/views/shared/unauthorized_message.php:17
#: app/views/shared/unauthorized_message_modern_paywall.php:19
msgctxt "ui"
msgid "Login"
msgstr ""

#: app/views/account/nav.php:8
msgctxt "ui"
msgid "Home"
msgstr ""

#: app/views/account/nav.php:25
msgctxt "ui"
msgid "You have a problem with one or more of your %1$s. To prevent any lapses in your %1$s please visit your %2$s%3$s%4$s page to update them."
msgstr ""

#: app/views/account/password.php:14
#: app/views/readylaunch/account/password.php:17
msgctxt "ui"
msgid "New Password"
msgstr ""

#: app/views/account/password.php:17
#: app/views/account/password.php:26
#: app/views/admin/members/new_member.php:52
#: app/views/checkout/form.php:109
#: app/views/checkout/form.php:121
#: app/views/checkout/spc_form.php:110
#: app/views/checkout/spc_form.php:122
#: app/views/login/form.php:40
#: app/views/login/reset_password.php:14
#: app/views/login/reset_password.php:25
#: app/views/readylaunch/account/password.php:20
#: app/views/readylaunch/account/password.php:29
#: app/views/readylaunch/checkout/form.php:132
#: app/views/readylaunch/checkout/form.php:149
#: app/views/readylaunch/login/form.php:100
#: app/views/readylaunch/login/reset_password.php:18
#: app/views/readylaunch/login/reset_password.php:29
msgid "Show password"
msgstr ""

#: app/views/account/password.php:23
#: app/views/readylaunch/account/password.php:26
msgctxt "ui"
msgid "Confirm New Password"
msgstr ""

#: app/views/account/password.php:35
#: app/views/login/reset_password.php:35
#: app/views/readylaunch/account/password.php:38
#: app/views/readylaunch/login/reset_password.php:39
msgctxt "ui"
msgid "Update Password"
msgstr ""

#: app/views/account/password.php:36
#: app/views/readylaunch/account/password.php:39
msgctxt "ui"
msgid "or"
msgstr ""

#: app/views/account/password.php:37
#: app/views/readylaunch/account/password.php:40
msgctxt "ui"
msgid "Cancel"
msgstr ""

#: app/views/account/payments.php:12
#: app/views/account/payments.php:30
#: app/views/readylaunch/account/payments.php:17
#: app/views/readylaunch/account/payments.php:48
msgctxt "ui"
msgid "Date"
msgstr ""

#: app/views/account/payments.php:13
#: app/views/account/payments.php:31
#: app/views/checkout/invoice.php:74
#: app/views/checkout/invoice_order_bumps.php:66
#: app/views/readylaunch/account/payments.php:21
#: app/views/readylaunch/account/payments.php:61
#: app/views/readylaunch/checkout/invoice.php:87
#: app/views/readylaunch/checkout/invoice_order_bumps.php:89
msgctxt "ui"
msgid "Total"
msgstr ""

#: app/views/account/payments.php:14
#: app/views/account/payments.php:35
#: app/views/account/payments.php:37
#: app/views/account/subscriptions.php:15
#: app/views/account/subscriptions.php:59
#: app/views/readylaunch/account/subscriptions.php:19
#: app/views/readylaunch/account/subscriptions.php:122
msgctxt "ui"
msgid "Membership"
msgstr ""

#: app/views/account/payments.php:15
#: app/views/account/payments.php:40
msgctxt "ui"
msgid "Method"
msgstr ""

#: app/views/account/payments.php:16
#: app/views/account/payments.php:41
#: app/views/readylaunch/account/payments.php:19
#: app/views/readylaunch/account/payments.php:55
#: app/views/readylaunch/account/subscriptions.php:21
#: app/views/readylaunch/account/subscriptions.php:165
msgctxt "ui"
msgid "Status"
msgstr ""

#: app/views/account/payments.php:17
#: app/views/account/payments.php:42
#: app/views/readylaunch/account/payments.php:16
#: app/views/readylaunch/account/payments.php:44
msgctxt "ui"
msgid "Invoice"
msgstr ""

#: app/views/account/payments.php:50
#: app/views/account/subscriptions.php:219
msgctxt "ui"
msgid "Previous Page"
msgstr ""

#: app/views/account/payments.php:53
#: app/views/account/subscriptions.php:222
msgctxt "ui"
msgid "Next Page"
msgstr ""

#: app/views/account/payments.php:61
#: app/views/readylaunch/account/payments.php:97
msgctxt "ui"
msgid "You have no completed payments to display."
msgstr ""

#: app/views/account/subscriptions.php:16
msgctxt "ui"
msgid "Subscription"
msgstr ""

#: app/views/account/subscriptions.php:17
#: app/views/account/subscriptions.php:131
#: app/views/emails/user_downgraded_sub.php:15
#: app/views/emails/user_resumed_sub.php:15
#: app/views/emails/user_upgraded_sub.php:15
msgctxt "ui"
msgid "Active"
msgstr ""

#: app/views/account/subscriptions.php:18
#: app/views/account/subscriptions.php:132
msgctxt "ui"
msgid "Created"
msgstr ""

#: app/views/account/subscriptions.php:19
msgctxt "ui"
msgid "Card Exp."
msgstr ""

#: app/views/account/subscriptions.php:71
#: app/views/readylaunch/account/subscriptions.php:20
#: app/views/readylaunch/account/subscriptions.php:136
msgctxt "ui"
msgid "Terms"
msgstr ""

#: app/views/account/subscriptions.php:77
#: app/views/readylaunch/account/subscriptions.php:193
msgctxt "ui"
msgid "Sub Account"
msgstr ""

#: app/views/account/subscriptions.php:83
#: app/views/emails/admin_new_sub.php:17
#: app/views/emails/admin_resumed_sub.php:17
msgctxt "ui"
msgid "Enabled"
msgstr ""

#: app/views/account/subscriptions.php:85
#: app/views/readylaunch/account/subscriptions.php:199
msgctxt "ui"
msgid "Lifetime"
msgstr ""

#: app/views/account/subscriptions.php:106
#: app/views/readylaunch/account/subscriptions.php:152
msgctxt "ui"
msgid "with coupon"
msgstr ""

#: app/views/account/subscriptions.php:122
#: app/views/readylaunch/account/subscriptions.php:176
msgctxt "ui"
msgid "Next Billing: %s"
msgstr ""

#: app/views/account/subscriptions.php:126
#: app/views/readylaunch/account/subscriptions.php:182
msgctxt "ui"
msgid "Expires: %s"
msgstr ""

#: app/views/account/subscriptions.php:139
msgctxt "ui"
msgid "Card Expires"
msgstr ""

#: app/views/account/subscriptions.php:148
msgctxt "ui"
msgid "%1$02d-%2$d"
msgstr ""

#: app/views/account/subscriptions.php:154
#: app/views/readylaunch/account/subscriptions.php:23
#: app/views/readylaunch/account/subscriptions.php:204
msgctxt "ui"
msgid "Actions"
msgstr ""

#: app/views/account/subscriptions.php:174
#: app/views/readylaunch/account/subscriptions.php:84
#: app/views/shortcodes/list_users_subscriptions.php:21
msgctxt "ui"
msgid "Renew"
msgstr ""

#: app/views/account/subscriptions.php:229
#: app/views/readylaunch/account/subscriptions.php:233
msgctxt "ui"
msgid "You have no active subscriptions to display."
msgstr ""

#: app/views/admin/about/1-2-4.php:5
msgid "Welcome to MemberPress vs 1.2.4"
msgstr ""

#: app/views/admin/about/1-2-4.php:6
msgid "This release includes the following awesome features and fixes:"
msgstr ""

#: app/views/admin/about/1-2-4.php:8
msgid "Autoresponder integrations have been moved into Add-ons ..."
msgstr ""

#: app/views/admin/about/1-2-4.php:9
msgid "We've added 4 additional auto-responder integration Add-ons"
msgstr ""

#: app/views/admin/about/1-2-4.php:10
msgid "We've launched our new MemberPress Developer Tools Add-on"
msgstr ""

#: app/views/admin/about/1-2-4.php:11
msgid "We've added an add-on auto-installation page to MemberPress"
msgstr ""

#: app/views/admin/about/1-2-4.php:12
msgid "We've added a Spanish translation"
msgstr ""

#: app/views/admin/about/1-2-4.php:13
msgid "And much more ..."
msgstr ""

#: app/views/admin/about/1-2-4.php:16
msgid "For a full list of features you can visit our %1$schangelog%2$s or %3$sread the blog post about 1.2.4%2$s."
msgstr ""

#: app/views/admin/account-login/ui.php:6
#: app/views/admin/options/account_login.php:5
msgid "MemberPress.com Account Login"
msgstr ""

#: app/views/admin/account-login/ui.php:11
msgid "Connected to MemberPress.com"
msgstr ""

#: app/views/admin/account-login/ui.php:15
msgid "Account Email"
msgstr ""

#: app/views/admin/account-login/ui.php:19
msgid "Site ID"
msgstr ""

#: app/views/admin/account-login/ui.php:34
msgid "Are you sure? This action will disconnect any of your Stripe payment methods, block webhooks from being processed, and prevent you from charging Credit Cards with and being notified of automatic rebills from Stripe."
msgstr ""

#: app/views/admin/account-login/ui.php:34
msgid "Disconnect from MemberPress.com"
msgstr ""

#: app/views/admin/account-login/ui.php:42
msgid "Connect your site to MemberPress.com to enable MemberPress Cloud Services!"
msgstr ""

#: app/views/admin/account-login/ui.php:45
msgid "Connect to MemberPress.com"
msgstr ""

#: app/views/admin/addons/affiliates.php:16
msgid "The Best Affiliate Program Plugin for WordPress"
msgstr ""

#: app/views/admin/addons/affiliates.php:20
msgid "Easy Affiliate helps you create a completely self-hosted affiliate program for your MemberPress site or ecommerce store within minutes. Start growing your sales with the power of referral marketing."
msgstr ""

#: app/views/admin/addons/affiliates.php:31
msgid "Integrates with WordPress ecommerce and email marketing solutions"
msgstr ""

#: app/views/admin/addons/affiliates.php:32
msgid "Pre-styled, theme-neutral Pro Dashboard"
msgstr ""

#: app/views/admin/addons/affiliates.php:33
msgid "Tracks commissions without using third-party cookies"
msgstr ""

#: app/views/admin/addons/affiliates.php:34
msgid "Real-Time Reports and 1-click affiliate payouts"
msgstr ""

#: app/views/admin/addons/affiliates.php:35
msgid "Detects affiliate fraud before you pay out"
msgstr ""

#: app/views/admin/addons/affiliates.php:36
msgid "Minus the fees and restrictions of other affiliate program solutions"
msgstr ""

#: app/views/admin/addons/affiliates.php:46
#: app/views/admin/addons/affiliates.php:51
msgid "Get Easy Affiliate"
msgstr ""

#: app/views/admin/addons/affiliates.php:48
msgid "Go to EasyAffiliate.com to get started on your MemberPress affiliate program."
msgstr ""

#: app/views/admin/addons/affiliates.php:61
msgid "Install and Activate Easy Affiliate"
msgstr ""

#: app/views/admin/addons/affiliates.php:63
msgid "Install Easy Affiliate from EasyAffiliate.com"
msgstr ""

#: app/views/admin/addons/affiliates.php:67
msgid "Installed & Active"
msgstr ""

#: app/views/admin/addons/affiliates.php:71
#: app/views/admin/addons/affiliates.php:73
msgid "Install & Activate"
msgstr ""

#: app/views/admin/addons/affiliates.php:84
msgid "Setup Easy Affiliate"
msgstr ""

#: app/views/admin/addons/affiliates.php:86
msgid "Easy Affiliate has an intuitive setup wizard to guide you through the setup process."
msgstr ""

#: app/views/admin/addons/ui.php:7
msgid "MemberPress Add-ons"
msgstr ""

#: app/views/admin/addons/ui.php:7
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:14
msgid "Refresh Add-ons"
msgstr ""

#: app/views/admin/addons/ui.php:7
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:18
msgid "Search add-ons"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/views/admin/addons/ui.php:13
msgid "Improve your memberships with our premium add-ons. Missing an add-on that you think you should be able to see? Click the %1$sRefresh Add-ons%2$s button above."
msgstr ""

#: app/views/admin/addons/ui.php:20
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:10
msgid "Available Add-ons"
msgstr ""

#: app/views/admin/addons/ui.php:41
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:43
#: vendor-prefixed/caseproof/growth-tools/src/App.php:100
msgid "Not Installed"
msgstr ""

#. Translators: %s: add-on status label.
#: app/views/admin/addons/ui.php:66
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:75
msgid "Status: %s"
msgstr ""

#: app/views/admin/addons/ui.php:90
#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:115
msgid "Install Add-on"
msgstr ""

#: app/views/admin/addons/ui.php:93
#: app/views/admin/products/order_bumps.php:31
msgid "Upgrade Now"
msgstr ""

#: app/views/admin/addons/ui.php:109
msgid "There were no Add-ons found for your license or lack thereof..."
msgstr ""

#: app/views/admin/auto-updates/option.php:2
msgid "Automatic Updates"
msgstr ""

#: app/views/admin/auto-updates/option.php:7
#: app/views/admin/auto-updates/option.php:10
msgid "Enable automatic, background updates"
msgstr ""

#: app/views/admin/auto-updates/option.php:11
msgid "Enabling background updates will automatically update MemberPress to the latest version, or the latest minors version."
msgstr ""

#: app/views/admin/auto-updates/option.php:17
msgid "<strong>All Updates (recommended)</strong> - Get the latest features, bug fixes, and security updates as they are released."
msgstr ""

#: app/views/admin/auto-updates/option.php:21
msgid "<strong>Minor Updates Only</strong> - Get bug fixes and security updates, but not major features."
msgstr ""

#: app/views/admin/auto-updates/option.php:25
msgid "<strong>None</strong> - Manually update everything."
msgstr ""

#: app/views/admin/coachkit/ui.php:16
msgid "Next-Level Coaching Tools"
msgstr ""

#: app/views/admin/coachkit/ui.php:20
msgid "Customize your coaching to thrive or expand with the industry's only membership-coaching platform!"
msgstr ""

#: app/views/admin/coachkit/ui.php:26
#: app/views/admin/coachkit/ui.php:61
msgid "MemberPress CoachKit™"
msgstr ""

#: app/views/admin/coachkit/ui.php:32
msgid "Sell unlimited coaching programs"
msgstr ""

#: app/views/admin/coachkit/ui.php:33
msgid "Track success with habit log"
msgstr ""

#: app/views/admin/coachkit/ui.php:34
msgid "Celebrate mini-wins with milestones"
msgstr ""

#: app/views/admin/coachkit/ui.php:35
msgid "Keep clients on track with due dates"
msgstr ""

#: app/views/admin/coachkit/ui.php:36
msgid "1:1 in-platform private messaging"
msgstr ""

#: app/views/admin/coachkit/ui.php:37
msgid "Match coaches to specific cohorts"
msgstr ""

#: app/views/admin/coachkit/ui.php:57
msgid "Enable MemberPress CoachKit™"
msgstr ""

#: app/views/admin/coachkit/ui.php:59
msgid "Install and Activate MemberPress CoachKit™"
msgstr ""

#: app/views/admin/coachkit/ui.php:67
msgid "Activate MemberPress CoachKit™ Add-On"
msgstr ""

#: app/views/admin/coachkit/ui.php:70
msgid "Install & Activate MemberPress CoachKit™ Add-On"
msgstr ""

#: app/views/admin/coachkit/ui.php:75
msgid "Purchase"
msgstr ""

#: app/views/admin/coupons/form.php:18
msgid "Discount:"
msgstr ""

#: app/views/admin/coupons/form.php:22
msgid "Coupon Discount"
msgstr ""

#: app/views/admin/coupons/form.php:23
msgid "<b>Recurring Memberships</b>: This discount will not apply to paid trials but will apply to all recurring transactions associated with the subscription. That means that 100% discount will give the member lifetime access for free.<br/><br/><b>Lifetime Memberships</b>: This discount will apply directly to the lifetime membership's one-time payment."
msgstr ""

#: app/views/admin/coupons/form.php:37
msgid "Discount Mode:"
msgstr ""

#: app/views/admin/coupons/form.php:41
msgid "Discount Mode"
msgstr ""

#: app/views/admin/coupons/form.php:42
msgid "<b>Standard:</b> This simply applies the discount to the amount of the charge or subscription.<br/><br/><b>First Payment:</b> This will allow you to set a different discount on the first transaction than the rebill transactions in a recurring subscription. If this value is set for a non-recurring payment then the First Payment discount will take precedence over the coupon's main discount.<br/><br/><b>Trial Override:</b> This will create a custom trial period based on the number of days & trial cost here. This option only works on recurring payments and will prevent any trials associated with the membership from working. The discount set above will still apply to the subscription’s recurring amount."
msgstr ""

#: app/views/admin/coupons/form.php:48
msgid "Standard"
msgstr ""

#: app/views/admin/coupons/form.php:49
msgid "First Payment"
msgstr ""

#: app/views/admin/coupons/form.php:50
msgid "Trial Period Override"
msgstr ""

#: app/views/admin/coupons/form.php:62
msgid "# of Days:"
msgstr ""

#: app/views/admin/coupons/form.php:66
msgid "Trial Days Price Text"
msgstr ""

#: app/views/admin/coupons/form.php:67
msgid "Values here that are multiples of 365 will show as years, multiples of 30 will show as months, multiples of 7 will show as weeks ... otherwise the trial will show up as days."
msgstr ""

#: app/views/admin/coupons/form.php:77
msgid "Trial Cost:"
msgstr ""

#: app/views/admin/coupons/form.php:92
msgid "First Payment Discount:"
msgstr ""

#: app/views/admin/coupons/form.php:96
msgid "First Payment Discount"
msgstr ""

#: app/views/admin/coupons/form.php:97
msgid "This is the discount that will be applied to the first payment. All additional payments will happen at the standard discount above."
msgstr ""

#: app/views/admin/coupons/form.php:116
msgid "Usage Count:"
msgstr ""

#: app/views/admin/coupons/form.php:120
msgid "Number of Coupon Uses"
msgstr ""

#: app/views/admin/coupons/form.php:121
msgid "This determines the number of times this coupon can be used.<br/><br/>Set to \"0\" to remove the limit."
msgstr ""

#: app/views/admin/coupons/form.php:132
msgid "Usage limit per user:"
msgstr ""

#: app/views/admin/coupons/form.php:136
msgid "Usage limit per user"
msgstr ""

#: app/views/admin/coupons/form.php:137
msgid "How many times this coupon can be used by an individual user. Set \"0\" to remove the limit."
msgstr ""

#: app/views/admin/coupons/form.php:149
msgid "Usage limit per user in Timeframe:"
msgstr ""

#: app/views/admin/coupons/form.php:153
msgid "Usage limit per user in Timeframe"
msgstr ""

#: app/views/admin/coupons/form.php:154
msgid "This determines the number of times this coupon can be used by each user in selected timeframe."
msgstr ""

#: app/views/admin/coupons/form.php:174
msgid "Allow on Upgrades and Downgrades:"
msgstr ""

#: app/views/admin/coupons/form.php:182
msgid "Schedule Coupon Start:"
msgstr ""

#: app/views/admin/coupons/form.php:192
msgid "Coupon Start Date:"
msgstr ""

#: app/views/admin/coupons/form.php:204
#: app/views/admin/coupons/form.php:241
msgid "Timezone"
msgstr ""

#: app/views/admin/coupons/form.php:209
msgid "Coupon Starts at <strong>00:00:01 AM on selected date</strong>."
msgstr ""

#: app/views/admin/coupons/form.php:219
msgid "Expire Coupon:"
msgstr ""

#: app/views/admin/coupons/form.php:229
msgid "Coupon Expiration:"
msgstr ""

#: app/views/admin/coupons/form.php:246
msgid "Coupon Expires at <strong>11:59:59 PM on selected date</strong>."
msgstr ""

#: app/views/admin/coupons/form.php:261
msgid "Apply coupon to the following Memberships:"
msgstr ""

#: app/views/admin/coupons/form.php:271
#: app/views/admin/coupons/form.php:280
msgid "Save Coupon"
msgstr ""

#: app/views/admin/coupons/form.php:272
#: app/views/admin/coupons/form.php:281
msgid "Coupon Saved"
msgstr ""

#: app/views/admin/coupons/form.php:278
msgid "You cannot create coupons until you have added at least 1 Membership."
msgstr ""

#: app/views/admin/courses/ui.php:14
msgid "Build & Sell Courses Quickly & Easily with MemberPress"
msgstr ""

#: app/views/admin/courses/ui.php:18
msgid "Get all the ease of use you expect from MemberPress combined with powerful LMS features designed to make building online courses super simple. This add-on boils it down to a basic, click-and-go process."
msgstr ""

#: app/views/admin/courses/ui.php:24
msgid "MemberPress Courses curriculum builder"
msgstr ""

#: app/views/admin/courses/ui.php:29
msgid "Powerful LMS features"
msgstr ""

#: app/views/admin/courses/ui.php:30
msgid "Included with every MemberPress license"
msgstr ""

#: app/views/admin/courses/ui.php:31
msgid "Create beautiful courses out of the box w/ Classroom Mode"
msgstr ""

#: app/views/admin/courses/ui.php:32
msgid "Fully visual drag-and-drop curriculum builder"
msgstr ""

#: app/views/admin/courses/ui.php:33
msgid "Protect content with MemberPress access rules"
msgstr ""

#: app/views/admin/courses/ui.php:34
msgid "Track learners' progress"
msgstr ""

#: app/views/admin/courses/ui.php:43
msgid "Enable Courses"
msgstr ""

#: app/views/admin/courses/ui.php:45
msgid "Install and Activate MemberPress Courses"
msgstr ""

#: app/views/admin/courses/ui.php:50
msgid "Activate Courses Add-On"
msgstr ""

#: app/views/admin/courses/ui.php:52
msgid "Install & Activate MemberPress Courses Add-On"
msgstr ""

#: app/views/admin/db/upgrade_error.php:10
#: app/views/admin/db/upgrade_error.php:29
msgid "MemberPress database upgrade error"
msgstr ""

#: app/views/admin/db/upgrade_error.php:35
msgid "Oops, your MemberPress database upgrade triggered an error..."
msgstr ""

#: app/views/admin/db/upgrade_error.php:36
msgid "If this is a production website rollback MemberPress to a previous version and contact our support team."
msgstr ""

#: app/views/admin/db/upgrade_error.php:44
msgid "Contact Support"
msgstr ""

#: app/views/admin/db/upgrade_needed.php:11
#: app/views/admin/db/upgrade_needed.php:171
msgid "MemberPress needs to upgrade your database"
msgstr ""

#: app/views/admin/db/upgrade_needed.php:68
msgid "Upgrading..."
msgstr ""

#: app/views/admin/db/upgrade_needed.php:174
msgid "Before starting the upgrade process <strong>make sure your <em>database is backed up</em></strong>."
msgstr ""

#: app/views/admin/db/upgrade_needed.php:175
msgid "And please be patient, the upgrade process <em>may take a few minutes</em>."
msgstr ""

#: app/views/admin/db/upgrade_needed.php:186
#: app/views/admin/db/upgrade_needed.php:189
msgid "Upgrade"
msgstr ""

#: app/views/admin/db/upgrade_needed.php:190
msgid "Are you sure? This will cancel the upgrade and roll MemberPress back to the previous version."
msgstr ""

#: app/views/admin/db/upgrade_needed.php:201
msgid "Your database is being upgraded"
msgstr ""

#: app/views/admin/db/upgrade_needed.php:202
msgid "Please be patient this could take a few minutes."
msgstr ""

#: app/views/admin/db/upgrade_needed.php:209
msgid "Are you sure? This will abort the upgrade and roll MemberPress back to the previous version."
msgstr ""

#: app/views/admin/db/upgrade_not_needed.php:10
#: app/views/admin/db/upgrade_not_needed.php:29
msgid "MemberPress database upgrade not needed"
msgstr ""

#: app/views/admin/db/upgrade_not_needed.php:32
msgid "Your MemberPress database is already up to date"
msgstr ""

#: app/views/admin/db/upgrade_not_needed.php:34
#: app/views/admin/db/upgrade_success.php:34
msgid "Back to Admin"
msgstr ""

#: app/views/admin/db/upgrade_success.php:10
msgid "MemberPress database upgrade was successful"
msgstr ""

#: app/views/admin/db/upgrade_success.php:29
msgid "MemberPress has successfully upgraded your database"
msgstr ""

#: app/views/admin/db/upgrade_success.php:32
msgid "You just successfully upgraded your MemberPress database ... now time to get back at it."
msgstr ""

#: app/views/admin/drm/modal.php:14
#: app/views/admin/drm/nhw/modal.php:12
msgid "<span>ALERT!</span> MemberPress Backend is Deactivated"
msgstr ""

#: app/views/admin/drm/modal.php:16
msgid "Your MemberPress license key is not found or is invalid. Without an active license key, your frontend is unaffected. However, you can no longer:"
msgstr ""

#: app/views/admin/drm/modal.php:18
#: app/views/admin/drm/nhw/modal.php:16
msgid "Issue customer refunds"
msgstr ""

#: app/views/admin/drm/modal.php:19
#: app/views/admin/drm/nhw/modal.php:17
msgid "Add new members"
msgstr ""

#: app/views/admin/drm/modal.php:20
#: app/views/admin/drm/nhw/modal.php:18
msgid "Manage memberships"
msgstr ""

#: app/views/admin/drm/modal.php:22
#: app/views/admin/drm/nhw/modal.php:20
msgid "This problem is easy to fix!"
msgstr ""

#: app/views/admin/drm/modal.php:25
msgid "Reactivate Backend Instantly*"
msgstr ""

#: app/views/admin/drm/modal.php:26
msgid "Buy or renew your license"
msgstr ""

#: app/views/admin/drm/modal.php:28
msgid "Click Here to purchase or renew your license key"
msgstr ""

#: app/views/admin/drm/modal.php:32
#: app/views/admin/drm/modal_fee.php:18
msgid "If you already have a license key, you can find it on your <a href=\""
msgstr ""

#: app/views/admin/drm/modal.php:34
#: app/views/admin/drm/modal_fee.php:20
#: app/views/admin/drm/nhw/modal.php:25
msgid "License Key"
msgstr ""

#: app/views/admin/drm/modal.php:41
msgid "* When re-activating without an active license, MP will add an additional %s fee to each transaction."
msgstr ""

#: app/views/admin/drm/modal.php:106
#: app/views/admin/drm/modal_fee.php:56
#: app/views/admin/drm/nhw/modal.php:61
msgid "ERROR! License Key not valid. Try Again."
msgstr ""

#: app/views/admin/drm/modal_fee.php:10
msgid "<span>ALERT!</span> MemberPress is running without a license"
msgstr ""

#: app/views/admin/drm/modal_fee.php:12
msgid "When using without a license, MemberPress will add an additional %s fee to each transaction."
msgstr ""

#: app/views/admin/drm/modal_fee.php:15
#: app/views/admin/drm/notices/locked_warning.php:9
msgid "Click here to purchase or renew your license key"
msgstr ""

#: app/views/admin/drm/nhw/modal.php:14
msgid "The license that came with your No Hassle Membership & Course Platform expired at the end of 2023. Without an active license key, your frontend will remain unaffected and your members will still have access. However, you will no longer be able to access the MemberPress backend, update courses, manage memberships, add new members etc. To prevent this from happening, please purchase a new license. Without a license you cannot: "
msgstr ""

#: app/views/admin/drm/nhw/modal.php:22
msgid "Click Here to purchase a new license key"
msgstr ""

#: app/views/admin/drm/nhw/notices/generic_notice.php:6
msgid "ALERT! Your MemberPress License will expire on Dec. 31, 2023"
msgstr ""

#: app/views/admin/drm/nhw/notices/generic_notice.php:8
msgid "The license that came with your No Hassle Membership & Course Platform will expire at the end of 2023. Without an active license key, your frontend will remain unaffected and your members will still have access. However, you will no longer be able to access MemberPress' backend, update courses, manage memberships, add new members etc. To prevent this from happening, please purchase a new license."
msgstr ""

#: app/views/admin/drm/nhw/notices/generic_notice.php:11
msgid "please purchase a new license at a very special price through this link"
msgstr ""

#: app/views/admin/drm/nhw/notices/locked_warning.php:6
msgid "The license that came with your No Hassle Membership & Course Platform expired at the end of 2023. Without an active license key, your frontend will remain unaffected and your members will still have access. However, you will no longer be able to access the MemberPress backend, update courses, manage memberships, add new members etc. To prevent this from happening, please purchase a new license. Without a license you cannot: Issue customer refunds, Add new members, Manage memberships."
msgstr ""

#: app/views/admin/drm/nhw/notices/locked_warning.php:9
msgid "Click here to purchase a new license key"
msgstr ""

#: app/views/admin/drm/nhw/notices/medium_warning.php:10
msgid "<a target=\"_blank\" href=\"%s\">Go to No Hassle Websites and make your selection</a>."
msgstr ""

#: app/views/admin/drm/nhw/notices/medium_warning.php:11
#: app/views/admin/drm/notices/low_warning.php:10
#: app/views/admin/drm/notices/medium_warning.php:11
msgid "<a href=\"%s\">Click here</a> to enter and activate your new license key."
msgstr ""

#: app/views/admin/drm/nhw/notices/medium_warning.php:16
msgid "Grab your key from <a target=\"_blank\" href=\"%s\">No Hassle Websites</a>."
msgstr ""

#: app/views/admin/drm/nhw/notices/medium_warning.php:17
#: app/views/admin/drm/notices/low_warning.php:16
#: app/views/admin/drm/notices/medium_warning.php:17
msgid "<a href=\"%s\">Click here</a> to enter and activate it."
msgstr ""

#: app/views/admin/drm/notices/fee_notice.php:6
msgid "MemberPress is running without a license"
msgstr ""

#: app/views/admin/drm/notices/fee_notice.php:7
msgid "When using without a license, MemberPress will add an additional fee to each transaction."
msgstr ""

#: app/views/admin/drm/notices/fee_notice.php:10
msgid "Learn More"
msgstr ""

#: app/views/admin/drm/notices/locked_warning.php:6
msgid "Your MemberPress license key is not found or is invalid. Without an active license key, your frontend is unaffected. However, you can no longer: Issue customer refunds, Add new members, Manage memberships."
msgstr ""

#: app/views/admin/drm/notices/low_warning.php:9
#: app/views/admin/drm/notices/medium_warning.php:10
msgid "Go to MemberPress.com and make your selection. <a target=\"_blank\" href=\"%s\">Pricing Page</a>."
msgstr ""

#: app/views/admin/drm/notices/low_warning.php:11
msgid "That’s it!."
msgstr ""

#: app/views/admin/drm/notices/low_warning.php:15
#: app/views/admin/drm/notices/medium_warning.php:16
msgid "Grab your key from your <a target=\"_blank\" href=\"%s\">Account Page</a>."
msgstr ""

#: app/views/admin/emails/options.php:10
msgid "Send %s"
msgstr ""

#: app/views/admin/emails/options.php:21
msgid "Hide Editor"
msgstr ""

#: app/views/admin/emails/options.php:28
msgid "Send Test"
msgstr ""

#: app/views/admin/emails/options.php:35
msgid "Reset to Default"
msgstr ""

#: app/views/admin/emails/options.php:41
msgid "Subject"
msgstr ""

#: app/views/admin/emails/options.php:45
msgid "Body"
msgstr ""

#: app/views/admin/emails/options.php:60
msgid "Insert &uarr;"
msgstr ""

#: app/views/admin/emails/options.php:68
msgid "Use default template"
msgstr ""

#: app/views/admin/emails/options.php:71
msgid "Default Email Template"
msgstr ""

#: app/views/admin/emails/options.php:72
msgid "When this is checked the body of this email will be wrapped in the default email template."
msgstr ""

#: app/views/admin/errors.php:9
msgid "ERROR:"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:26
msgid "Connected to PayPal Commerce Platform - Sandbox mode"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:29
#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:54
#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:79
msgid "PayPal Merchant ID: %s"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:35
#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:60
msgid "You need to confirm your email to accept payments"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:44
#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:70
msgid "My email is verified"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:51
msgid "Connected to PayPal Commerce Platform - Live mode"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:76
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:41
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:109
msgid "Connect with the world's most powerful and easy to use Payment Gateway"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:88
msgid "Pay Securely"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:92
msgid "Pay with PayPal"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:96
msgid "Pay with PayPal Credit"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:100
msgid "Global reach"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:108
msgid "Automatic configuration"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:112
msgid "Recurring subscription billing"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:116
msgid "Non-recurring payments"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:124
msgid "If you refresh the page and see this message for more than 5 minutes, disconnect and try again."
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:134
msgid "Disconnect and Retry"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:138
msgid "IPN URL: "
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:193
msgid "Connect MemberPress"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:197
#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:236
msgid "Are you sure you want to disconnect? Your future renewing payments will not track properly..."
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:203
#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:243
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:35
msgid "Disconnect"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:205
msgid "Going live will stop your Sandbox connection. Any subscriptions on your site connected to Sandbox will no longer track their renewals. Are you sure you're ready to Go Live?"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:227
msgid "Connect Live"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:260
msgid "Connect Sandbox"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:268
msgid "Enable Smart Payment Buttons"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:274
msgid "Are you sure?"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:285
msgid "Rollback to PayPal standard"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:296
msgid "Save settings & Connect"
msgstr ""

#: app/views/admin/gateways/paypal/connect-migrate-prompt.php:297
msgid "Processing"
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:15
msgid "Test Mode"
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:22
#: app/views/admin/gateways/stripe/checkboxes.php:30
#: app/views/admin/products/registration.php:92
#: app/views/admin/products/registration.php:96
msgid "Customize Payment Methods"
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:32
msgid "The configured currency has changed, please save the options to change the payment methods."
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/views/admin/gateways/stripe/checkboxes.php:39
msgid "Some of these payment methods have limitations. %1$sClick here%2$s to learn more."
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:58
msgid "Apple Pay"
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:59
msgid "Google Pay"
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:76
#: app/views/admin/readylaunch/account.php:79
#: app/views/admin/readylaunch/checkout.php:27
#: app/views/admin/readylaunch/coaching.php:79
#: app/views/admin/readylaunch/login.php:84
#: app/views/admin/readylaunch/pricing.php:69
#: app/views/admin/readylaunch/thankyou.php:118
msgctxt "ui"
msgid "Update"
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:93
msgid "Send Debug Emails"
msgstr ""

#: app/views/admin/gateways/stripe/checkboxes.php:97
msgid "Stripe Webhook URL:"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:21
msgid "Disconnecting from this Stripe Account will block webhooks from being processed, and prevent MemberPress subscriptions associated with it from working."
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:25
msgid "Connected to Stripe"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:27
msgid "Connected to: %1$s %2$s %3$s"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:32
msgid "Refresh Stripe Credentials"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:46
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:87
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:114
#: app/views/admin/onboarding/payments.php:25
msgid "Accept all Major Credit Cards"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:47
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:88
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:115
#: app/views/admin/onboarding/payments.php:26
msgid "Flexible subscriptions and billing terms"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:48
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:89
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:116
#: app/views/admin/gateways/stripe/keys.php:22
#: app/views/admin/gateways/stripe/keys.php:33
msgid "25+ ways to pay"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:53
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:94
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:121
#: app/views/admin/gateways/stripe/keys.php:20
#: app/views/admin/gateways/stripe/keys.php:31
msgid "Accept Apple Pay"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:54
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:95
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:122
#: app/views/admin/gateways/stripe/keys.php:21
#: app/views/admin/gateways/stripe/keys.php:32
msgid "Accept Google Wallet"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:55
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:96
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:123
#: app/views/admin/onboarding/payments.php:28
msgid "Fraud prevention tools"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:62
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:72
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:102
#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:129
msgid "\"Connect with Stripe\" button"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:68
msgid "Re-Connect to Stripe"
msgstr ""

#: app/views/admin/gateways/stripe/connect-migrate-prompt.php:69
msgid "This Payment Method has been disconnected so it may stop working for new and recurring payments at any time. To prevent this, re-connect your Stripe account by clicking the \"Connect with Stripe\" button below."
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:17
msgid "Accept Credit Cards on site"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:18
#: app/views/admin/gateways/stripe/keys.php:34
msgid "Recurring billing"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:19
#: app/views/admin/gateways/stripe/keys.php:35
msgid "SCA ready"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:29
msgid "Offsite secure hosted solution"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:30
msgid "Accept Credit Cards"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:45
msgid "Test Publishable Key*:"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:49
msgid "Test Secret Key*:"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:57
msgid "Live Publishable Key*:"
msgstr ""

#: app/views/admin/gateways/stripe/keys.php:61
msgid "Live Secret Key*:"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag, %3$s: open link tag, %4$s: close link tag, %5$s: open link tag, %6$s: close link tag.
#: app/views/admin/get_started.php:10
msgid "Get started with MemberPress by %1$sadding a Payment Method%2$s, %3$sadding a Membership%4$s, then %5$sadding a Rule%6$s to protect your content."
msgstr ""

#: app/views/admin/groups/custom_page_template_form.php:7
#: app/views/admin/products/custom_page_template_form.php:7
msgid "Use Custom Page Template"
msgstr ""

#: app/views/admin/groups/form.php:10
msgid "Upgrade Path"
msgstr ""

#: app/views/admin/groups/form.php:14
msgid "This group is a membership upgrade path"
msgstr ""

#: app/views/admin/groups/form.php:15
msgid "Enabling this will mean that users can only be subscribed to one of these memberships at a time. If un-checked, users may be subscribed to each of these memberships simultaneously."
msgstr ""

#: app/views/admin/groups/form.php:32
msgid "Reset billing period"
msgstr ""

#: app/views/admin/groups/form.php:36
msgid "Reset billing period when upgrading"
msgstr ""

#: app/views/admin/groups/form.php:37
msgid "If this is checked, the billing period will be reset when a recurring membership is upgraded."
msgstr ""

#: app/views/admin/groups/form.php:49
msgid "Downgrade Path"
msgstr ""

#: app/views/admin/groups/form.php:53
msgid "Fallback group when membership expires or is cancelled"
msgstr ""

#: app/views/admin/groups/form.php:54
msgid "Select which membership in the group to fall back to when the paid subscription expires or is cancelled. If you are unsure, leave the default."
msgstr ""

#: app/views/admin/groups/form.php:58
msgid "Memberships:"
msgstr ""

#: app/views/admin/groups/form.php:63
msgid "Here you can add/remove memberships from this group pricing page.%1$s%1$sThe order of the memberships is important here. Order the memberships so that the lowest tier membership is at the top of the list and the highest tier membership is at the bottom, with the other memberships in order in between."
msgstr ""

#: app/views/admin/groups/form.php:68
msgid "ReadyLaunch is enabled for Groups in your MemberPress > Settings > ReadyLaunch tab. ReadyLaunch can currently only show 5 Membership plans per Group."
msgstr ""

#: app/views/admin/groups/form.php:74
msgid "Add Membership"
msgstr ""

#: app/views/admin/groups/form.php:86
#: app/views/admin/groups/form.php:90
msgid "Disable Change Plan Pop-Up"
msgstr ""

#: app/views/admin/groups/form.php:91
msgid "This will take the user to the Group pricing page when they click on Change Plan instead of showing them the quick selection pop-up."
msgstr ""

#: app/views/admin/groups/form.php:96
#: app/views/admin/groups/form.php:100
msgid "Disable Pricing Page"
msgstr ""

#: app/views/admin/groups/form.php:101
msgid "This will disable the pricing page from being accessed on the front end of your site. It will return a 404 (not found) page if a user attempts to access it.<br/><br/>You can optionally provide an alternate URL to take the member to if they try to visit this page."
msgstr ""

#: app/views/admin/groups/form.php:108
msgid "Pricing Page Theme:"
msgstr ""

#: app/views/admin/groups/form.php:113
msgid "Custom Button CSS classes (optional):"
msgstr ""

#: app/views/admin/groups/form.php:117
msgid "Custom Highlighted Button CSS classes (optional):"
msgstr ""

#: app/views/admin/groups/form.php:121
msgid "Custom Disabled Button CSS classes (optional):"
msgstr ""

#: app/views/admin/groups/form.php:127
msgid "Alternate Group URL:"
msgstr ""

#: app/views/admin/groups/form.php:132
msgid "Group Price Boxes Shortcodes"
msgstr ""

#: app/views/admin/groups/form.php:136
msgid "Manually place group price boxes"
msgstr ""

#: app/views/admin/groups/form.php:137
msgid "By default MemberPress will append the pricing boxes to the end of the Group page.<br/><br/>If you'd like to show them in a different place on the group page or some other page on this site just copy and paste a shortcode where you'd like the price boxes for this group to appear."
msgstr ""

#: app/views/admin/groups/form.php:141
msgid "Shortcode to be used on this group page."
msgstr ""

#: app/views/admin/groups/form.php:143
msgid "Shortcode which can be used on any other WordPress page, post or custom post type."
msgstr ""

#: app/views/admin/members/new_member.php:6
msgid "Manually Add a New Member"
msgstr ""

#: app/views/admin/members/new_member.php:48
msgid "Password"
msgstr ""

#: app/views/admin/members/new_member.php:74
msgid "Initial Membership"
msgstr ""

#: app/views/admin/members/new_member.php:75
msgid "Select the initial membership you'd like your new member to be part of."
msgstr ""

#: app/views/admin/members/new_member.php:85
msgid "Send User Notification"
msgstr ""

#: app/views/admin/members/new_member.php:89
msgid "Send the new member an email with their username"
msgstr ""

#: app/views/admin/members/new_member.php:94
msgid "Send Welcome Email"
msgstr ""

#: app/views/admin/members/new_member.php:98
msgid "Send the new member a membership welcome email"
msgstr ""

#: app/views/admin/members/new_member.php:103
#: app/views/admin/products/product_options_meta_box.php:9
msgid "Advanced"
msgstr ""

#: app/views/admin/members/new_member.php:106
msgid "When a new member is added manually, an initial MemberPress transaction is created to grant them access to your membership. You can modify the details of this initial transaction here."
msgstr ""

#: app/views/admin/members/new_member.php:111
msgid "Trans Num"
msgstr ""

#: app/views/admin/members/new_member.php:115
msgid "Transaction Number"
msgstr ""

#: app/views/admin/members/new_member.php:116
msgid "This is typically the id of the transaction that is generated by the gateway. Typically this value won't need to be changed unless you're referencing a transaction that happened outside of MemberPress for this user and membership."
msgstr ""

#: app/views/admin/members/new_member.php:126
#: app/views/admin/members/new_member.php:130
msgid "Amount"
msgstr ""

#: app/views/admin/members/new_member.php:131
msgid "This is the amount the member initially paid for the membership. Unless money has changed hands outside of MemberPress (and it's being recorded here) then we recommend this value stay at zero so your repots will remain accurate."
msgstr ""

#: app/views/admin/members/new_member.php:150
#: app/views/admin/members/new_member.php:154
msgid "Payment Method"
msgstr ""

#: app/views/admin/members/new_member.php:155
msgid "Unless this is recording an actual transaction made outside of MemberPress but with one of your gateways, we recommend that this remain set to 'manual'"
msgstr ""

#: app/views/admin/members/new_member.php:183
msgid "Expires At"
msgstr ""

#: app/views/admin/members/new_member.php:184
msgid "This refences the date and time that the user's membership granted by a transaction will expire and need to be renewed to continue access."
msgstr ""

#: app/views/admin/members/new_member.php:217
#: app/views/admin/subscriptions/new.php:18
#: app/views/admin/transactions/new_trans.php:18
msgid "Create"
msgstr ""

#: app/views/admin/members/row.php:52
#: app/views/admin/subscriptions/row.php:117
#: app/views/admin/transactions/row.php:47
msgid "View member's profile"
msgstr ""

#: app/views/admin/members/row.php:52
#: app/views/admin/subscriptions/row.php:119
#: app/views/admin/transactions/row.php:49
msgid "Deleted"
msgstr ""

#: app/views/admin/members/row.php:55
msgid "Edit member's profile"
msgstr ""

#: app/views/admin/members/row.php:57
msgid "Delete member"
msgstr ""

#: app/views/admin/members/row.php:57
#: app/views/admin/subscriptions/row.php:84
#: app/views/admin/transactions/row.php:116
msgid "Delete"
msgstr ""

#: app/views/admin/members/row.php:145
msgid "Trial"
msgstr ""

#: app/views/admin/members/search_box.php:2
#: app/views/admin/subscriptions/search_box.php:2
#: app/views/admin/transactions/search_box.php:2
msgid "Filter by"
msgstr ""

#: app/views/admin/members/search_box.php:12
msgid "All Members"
msgstr ""

#: app/views/admin/members/search_box.php:13
#: app/views/admin/reports/overall_info_blocks.php:7
msgid "Active Members"
msgstr ""

#: app/views/admin/members/search_box.php:14
#: app/views/admin/reports/overall_info_blocks.php:12
msgid "Inactive Members"
msgstr ""

#: app/views/admin/members/search_box.php:15
msgid "Expired Members"
msgstr ""

#: app/views/admin/members/search_box.php:16
msgid "Non-Members"
msgstr ""

#: app/views/admin/must_configure.php:5
msgid "<b>MemberPress hasn't been configured yet.</b> Go to the MemberPress %1$soptions page%2$s to get it setup."
msgstr ""

#: app/views/admin/onboarding/active_license.php:18
msgid "License activated until %s"
msgstr ""

#: app/views/admin/onboarding/active_license.php:23
msgid "License activated"
msgstr ""

#: app/views/admin/onboarding/active_license.php:30
msgid "Account email"
msgstr ""

#: app/views/admin/onboarding/active_license.php:38
msgid "Product"
msgstr ""

#: app/views/admin/onboarding/active_license.php:46
msgid "Activations"
msgstr ""

#. Translators: %1$s: open b tag, %2$d: activation count, %3$s: max activations, %4$s close b tag.
#: app/views/admin/onboarding/active_license.php:52
#: app/views/admin/options/active_license.php:46
msgid "%1$s%2$d of %3$s%4$s sites have been activated with this license key"
msgstr ""

#: app/views/admin/onboarding/active_license.php:63
msgid "Manage activations"
msgstr ""

#: app/views/admin/onboarding/active_license.php:66
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:189
msgid "Deactivate License"
msgstr ""

#: app/views/admin/onboarding/complete.php:4
msgid "Congrats! You’re done."
msgstr ""

#: app/views/admin/onboarding/complete.php:6
msgid "Check out what you set up..."
msgstr ""

#: app/views/admin/onboarding/complete.php:11
msgid "Your next step..."
msgstr ""

#: app/views/admin/onboarding/complete.php:16
#: app/views/admin/onboarding/complete.php:20
msgid "Getting Started with MemberPress Courses"
msgstr ""

#: app/views/admin/onboarding/complete.php:21
msgid "MemberPress Courses is included with your subscription. Learn how you can start selling what you know today!"
msgstr ""

#: app/views/admin/onboarding/complete.php:28
msgid "Make the most of your MemberPress site..."
msgstr ""

#: app/views/admin/onboarding/complete.php:33
#: app/views/admin/onboarding/complete.php:37
msgid "MemberPress Blog"
msgstr ""

#: app/views/admin/onboarding/complete.php:38
msgid "Sign up for tips, tricks, and industry updates from top membership, LMS, and online business experts and influencers."
msgstr ""

#: app/views/admin/onboarding/content-search-results.php:13
#: app/views/admin/onboarding/parts/content_popup.php:16
msgid "Specific Page"
msgstr ""

#: app/views/admin/onboarding/content-search-results.php:19
msgid "No content found."
msgstr ""

#: app/views/admin/onboarding/content.php:12
msgid "Migration complete! Migrated courses can be protected using the link below."
msgstr ""

#: app/views/admin/onboarding/content.php:16
msgid "Migrate from LearnDash?"
msgstr ""

#: app/views/admin/onboarding/content.php:17
msgid "We'd usually ask you to select some content here, but we noticed that you have some LearnDash Courses that could be migrated to MemberPress Courses automatically. Would you like to migrate your existing course content to MemberPress Courses?"
msgstr ""

#: app/views/admin/onboarding/content.php:24
#: app/views/admin/options/courses_migrator.php:14
msgid "Migrate user progress and quiz attempts"
msgstr ""

#: app/views/admin/onboarding/content.php:28
#: app/views/admin/options/courses_migrator.php:16
msgid "Start Migration"
msgstr ""

#: app/views/admin/onboarding/content.php:29
msgid "No Thanks, I'll Choose Other Content"
msgstr ""

#: app/views/admin/onboarding/content.php:30
msgid "Please stay on this page until the migration completes"
msgstr ""

#: app/views/admin/onboarding/content.php:36
msgid "Now let's get some content to protect"
msgstr ""

#: app/views/admin/onboarding/content.php:37
msgid "Here, you can create new content to protect. Or you can choose existing content on your site that you'd like to protect."
msgstr ""

#: app/views/admin/onboarding/content.php:39
msgid "Create New Content"
msgstr ""

#: app/views/admin/onboarding/content.php:40
#: app/views/admin/onboarding/content.php:64
msgid "Choose Existing Content"
msgstr ""

#: app/views/admin/onboarding/content.php:45
msgid "Your Content"
msgstr ""

#: app/views/admin/onboarding/content.php:66
msgid "Search..."
msgstr ""

#: app/views/admin/onboarding/content.php:72
#: app/views/admin/onboarding/membership.php:87
#: app/views/admin/onboarding/parts/content_popup.php:34
#: app/views/admin/onboarding/rules.php:51
#: app/views/admin/subscriptions/row.php:230
#: app/views/admin/transactions/row.php:170
msgid "Save"
msgstr ""

#: app/views/admin/onboarding/features.php:8
msgid "What features do you want to enable?"
msgstr ""

#: app/views/admin/onboarding/features.php:9
msgid "MemberPress is chock full of awesome features. Here are a few you can enable right off the bat."
msgstr ""

#: app/views/admin/onboarding/features.php:13
msgid "Advanced Content Protection"
msgstr ""

#: app/views/admin/onboarding/features.php:14
msgid "Increase perceived value, and protect your bottom line from unpaying eyes. Paywall your valuable content a thousand ways!"
msgstr ""

#: app/views/admin/onboarding/features.php:22
msgid "Customizable Checkout"
msgstr ""

#: app/views/admin/onboarding/features.php:23
msgid "Sell more memberships by accepting multiple payment types – from PayPal and credit cards to digital wallets, bank checks, and even cash by mail. You decide."
msgstr ""

#: app/views/admin/onboarding/features.php:32
msgid "Sell what you know. Create memorable online courses, including quizzes and progress tracking."
msgstr ""

#: app/views/admin/onboarding/features.php:49
msgid "Seamlessly integrates with your existing Members, Memberships and Courses to provide a dynamic platform to sell an Unlimited number of coaching programs."
msgstr ""

#: app/views/admin/onboarding/features.php:65
msgid "Add value to your memberships by giving users access to downloadable files like white papers, guides, checklists, and videos – the sky’s the limit."
msgstr ""

#: app/views/admin/onboarding/features.php:81
msgid "Keep users coming back for more with a VIP forum or chat room they can access based on membership level. Available with MemberPress Plus or Pro."
msgstr ""

#: app/views/admin/onboarding/features.php:97
msgid "Work smarter not harder. Connect MemberPress with thousands of your favorite productivity and functionality apps and services. Available with MemberPress Plus or Pro."
msgstr ""

#: app/views/admin/onboarding/features.php:113
msgid "Expand your audience and sell more memberships. With Gifting, you can market to people who know people who could use your membership. Available with MemberPress Pro."
msgstr ""

#: app/views/admin/onboarding/features.php:129
msgid "Allow your members to add sub-accounts to their memberships based on subscription level. Great for families and groups. Available with MemberPress Pro."
msgstr ""

#: app/views/admin/onboarding/features.php:145
msgid "Create your own non-salaried sales team, and make up to 30% more in membership sales with referral marketing."
msgstr ""

#: app/views/admin/onboarding/features.php:162
msgid "If your subscription level allows, the following plugins will be installed automatically: %s"
msgstr ""

#: app/views/admin/onboarding/license.php:8
msgid "Your License"
msgstr ""

#: app/views/admin/onboarding/license.php:14
msgid "First thing's first. Let's get your license activated."
msgstr ""

#: app/views/admin/onboarding/license.php:22
msgid "First thing's first. Let's get your license activated. Contact your developer agency if you need your license key."
msgstr ""

#: app/views/admin/onboarding/license.php:24
msgid "Enter license key"
msgstr ""

#: app/views/admin/onboarding/membership.php:5
msgid "Time to make your first membership"
msgstr ""

#: app/views/admin/onboarding/membership.php:6
msgid "Now that you've got some content to protect, you'll want to show people how they can access it. That's what a \"membership\" is."
msgstr ""

#: app/views/admin/onboarding/membership.php:7
msgid "MemberPress lets you create an unlimited number of memberships and name them any way you like (for example, Bronze, Silver, and Gold). Here, we'll set up your very FIRST membership."
msgstr ""

#: app/views/admin/onboarding/membership.php:15
msgid "Your membership"
msgstr ""

#: app/views/admin/onboarding/membership.php:23
#: app/views/admin/onboarding/membership.php:51
msgid "Billing"
msgstr ""

#: app/views/admin/onboarding/membership.php:45
#: app/views/admin/onboarding/rules.php:23
#: app/views/admin/onboarding/rules.php:46
msgid "Membership Name"
msgstr ""

#: app/views/admin/onboarding/membership.php:68
msgid "Recurring (Annually)"
msgstr ""

#: app/views/admin/onboarding/membership.php:81
msgid "0"
msgstr ""

#. Translators: %1$s: open underline tag, %2$s: close underline tag.
#: app/views/admin/onboarding/membership.php:92
msgid "More advanced options are available in %1$sMemberPress > Memberships%2$s"
msgstr ""

#: app/views/admin/onboarding/nav/complete.php:12
msgid "Finish"
msgstr ""

#: app/views/admin/onboarding/nav/content.php:12
#: app/views/admin/onboarding/nav/finish.php:5
#: app/views/admin/onboarding/nav/membership.php:11
#: app/views/admin/onboarding/nav/payments.php:17
#: app/views/admin/onboarding/nav/payments.php:23
#: app/views/admin/onboarding/nav/rules.php:11
msgid "Skip"
msgstr ""

#: app/views/admin/onboarding/nav/content.php:15
#: app/views/admin/onboarding/nav/features.php:5
#: app/views/admin/onboarding/nav/finish.php:8
#: app/views/admin/onboarding/nav/license.php:5
#: app/views/admin/onboarding/nav/membership.php:14
#: app/views/admin/onboarding/nav/payments.php:20
#: app/views/admin/onboarding/nav/rules.php:14
msgid "Continue"
msgstr ""

#: app/views/admin/onboarding/parts/content_popup.php:1
msgid "Create Content"
msgstr ""

#: app/views/admin/onboarding/parts/content_popup.php:10
msgid "Choose this if you'd like to create and protect a new online course."
msgstr ""

#: app/views/admin/onboarding/parts/content_popup.php:17
msgid "Choose this if you'd like to create and protect a single page of content."
msgstr ""

#: app/views/admin/onboarding/parts/content_popup.php:24
msgid "Write course name"
msgstr ""

#: app/views/admin/onboarding/parts/content_popup.php:30
msgid "Write page title"
msgstr ""

#. Translators: %1$s: open underline tag, %2$s: close underline tag.
#: app/views/admin/onboarding/parts/content_popup.php:39
msgid "More advanced options are available in %1$sMemberPress > Courses%2$s"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:32
msgid "Processing upgrade"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:34
msgid "Please wait while the upgrade is processed, this may take a minute."
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:48
msgid "To unlock selected features, upgrade with Easy Affiliate."
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:53
msgid "To unlock selected features, upgrade with CoachKit™."
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:58
msgid "To unlock selected features, upgrade with CoachKit™ and Easy Affiliate."
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:81
#: app/views/admin/onboarding/parts/finish.php:111
msgid "Purchase Easy Affiliate"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:92
#: app/views/admin/onboarding/parts/finish.php:117
msgid "Purchase CoachKit™"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:107
msgid "You have selected following features:"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:123
msgid "Finish setup"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:158
#: app/views/admin/onboarding/parts/finish.php:210
#: app/views/admin/onboarding/payments.php:39
msgid "Authorize.net"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:190
#: app/views/admin/onboarding/parts/finish.php:222
msgid "Finishing setup"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:191
msgid "Please don't close the browser."
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:212
msgid "Configure Gateway"
msgstr ""

#: app/views/admin/onboarding/parts/finish.php:224
msgid "Please wait.."
msgstr ""

#: app/views/admin/onboarding/payments.php:13
msgid "Get set up to accept payments"
msgstr ""

#: app/views/admin/onboarding/payments.php:14
msgid "You won't believe how easy it is to accept online payments with MemberPress."
msgstr ""

#: app/views/admin/onboarding/payments.php:21
msgid "The world's most powerful and easy to use payment gateway."
msgstr ""

#: app/views/admin/onboarding/payments.php:23
msgid "Stripe Offers"
msgstr ""

#: app/views/admin/onboarding/payments.php:27
msgid "Accept SEPA, Apple Pay, Google Wallet, iDeal"
msgstr ""

#: app/views/admin/onboarding/payments.php:30
msgid "Plus more"
msgstr ""

#: app/views/admin/onboarding/payments.php:31
msgid "Add Stripe"
msgstr ""

#: app/views/admin/onboarding/payments.php:41
msgid "API Login ID"
msgstr ""

#: app/views/admin/onboarding/payments.php:45
msgid "Transaction Key"
msgstr ""

#: app/views/admin/onboarding/payments.php:49
msgid "Signature Key"
msgstr ""

#: app/views/admin/onboarding/payments.php:53
msgid "Webhook URL"
msgstr ""

#: app/views/admin/onboarding/payments.php:57
msgid "Done"
msgstr ""

#: app/views/admin/onboarding/payments.php:61
msgid "Be sure to add your payment option"
msgstr ""

#: app/views/admin/onboarding/payments.php:62
msgid "If you skip this step, MemberPress will set up the offline payment option automatically. You can later visit MemberPress > Settings > Payments to get your gateway rolling."
msgstr ""

#: app/views/admin/onboarding/payments.php:64
msgid "Add Offline Payment"
msgstr ""

#: app/views/admin/onboarding/payments.php:69
msgid "Trying to connect with Authorize.net?"
msgstr ""

#: app/views/admin/onboarding/payments.php:70
msgid "You'll need a Pro license to enable this gateway. But it's easy! Just click the button below and follow the prompts."
msgstr ""

#: app/views/admin/onboarding/payments.php:73
msgid "Continue to Upgrade"
msgstr ""

#: app/views/admin/onboarding/payments.php:79
msgid "Do you need to collect taxes?"
msgstr ""

#: app/views/admin/onboarding/payments.php:85
msgid "Just check this box to enable automatic tax rate lookups and calculations with Stripe.*"
msgstr ""

#. Translators: %1$s: open link tag, %2$s: close link tag.
#: app/views/admin/onboarding/payments.php:92
msgid "* Pricing for Stripe Tax API starts at 0.50 USD per transaction, where you're registered to collect taxes. This includes 10 calculation API calls per transaction and is priced at 0.05 USD per additional calculation API call beyond 10. To learn more, visit the %1$sStripe Tax pricing page%2$s."
msgstr ""

#: app/views/admin/onboarding/payments.php:129
msgid "Once Stripe Tax is enabled in the Stripe dashboard, you can enable Stripe Tax at MemberPress &rarr; Settings &rarr; Taxes."
msgstr ""

#: app/views/admin/onboarding/rules.php:5
msgid "Let's create a Rule to protect your content"
msgstr ""

#: app/views/admin/onboarding/rules.php:6
msgid "Rules are the magic behind MemberPress – giving you super specialized paywalling power with the click of a button."
msgstr ""

#: app/views/admin/onboarding/rules.php:7
msgid "You can use Rules to protect pages, child pages, posts, custom post types, categories, tags... almost anything you can imagine. Here, we'll set your first Rule."
msgstr ""

#: app/views/admin/onboarding/rules.php:9
#: app/views/admin/onboarding/rules.php:36
msgid "Create Rule"
msgstr ""

#: app/views/admin/onboarding/rules.php:15
msgid "Your rules"
msgstr ""

#: app/views/admin/onboarding/rules.php:18
msgid "Course / Page"
msgstr ""

#. Translators: %1$s: open underline tag, %2$s: close underline tag.
#: app/views/admin/onboarding/rules.php:56
msgid "More advanced options are available in %1$sMemberPress > Rules%2$s"
msgstr ""

#: app/views/admin/onboarding/welcome.php:8
msgid "Welcome and thank you for choosing us!"
msgstr ""

#: app/views/admin/onboarding/welcome.php:10
msgid "MemberPress setup is fast and easy. Click below, and we'll walk you through the quick initial process. And don't worry. You can go back and change anything you do – at anytime. Nothing's permanent (unless you want it to be). So feel free to explore!"
msgstr ""

#: app/views/admin/onboarding/welcome.php:13
msgid "Get Started"
msgstr ""

#. Translators: %1$s: the license edition, %2$s: the installed edition, %3$s: open link tag, %4$s: close link tag.
#: app/views/admin/options/active_license.php:14
msgid "This License Key is for %1$s, but %2$s is installed. %3$sClick here%4$s to install the correct edition for the license (%1$s)."
msgstr ""

#: app/views/admin/options/active_license.php:26
msgid "Active License Key Information:"
msgstr ""

#: app/views/admin/options/active_license.php:29
#: app/views/admin/options/inactive_license.php:18
msgid "License Key:"
msgstr ""

#: app/views/admin/options/active_license.php:33
#: app/views/admin/transactions/trans_form.php:69
msgid "Status:"
msgstr ""

#: app/views/admin/options/active_license.php:34
msgid "Active on %s"
msgstr ""

#: app/views/admin/options/active_license.php:37
msgid "Product:"
msgstr ""

#: app/views/admin/options/active_license.php:41
msgid "Activations:"
msgstr ""

#: app/views/admin/options/active_license.php:57
msgid "Deactivate License Key on %s"
msgstr ""

#: app/views/admin/options/courses_migrator.php:5
msgid "Migrate to MemberPress Courses"
msgstr ""

#: app/views/admin/options/courses_migrator.php:12
msgid "LearnDash"
msgstr ""

#: app/views/admin/options/courses_migrator.php:13
msgid "Migrate courses, lessons and quizzes from LearnDash."
msgstr ""

#: app/views/admin/options/courses_migrator.php:17
msgid "Please stay on this page"
msgstr ""

#: app/views/admin/options/courses_migrator.php:23
msgid "No compatible migrations found."
msgstr ""

#: app/views/admin/options/custom_fields_row.php:74
msgid "Slug:"
msgstr ""

#: app/views/admin/options/edge_updates.php:5
msgid "Include MemberPress edge (development) releases in automatic updates (not recommended for production websites)"
msgstr ""

#: app/views/admin/options/form.php:6
msgid "User Manual"
msgstr ""

#: app/views/admin/options/form.php:19
msgid "License"
msgstr ""

#: app/views/admin/options/form.php:20
msgid "Pages"
msgstr ""

#: app/views/admin/options/form.php:22
msgid "Fields"
msgstr ""

#: app/views/admin/options/form.php:23
msgid "Payments"
msgstr ""

#: app/views/admin/options/form.php:25
msgid "Marketing"
msgstr ""

#: app/views/admin/options/form.php:26
msgid "Info"
msgstr ""

#: app/views/admin/options/form.php:27
msgid "General"
msgstr ""

#: app/views/admin/options/form.php:33
msgid "MemberPress License"
msgstr ""

#: app/views/admin/options/form.php:49
msgid "You're currently running version %s of MemberPress."
msgstr ""

#: app/views/admin/options/form.php:56
msgid "Reserved Pages"
msgstr ""

#: app/views/admin/options/form.php:72
msgid "MemberPress Coaching Page"
msgstr ""

#: app/views/admin/options/form.php:73
msgid "Coaching"
msgstr ""

#: app/views/admin/options/form.php:79
msgid "Pages Slugs:"
msgstr ""

#: app/views/admin/options/form.php:82
msgid "Pages Slugs"
msgstr ""

#: app/views/admin/options/form.php:83
msgid "Use these fields to customize the base slug of urls for your groups and memberships."
msgstr ""

#: app/views/admin/options/form.php:84
msgid "Note: It isn't recommended that you change these values if you already have existing groups and membership pages on a production membership site because all your urls for them will change (WordPress will attempt to redirect from old urls to new urls)."
msgstr ""

#: app/views/admin/options/form.php:90
msgid "Group Pages Slug:"
msgstr ""

#: app/views/admin/options/form.php:96
msgid "Membership Pages Slug:"
msgstr ""

#: app/views/admin/options/form.php:108
msgid "Redirect unauthorized visitors to a specific URL"
msgstr ""

#: app/views/admin/options/form.php:111
msgid "Redirect Unauthorized Access"
msgstr ""

#: app/views/admin/options/form.php:112
msgid "MemberPress allows you to handle unauthorized access by replacing the content on page or via a redirection to a specific url.<br/><br/>When this is checked, unauthorized visits will be redirected to a url, otherwise the unauthorized message and login form will appear on the page."
msgstr ""

#: app/views/admin/options/form.php:116
msgid "Redirect method:"
msgstr ""

#: app/views/admin/options/form.php:119
msgid "Redirect Method"
msgstr ""

#: app/views/admin/options/form.php:120
msgid "It is highly recommended that \"template_redirect\" should be your default redirect method.<br/><br/>However, this does not work with all WordPress themes. If you find that the unauthorized redirection is not happening when enabled try switching this to \"init\" instead."
msgstr ""

#: app/views/admin/options/form.php:123
msgid "(recommended)"
msgstr ""

#: app/views/admin/options/form.php:129
msgid "Redirect non-singular views:"
msgstr ""

#: app/views/admin/options/form.php:132
msgid "Redirect Non-Singular Views"
msgstr ""

#: app/views/admin/options/form.php:133
msgid "If any post in a non-singular view (EX: Blog page, category pages, archive pages etc) is protected, then do not allow the unauthorized members to see this non-singular view at all."
msgstr ""

#: app/views/admin/options/form.php:136
msgid "URL to direct unauthorized visitors to:"
msgstr ""

#: app/views/admin/options/form.php:139
msgid "Unauthorized Redirection URL"
msgstr ""

#: app/views/admin/options/form.php:140
msgid "This is the URL that visitors will be redirected to when trying to access unauthorized content."
msgstr ""

#: app/views/admin/options/form.php:144
msgid "You can use the <strong>[mepr-unauthorized-message]</strong> shortcode on this unauthorized page (assuming this url points to a page on this site)."
msgstr ""

#: app/views/admin/options/form.php:149
msgid "Show an excerpt to unauthorized visitors"
msgstr ""

#: app/views/admin/options/form.php:166
msgid "Show a login form on pages containing unauthorized message"
msgstr ""

#: app/views/admin/options/form.php:171
msgid "Default Unauthorized Message:"
msgstr ""

#: app/views/admin/options/form.php:174
msgid "Default Unauthorized Message"
msgstr ""

#: app/views/admin/options/form.php:175
msgid "This is the default message that will show up when a user is not allowed to access the content on a page."
msgstr ""

#: app/views/admin/options/form.php:186
msgid "Permissions:"
msgstr ""

#: app/views/admin/options/form.php:192
msgid "Disable the WordPress admin bar for members"
msgstr ""

#: app/views/admin/options/form.php:198
msgid "Keep members out of the WordPress Dashboard"
msgstr ""

#: app/views/admin/options/form.php:206
msgid "Allow Members to Cancel their own subscriptions"
msgstr ""

#: app/views/admin/options/form.php:213
msgid "Allow Members to Pause &amp; Resume their own subscriptions"
msgstr ""

#: app/views/admin/options/form.php:218
msgid "Pausing &amp; Resuming Subscriptions"
msgstr ""

#: app/views/admin/options/form.php:219
msgid "This option will only be available if this is enabled and the user purchased their subscription using PayPal or Stripe."
msgstr ""

#: app/views/admin/options/form.php:225
msgid "Registration:"
msgstr ""

#: app/views/admin/options/form.php:229
msgid "Password Strength Meter:"
msgstr ""

#: app/views/admin/options/form.php:233
msgid "Show &amp; Require Medium Password or Stronger (Recommended)"
msgstr ""

#: app/views/admin/options/form.php:234
msgid "Show &amp; Require Strong Password or Stronger"
msgstr ""

#: app/views/admin/options/form.php:235
msgid "Show &amp; Require Very Strong Password"
msgstr ""

#: app/views/admin/options/form.php:243
msgid "Disable the standard WordPress registration form"
msgstr ""

#: app/views/admin/options/form.php:249
msgid "Enable Coupon Field on membership registration forms"
msgstr ""

#: app/views/admin/options/form.php:257
msgid "Members must use their email address for their Username"
msgstr ""

#: app/views/admin/options/form.php:263
msgid "Pro-rate subscription prices when a member upgrades"
msgstr ""

#: app/views/admin/options/form.php:271
msgid "Disable the 1 day grace period after signup"
msgstr ""

#: app/views/admin/options/form.php:275
msgid "1 Day Grace Period"
msgstr ""

#: app/views/admin/options/form.php:276
msgid "PayPal, Stripe, and Authorize.net can sometimes take up to 24 hours to process the first payment on a members recurring subscription. By default MemberPress allows a 1 day grace period after a member signs up, so they can access the site immediately rather than wait for their payment to clear."
msgstr ""

#: app/views/admin/options/form.php:276
msgid "If you would like to make them wait for the payment to clear before they are allowed to access the site, then enable this option."
msgstr ""

#: app/views/admin/options/form.php:282
msgid "Disable Password Fields on membership registration forms"
msgstr ""

#: app/views/admin/options/form.php:286
msgid "Disable Password Fields"
msgstr ""

#: app/views/admin/options/form.php:287
msgid "When this option is checked, the Password, Password Confirmation & Password Strength fields will not appear on the checkout page. The Member will then receive an email after they register that will provide the steps they can take to set up a password."
msgstr ""

#: app/views/admin/options/form.php:295
msgid "Enable Single Page Checkout"
msgstr ""

#: app/views/admin/options/form.php:299
msgid "Single Page Checkout"
msgstr ""

#: app/views/admin/options/form.php:300
msgid "Enabling this will eliminate the second step of the checkout process. Users will be able to enter their personal and payment details during the first step instead."
msgstr ""

#: app/views/admin/options/form.php:307
msgid "Enable Single Page Checkout Invoice"
msgstr ""

#: app/views/admin/options/form.php:311
msgid "Single Page Checkout Invoice"
msgstr ""

#: app/views/admin/options/form.php:312
msgid "Enabling this will display Invoice table above the payment options."
msgstr ""

#: app/views/admin/options/form.php:321
msgid "Require Terms of Service on membership registration forms"
msgstr ""

#: app/views/admin/options/form.php:330
msgid "URL to your Terms of Service page:"
msgstr ""

#: app/views/admin/options/form.php:338
msgid "Terms of Service Checkbox Title:"
msgstr ""

#: app/views/admin/options/form.php:349
msgid "Require Privacy Policy acceptance on membership registration forms"
msgstr ""

#: app/views/admin/options/form.php:356
msgid "Your Privacy Policy: "
msgstr ""

#: app/views/admin/options/form.php:365
msgid "Privacy Policy Page"
msgstr ""

#: app/views/admin/options/form.php:372
msgid "Privacy Policy Checkbox Title:"
msgstr ""

#: app/views/admin/options/form.php:375
msgid "Privacy Policy Checkbox Title"
msgstr ""

#: app/views/admin/options/form.php:376
msgid "This will appear next to the Privacy Policy checkbox on membership registration forms. The text between % characters will become the link text to your Privacy Policy page."
msgstr ""

#: app/views/admin/options/form.php:386
msgid "Login & Logout:"
msgstr ""

#: app/views/admin/options/form.php:390
#: app/views/admin/options/form.php:394
msgid "Use MemberPress login page URL"
msgstr ""

#: app/views/admin/options/form.php:395
msgid "Use this option to return MemberPress login page URL when other plugins/themes call the wp_login_url() function. If you have other plugins that use their own Login pages too you may want to leave this option disabled. This does NOT prevent directly accessing /wp-login.php page."
msgstr ""

#: app/views/admin/options/form.php:398
msgid "URL to direct member to after login:"
msgstr ""

#: app/views/admin/options/form.php:401
msgid "Login Redirect URL"
msgstr ""

#: app/views/admin/options/form.php:402
msgid "For this to work you must have the Login page set in the MemberPress options. You can also override this option on a per-membership basis in the Advanced box when creating/editing a membership."
msgstr ""

#: app/views/admin/options/form.php:405
msgid "URL to direct member to after logout:"
msgstr ""

#: app/views/admin/options/form.php:408
msgid "Logout Redirect URL"
msgstr ""

#: app/views/admin/options/form.php:409
msgid "Set what URL you want the member to be taken to when they logout on your site. This setting applies to Administrators as well."
msgstr ""

#: app/views/admin/options/form.php:414
msgid "Account Page Welcome Message"
msgstr ""

#: app/views/admin/options/form.php:417
msgid "This text will appear below the navigation on the Account Page."
msgstr ""

#: app/views/admin/options/form.php:423
msgid "Logged In Purchases:"
msgstr ""

#: app/views/admin/options/form.php:427
msgid "Show The Fields Below For Logged-in Purchases"
msgstr ""

#: app/views/admin/options/form.php:430
msgid "Extended User Information Fields:"
msgstr ""

#: app/views/admin/options/form.php:434
msgid "Name Fields:"
msgstr ""

#: app/views/admin/options/form.php:445
#: app/views/admin/options/form.php:463
msgid "Require"
msgstr ""

#: app/views/admin/options/form.php:451
msgid "Address Fields:"
msgstr ""

#: app/views/admin/options/form.php:452
msgid "(Required when taxes are enabled)"
msgstr ""

#: app/views/admin/options/form.php:469
msgid "Custom User Information Fields:"
msgstr ""

#: app/views/admin/options/form.php:472
msgid "Custom User Information Fields"
msgstr ""

#: app/views/admin/options/form.php:473
msgid "You can specify custom fields to be used with your users' account. Just click the 'plus' button below to add your first field."
msgstr ""

#: app/views/admin/options/form.php:478
msgid "Add new Custom Field"
msgstr ""

#: app/views/admin/options/form.php:484
msgid "Payment Methods"
msgstr ""

#: app/views/admin/options/form.php:498
msgid "A gateway is required to accept payments.Click the (+) below to add your first payment gateway."
msgstr ""

#: app/views/admin/options/form.php:500
msgid "Add a Payment Method"
msgstr ""

#: app/views/admin/options/form.php:500
msgid "Add Payment Method"
msgstr ""

#: app/views/admin/options/form.php:507
#: app/views/admin/options/form.php:510
msgid "Member Notices"
msgstr ""

#: app/views/admin/options/form.php:511
msgid "These are notices that will be sent to your members when events happen in MemberPress."
msgstr ""

#: app/views/admin/options/form.php:517
msgid "Admin Emails &amp; Notices"
msgstr ""

#: app/views/admin/options/form.php:520
msgid "Admin Notices"
msgstr ""

#: app/views/admin/options/form.php:521
msgid "These are notices that will be sent to the addresses you've set below when events happen in MemberPress."
msgstr ""

#: app/views/admin/options/form.php:525
msgid "Admin Email Addresses:"
msgstr ""

#: app/views/admin/options/form.php:528
msgid "Notification Email Addresses"
msgstr ""

#: app/views/admin/options/form.php:529
msgid "This is a comma separated list of email addresses that will receive admin notifications. This defaults to your admin email set in \"Settings\" -> \"General\" -> \"E-mail Address\""
msgstr ""

#: app/views/admin/options/form.php:537
msgid "Privacy Settings"
msgstr ""

#: app/views/admin/options/form.php:540
msgid "Include Privacy Policy link"
msgstr ""

#: app/views/admin/options/form.php:543
msgid "Privacy Policy Link"
msgstr ""

#: app/views/admin/options/form.php:544
msgid "When this option is checked, a link to your site's Privacy Policy page will be included in all email communication."
msgstr ""

#: app/views/admin/options/form.php:547
msgid "Send Mail From"
msgstr ""

#: app/views/admin/options/form.php:549
msgid "From Name:"
msgstr ""

#: app/views/admin/options/form.php:552
msgid "From Email:"
msgstr ""

#: app/views/admin/options/form.php:562
#: app/views/admin/options/form.php:565
msgid "Disable global auto-responder lists"
msgstr ""

#: app/views/admin/options/form.php:566
msgid "When this option is checked, and a membership has its own auto-responder list set under its Advanced tab. The global list you set on this page will be disabled on that membership page. The member will only be added to the membership's list, and not the global list in this case."
msgstr ""

#: app/views/admin/options/form.php:570
msgid "Opt-in box checked by default"
msgstr ""

#: app/views/admin/options/form.php:573
msgid "Opt-in checkbox initial value"
msgstr ""

#: app/views/admin/options/form.php:574
msgid "Use this option to set whether the opt-in checkbox shown at signup should be checked or unchecked by default. When enabled, the checkbox will be checked by default."
msgstr ""

#: app/views/admin/options/form.php:577
msgid "Enable/Disable Integrations"
msgstr ""

#: app/views/admin/options/form.php:586
#: app/views/admin/options/form.php:589
msgid "Merchant Business Address"
msgstr ""

#: app/views/admin/options/form.php:590
msgid "Enter your business's name & address. Used to figure out tax rates and to include in email receipts."
msgstr ""

#: app/views/admin/options/form.php:597
msgid "Business Name*"
msgstr ""

#: app/views/admin/options/form.php:605
msgid "Address Line 1*"
msgstr ""

#: app/views/admin/options/form.php:613
msgid "Address Line 2"
msgstr ""

#: app/views/admin/options/form.php:621
msgid "City*"
msgstr ""

#: app/views/admin/options/form.php:629
msgid "Country*"
msgstr ""

#: app/views/admin/options/form.php:637
msgid "State*"
msgstr ""

#: app/views/admin/options/form.php:645
msgid "Postcode*"
msgstr ""

#: app/views/admin/options/form.php:657
msgid "Internationalization"
msgstr ""

#: app/views/admin/options/form.php:662
msgid "Language Code:"
msgstr ""

#: app/views/admin/options/form.php:670
msgid "Currency Code:"
msgstr ""

#: app/views/admin/options/form.php:678
msgid "Currency Symbol:"
msgstr ""

#: app/views/admin/options/form.php:686
msgid "Symbol After Amount"
msgstr ""

#: app/views/admin/options/form.php:689
msgid "Display Currency Symbol After Amount"
msgstr ""

#: app/views/admin/options/form.php:690
msgid "Will display the currency symbol after the price of the item. For example: 5.00$ instead of $5.00. Unless you know your countries currency symbol should be listed after, leave this unchecked."
msgstr ""

#: app/views/admin/options/form.php:701
msgid "Theme Compatibility"
msgstr ""

#: app/views/admin/options/form.php:706
msgid "Global CSS Styles"
msgstr ""

#: app/views/admin/options/form.php:709
msgid "Load all of MemberPress's CSS Styles on Each Page"
msgstr ""

#: app/views/admin/options/form.php:710
msgid "When this option is enabled, all of MemberPress's CSS Styles will be loaded on every front end page of your website."
msgstr ""

#: app/views/admin/options/form.php:710
msgid "Note: This option should only be enabled if MemberPress compatibility with your theme requires its usage."
msgstr ""

#: app/views/admin/options/form.php:721
msgid "SEO & PayWall"
msgstr ""

#: app/views/admin/options/form.php:726
msgid "Authorize Search Engines"
msgstr ""

#: app/views/admin/options/form.php:729
msgid "Treat popular search engines as authorized members?"
msgstr ""

#: app/views/admin/options/form.php:730
msgid "When this option is enabled -- Google, Yahoo, Ask, and Bing/MSN are allowed to view protected content on your site as if they were authorized members. This can help search engines index your content."
msgstr ""

#: app/views/admin/options/form.php:730
msgid "Note: This does not work with caching enabled on your site. So avoid enabling this when using WP Super Cache, W3TC, WP Engine hosting, GoDaddy's Managed WordPress -- or any other plugin/webhost that caches your pages."
msgstr ""

#: app/views/admin/options/form.php:745
msgid "Block Search Engines"
msgstr ""

#: app/views/admin/options/form.php:748
msgid "Ask search engines to not index protected content?"
msgstr ""

#: app/views/admin/options/form.php:749
msgid "Automatically ask search engines not to index protected content on your site. If the 'Authorize Search Engines' option is enabled, this setting has no effect."
msgstr ""

#: app/views/admin/options/form.php:764
#: app/views/admin/options/form.php:767
msgid "Enable PayWall"
msgstr ""

#: app/views/admin/options/form.php:768
msgid "When enabled, you can specify how many free page views an unauthorized guest will have before being shown the unauthorized messages. We strongly encourage you to leave this option disabled during your initial setup and testing of MemberPress."
msgstr ""

#: app/views/admin/options/form.php:784
msgid "# Free Views"
msgstr ""

#: app/views/admin/options/form.php:787
msgid "Number of Free Views per Guest"
msgstr ""

#: app/views/admin/options/form.php:788
msgid "The number of free views unauthorized users can have before the paywall kicks in to prevent access to protected content."
msgstr ""

#: app/views/admin/options/form.php:800
msgid "Weekly Summary Email"
msgstr ""

#: app/views/admin/options/form.php:805
#: app/views/admin/options/form.php:808
msgid "Disable Weekly Summary Email"
msgstr ""

#: app/views/admin/options/form.php:809
msgid "A weekly summary email is sent from MemberPress with a report on the revenue and transactions from the previous week. You can stop the email being sent by checking this option."
msgstr ""

#: app/views/admin/options/form.php:820
msgid "Rewrite Rules"
msgstr ""

#: app/views/admin/options/form.php:825
msgid "Disable Rewrite Rules"
msgstr ""

#: app/views/admin/options/form.php:828
msgid "Disable mod_rewrite (.htaccess) Rules"
msgstr ""

#: app/views/admin/options/form.php:829
msgid "If you are having problems getting other 3rd party applications such as phpBB or phpList to work along side MemberPress, you may need to check this option. Disabling mod_rewrite will mean that individual files cannot be protected with the Custom URI Rules."
msgstr ""

#: app/views/admin/options/form.php:840
msgid "Misc"
msgstr ""

#: app/views/admin/options/form.php:845
#: app/views/admin/options/form.php:848
msgid "Hide MemberPress Menu in Admin Bar"
msgstr ""

#: app/views/admin/options/form.php:849
msgid "Enabling this option will hide the MemberPress admin bar menu"
msgstr ""

#: app/views/admin/options/form.php:858
#: app/views/admin/options/form.php:861
msgid "Enable WP Rest API search discovery protection"
msgstr ""

#: app/views/admin/options/form.php:862
msgid "Enabling this option will protect your WP Rest API search discovery"
msgstr ""

#: app/views/admin/options/form.php:879
msgid "Update Options"
msgstr ""

#: app/views/admin/options/gateway.php:17
#: app/views/admin/subscriptions/form.php:81
#: app/views/admin/transactions/trans_form.php:82
msgid "Gateway:"
msgstr ""

#: app/views/admin/options/gateway.php:31
msgid "ID:"
msgstr ""

#: app/views/admin/options/gateway.php:35
msgid "Show Payment Label"
msgstr ""

#: app/views/admin/options/gateway.php:36
msgid "Show Payment Icon"
msgstr ""

#: app/views/admin/options/gateway.php:37
msgid "Show Payment Description"
msgstr ""

#. Translators: %1$s: link to memberpress.com, %2$s: link to memberpress.com/login.
#: app/views/admin/options/inactive_license.php:9
msgid "You must have a License Key to enable automatic updates for MemberPress, access the admin areas of MemberPress, and receive support. If you don't have a License please go to %1$s to get one. If you do have a license you can login at %2$s to manage your licenses and site activations."
msgstr ""

#: app/views/admin/options/inactive_license.php:21
msgid "Activate License Key"
msgstr ""

#: app/views/admin/popups/deactivation_survey.php:8
msgid "Quick Feedback"
msgstr ""

#: app/views/admin/popups/deactivation_survey.php:10
msgid "If you have a moment, please share why you are deactivating MemberPress:"
msgstr ""

#: app/views/admin/popups/deactivation_survey.php:29
msgid "Submit & Deactivate"
msgstr ""

#: app/views/admin/popups/deactivation_survey.php:30
msgid "Skip & Deactivate"
msgstr ""

#: app/views/admin/products/advanced.php:7
#: app/views/admin/products/advanced.php:10
msgid "Membership Access URL"
msgstr ""

#: app/views/admin/products/advanced.php:11
msgid "This is the URL where those who purchase this membership can get the content they now have access to."
msgstr ""

#: app/views/admin/products/advanced.php:11
msgid "When set, the Membership Access URL will make this membership name clickable when a member views their Subscriptions/Payments tabs on the Account page."
msgstr ""

#: app/views/admin/products/advanced.php:20
msgid "Membership Pricing Terms:"
msgstr ""

#: app/views/admin/products/advanced.php:24
msgid "Hidden"
msgstr ""

#: app/views/admin/products/advanced.php:29
msgid "Membership Pricing Terms"
msgstr ""

#: app/views/admin/products/advanced.php:30
msgid "By default MemberPress will automatically generate the pricing terms for you on the membership registration page. But in some cases you'll want to either present custom text for the terms ... or hide it alltogether."
msgstr ""

#: app/views/admin/products/advanced.php:31
msgid "This will allow you to customize the pricing terms on the <b>Registration, Payment, and Account Subscription's pages</b> for this membership. Here's a description of these options:"
msgstr ""

#: app/views/admin/products/advanced.php:32
msgid "<b>Default:</b> This will enable the system-generated membership terms."
msgstr ""

#: app/views/admin/products/advanced.php:33
msgid "<b>Custom:</b> This will enable you to enter your own description for your membership terms."
msgstr ""

#: app/views/admin/products/advanced.php:34
msgid "<b>Hidden:</b> This will hide the membership terms."
msgstr ""

#: app/views/admin/products/advanced.php:40
msgid "Custom Registration Pricing Terms:"
msgstr ""

#: app/views/admin/products/advanced.php:47
#: app/views/admin/products/advanced.php:51
msgid "Custom Login Redirect URLs"
msgstr ""

#: app/views/admin/products/advanced.php:52
msgid "By default MemberPress will redirect members to the page setup in options. This will allow you to override that default behavior and customize it any way you want."
msgstr ""

#: app/views/admin/products/advanced.php:57
#: app/views/admin/products/advanced.php:62
msgid "Default Login Redirect URL"
msgstr ""

#: app/views/admin/products/advanced.php:63
msgid "If the member logging in has an active Subscription to this membership and none of the custom URLs below apply, they will be redirected to this URL after logging in."
msgstr ""

#: app/views/admin/products/advanced.php:63
msgid "Note: This overrides the Login Redirect URL setting in the MemberPress Options. Leave this option blank to use the Login Redirect URL in the MemberPress Options instead."
msgstr ""

#: app/views/admin/products/advanced.php:67
#: app/views/admin/products/advanced.php:71
msgid "Add Custom Login Redirect URLs"
msgstr ""

#: app/views/admin/products/advanced.php:72
msgid "This allows you to redirect a user to different URLs based on how many times they have already logged in. So for example, if you wanted to show upsell pages to members on their 3rd and 6th logins, you can do that by setting the URL to send the member to when they login for the 3rd time and again on the 6th time."
msgstr ""

#: app/views/admin/products/advanced.php:83
#: app/views/admin/products/advanced.php:98
msgid "Login #"
msgstr ""

#: app/views/admin/products/advanced.php:87
#: app/views/admin/products/advanced.php:102
msgid "Remove URL"
msgstr ""

#: app/views/admin/products/advanced.php:110
msgid "Add URL"
msgstr ""

#: app/views/admin/products/form.php:10
msgid "Price (%s):"
msgstr ""

#: app/views/admin/products/form.php:16
msgid "Billing Type:"
msgstr ""

#: app/views/admin/products/form.php:20
msgid "Recurring"
msgstr ""

#: app/views/admin/products/form.php:21
msgid "One-Time"
msgstr ""

#: app/views/admin/products/form.php:36
#: app/views/admin/products/form.php:39
msgid "Access:"
msgstr ""

#: app/views/admin/products/form.php:42
msgid "Expire"
msgstr ""

#: app/views/admin/products/form.php:43
msgid "Fixed Expire"
msgstr ""

#: app/views/admin/products/form.php:49
msgid "Expire After:"
msgstr ""

#: app/views/admin/products/form.php:58
#: app/views/admin/products/form.php:182
#: app/views/admin/reminders/trigger.php:8
msgid "days"
msgstr ""

#: app/views/admin/products/form.php:61
#: app/views/admin/products/form.php:185
#: app/views/admin/reminders/trigger.php:11
msgid "years"
msgstr ""

#: app/views/admin/products/form.php:70
msgid "Allow Early Renewals"
msgstr ""

#: app/views/admin/products/form.php:77
msgid "Expire On:"
msgstr ""

#: app/views/admin/products/form.php:92
msgid "Allow Early Annual Renewals"
msgstr ""

#: app/views/admin/products/form.php:100
msgid "Interval:"
msgstr ""

#: app/views/admin/products/form.php:118
msgid "Trial Period"
msgstr ""

#: app/views/admin/products/form.php:121
msgid "Trial Period Info"
msgstr ""

#: app/views/admin/products/form.php:122
msgid "The trial period is the number of days listed in the \"Trial Duration\" field. A 1 month trial would be 30 days, 2 months would be 60. Similarly, 1 year would be 365."
msgstr ""

#: app/views/admin/products/form.php:124
msgid "Price must be greater than 0.00 to choose recurring subscriptions."
msgstr ""

#: app/views/admin/products/form.php:129
msgid "Trial Duration (Days):"
msgstr ""

#: app/views/admin/products/form.php:135
#: app/views/admin/subscriptions/form.php:113
msgid "Trial Amount (%s):"
msgstr ""

#: app/views/admin/products/form.php:141
msgid "Allow Only One Trial"
msgstr ""

#: app/views/admin/products/form.php:144
msgid "Restrict Trial to One Per Member"
msgstr ""

#: app/views/admin/products/form.php:145
msgid "When checked, this option will allow a member to go through the trial once. If they cancel their subscription and try to re-subscribe then they'll be able to do so but without the trial. Note: Coupons set to override a membership trial will still work even with this checked."
msgstr ""

#: app/views/admin/products/form.php:153
msgid "Limit Payment Cycles"
msgstr ""

#: app/views/admin/products/form.php:158
msgid "Max # of Payments:"
msgstr ""

#: app/views/admin/products/form.php:164
msgid "Access After Last Cycle:"
msgstr ""

#: app/views/admin/products/form.php:168
msgid "Expire Access"
msgstr ""

#: app/views/admin/products/form.php:169
msgid "Lifetime Access"
msgstr ""

#: app/views/admin/products/form.php:170
msgid "Expire Access After"
msgstr ""

#: app/views/admin/products/order_bumps.php:5
msgid "Oops! Order Bumps is a Pro Perk"
msgstr ""

#: app/views/admin/products/order_bumps.php:6
msgid "Level up and unlock the power today!"
msgstr ""

#. Translators: %1$s: open italics tag, %2$s: close italics tag.
#: app/views/admin/products/order_bumps.php:11
msgid "%1$sTurn every sale into an upsell opportunity!%2$s Don't miss the chance to skyrocket your revenue with MemberPress Order Bumps – the game-changing tool that lets you automatically offer tempting add-ons to your customers right at checkout."
msgstr ""

#: app/views/admin/products/order_bumps.php:18
msgid "Amplify your average transaction value and maximize every customer interaction – all while elevating their shopping experience."
msgstr ""

#. Translators: %1$s: open bold tag, %2$s: close bold tag.
#: app/views/admin/products/order_bumps.php:24
msgid "Level up your earning potential with the %1$sPro-exclusive Order Bumps%2$s feature today!"
msgstr ""

#: app/views/admin/products/permissions.php:23
msgid "Allow users to create multiple, active subscriptions to this membership"
msgstr ""

#: app/views/admin/products/permissions.php:28
msgid "Who can purchase this Membership"
msgstr ""

#: app/views/admin/products/permissions.php:37
msgid "No permissions message"
msgstr ""

#: app/views/admin/products/price_box.php:7
msgid "Preview"
msgstr ""

#: app/views/admin/products/price_box.php:14
msgid "Highlighted"
msgstr ""

#: app/views/admin/products/price_box.php:18
msgid "Highlight"
msgstr ""

#: app/views/admin/products/price_box.php:19
msgid "<strong>Highlighted:</strong> Make this a Highlighted option on the Group Pricing Page. This makes it stand-out from the other listed memberships."
msgstr ""

#: app/views/admin/products/price_box.php:30
#: app/views/admin/products/price_box.php:34
msgid "Pricing Display"
msgstr ""

#: app/views/admin/products/price_box.php:35
msgid "This determines how the price will be displayed on the pricing table. If 'Auto' is selected then MemberPress will automatically generate the price for you, if 'Custom' is selected then you'll be able to enter your own custom pricing terms and if you select 'None' then no price will be visible."
msgstr ""

#: app/views/admin/products/price_box.php:39
msgid "Auto"
msgstr ""

#: app/views/admin/products/price_box.php:48
msgid "Custom Pricing"
msgstr ""

#: app/views/admin/products/price_box.php:54
msgid "Heading Text:"
msgstr ""

#: app/views/admin/products/price_box.php:59
msgid "Benefits:"
msgstr ""

#: app/views/admin/products/price_box.php:67
msgid "Footer Text:"
msgstr ""

#: app/views/admin/products/price_box.php:72
msgid "Button Text:"
msgstr ""

#: app/views/admin/products/price_box.php:77
msgid "Button Position"
msgstr ""

#: app/views/admin/products/price_box.php:80
#: app/views/admin/products/price_box.php:86
msgid "Header"
msgstr ""

#: app/views/admin/products/price_box.php:85
msgid "Footer"
msgstr ""

#: app/views/admin/products/price_box.php:87
msgid "Both"
msgstr ""

#: app/views/admin/products/product_options_meta_box.php:6
#: js/blocks/membership-signup/index.js:11
#: js/build/blocks.js:1
msgid "Registration"
msgstr ""

#: app/views/admin/products/product_options_meta_box.php:7
msgid "Permissions"
msgstr ""

#: app/views/admin/products/product_options_meta_box.php:8
msgid "Price Box"
msgstr ""

#: app/views/admin/products/product_options_meta_box.php:11
msgid "Order Bumps"
msgstr ""

#: app/views/admin/products/registration.php:7
msgid "Registration Button Text:"
msgstr ""

#: app/views/admin/products/registration.php:14
#: app/views/admin/products/registration.php:18
msgid "This Membership is Tax Exempt"
msgstr ""

#: app/views/admin/products/registration.php:19
msgid "If this option is checked then taxes won't be calculated for this Membership."
msgstr ""

#: app/views/admin/products/registration.php:25
msgid "Tax Rates:"
msgstr ""

#: app/views/admin/products/registration.php:27
msgid "Standard Rate"
msgstr ""

#: app/views/admin/products/registration.php:28
msgid "Reduced Rate"
msgstr ""

#: app/views/admin/products/registration.php:33
msgid "Reduced VAT Tax Rates"
msgstr ""

#: app/views/admin/products/registration.php:34
msgid "By default MemberPress will automatically apply standard VAT tax rates. But in some cases you'll want to use a reduced VAT rate for your membership. This only applies to the EU VAT rates."
msgstr ""

#: app/views/admin/products/registration.php:35
msgid "This will allow you to choose appropriate tax for your tangible/intangible products."
msgstr ""

#: app/views/admin/products/registration.php:45
msgid "Enable custom thank you page message"
msgstr ""

#: app/views/admin/products/registration.php:49
msgid "Enable Custom Thank You"
msgstr ""

#: app/views/admin/products/registration.php:50
msgid "Enabling this option will reveal additional options you can use to provide a custom Thank You message or page to show after a member purchases this membership."
msgstr ""

#: app/views/admin/products/registration.php:57
msgid "Enable custom thank you message"
msgstr ""

#: app/views/admin/products/registration.php:61
msgid "Enable Custom Thank You Message"
msgstr ""

#: app/views/admin/products/registration.php:62
msgid "Enabling this option will reveal a new Compose form which you can use to provide a custom message to show on the Thank You page after a member purchases this membership."
msgstr ""

#: app/views/admin/products/registration.php:71
msgid "Enable custom thank you page"
msgstr ""

#: app/views/admin/products/registration.php:75
msgid "Enable Custom Thank You Page"
msgstr ""

#: app/views/admin/products/registration.php:76
msgid "Enabling this option will reveal a dropdown which you can use to provide a custom Thank You page to show after a member purchases this membership."
msgstr ""

#: app/views/admin/products/registration.php:97
msgid "Enabling this option will reveal a drag and drop list of the available payment methods. You can use this to re-order or even hide payment methods from the dropdown on this membership registration page."
msgstr ""

#: app/views/admin/products/registration.php:101
msgid "Active Payment Methods"
msgstr ""

#: app/views/admin/products/registration.php:111
msgid "No Payment Methods were found. Please go to the options page to configure some."
msgstr ""

#: app/views/admin/products/registration.php:129
msgid "Inactive Payment Methods"
msgstr ""

#: app/views/admin/products/registration.php:149
#: app/views/admin/products/registration.php:153
msgid "Customize User Information Fields"
msgstr ""

#: app/views/admin/products/registration.php:154
msgid "Enabling this option will reveal a drag and drop list of the available Custom User Information Fields you defined in the MemberPress Options. You can use this to show/hide certain fields from this membership registration form."
msgstr ""

#: app/views/admin/products/registration.php:158
msgid "Active Fields"
msgstr ""

#: app/views/admin/products/registration.php:164
msgid "No fields were found. Please go to the options page to configure some."
msgstr ""

#: app/views/admin/products/registration.php:187
msgid "Inactive Fields"
msgstr ""

#: app/views/admin/products/registration.php:208
#: app/views/admin/products/registration.php:212
msgid "Disable Address Fields"
msgstr ""

#: app/views/admin/products/registration.php:213
msgid "When Addresses are enabled and required, you can disable this option only on free memberships."
msgstr ""

#: app/views/admin/products/registration.php:223
msgid "Membership Shortcodes"
msgstr ""

#: app/views/admin/products/registration.php:225
msgid "You can use this shortcode anywhere on your site to quickly display a link to this membership page. If the text inbetween the shortcode is not present, MemberPress will use the membership title as the link text instead."
msgstr ""

#: app/views/admin/products/registration.php:226
#: app/views/admin/products/registration.php:229
msgid "Optional link label here..."
msgstr ""

#: app/views/admin/products/registration.php:228
msgid "This shortcode can be used to show a link to the Membership Access URL set in the Advanced tab."
msgstr ""

#: app/views/admin/products/registration.php:231
msgid "Shortcode to be used on this membership page to manually place the registration form."
msgstr ""

#: app/views/admin/products/registration.php:233
msgid "Shortcode which can be used on any other WordPress page, post or custom post type to manually place the registration form for this membership."
msgstr ""

#: app/views/admin/products/stripe_tax.php:6
#: app/views/admin/products/stripe_tax.php:19
msgid "Stripe Tax Category"
msgstr ""

#: app/views/admin/products/stripe_tax.php:8
msgid "Default tax category"
msgstr ""

#: app/views/admin/products/stripe_tax.php:9
msgid "General - Electronically Supplied Services"
msgstr ""

#: app/views/admin/products/stripe_tax.php:10
msgid "General - Tangible Goods"
msgstr ""

#: app/views/admin/products/stripe_tax.php:11
msgid "General - Services"
msgstr ""

#: app/views/admin/products/stripe_tax.php:15
msgid "Enter custom tax code"
msgstr ""

#. Translators: %1$s: br tag, %2$s: open strong tag, %3$s: close strong tag, %4$s: open link tag, %5$s: close link tag.
#: app/views/admin/products/stripe_tax.php:22
msgid "Set the Stripe tax category for this membership.%1$s%1$sThe %2$sDefault tax category%3$s will use the default tax category from the %4$sStripe tax settings%5$s.%1$s%1$sSelect %2$sCustom%3$s to be able to enter a %6$scustom tax category%5$s."
msgstr ""

#: app/views/admin/readylaunch/account.php:9
#: app/views/admin/readylaunch/coaching.php:9
msgid "Account Settings"
msgstr ""

#: app/views/admin/readylaunch/account.php:21
#: app/views/admin/readylaunch/coaching.php:21
#: app/views/admin/readylaunch/login.php:22
#: app/views/admin/readylaunch/thankyou.php:22
msgid "Show Welcome Image"
msgstr ""

#: app/views/admin/readylaunch/account.php:55
#: app/views/admin/readylaunch/coaching.php:55
msgid "Upload Welcome Image"
msgstr ""

#: app/views/admin/readylaunch/account.php:64
#: app/views/admin/readylaunch/coaching.php:64
msgctxt "Uploader: Upload Welcome Image - or - Select Image"
msgid "or"
msgstr ""

#: app/views/admin/readylaunch/account.php:67
#: app/views/admin/readylaunch/coaching.php:67
#: app/views/admin/readylaunch/options.php:26
msgid "Select Image"
msgstr ""

#: app/views/admin/readylaunch/checkout.php:9
msgid "Registration Page Settings"
msgstr ""

#: app/views/admin/readylaunch/checkout.php:21
msgid "Show Price Terms"
msgstr ""

#: app/views/admin/readylaunch/login.php:9
msgid "Login Settings"
msgstr ""

#: app/views/admin/readylaunch/options.php:8
msgid "Global Design Settings"
msgstr ""

#: app/views/admin/readylaunch/options.php:16
msgid "Your Logo (1000x300px recommended, svg or png)"
msgstr ""

#: app/views/admin/readylaunch/options.php:20
msgid "Logo (will be placed on top of brand color in all cases)"
msgstr ""

#: app/views/admin/readylaunch/options.php:41
msgid "Brand Colors"
msgstr ""

#: app/views/admin/readylaunch/options.php:46
msgid "Primary Color"
msgstr ""

#: app/views/admin/readylaunch/options.php:56
msgid "Footer Settings"
msgstr ""

#: app/views/admin/readylaunch/options.php:61
msgid "WP Footer Hook"
msgstr ""

#: app/views/admin/readylaunch/options.php:68
msgid "Disabled"
msgstr ""

#: app/views/admin/readylaunch/options.php:73
msgid "ReadyLaunch™ Templates"
msgstr ""

#: app/views/admin/readylaunch/options.php:86
msgid "Pricing Page"
msgstr ""

#: app/views/admin/readylaunch/options.php:89
#: app/views/admin/readylaunch/options.php:105
#: app/views/admin/readylaunch/options.php:122
#: app/views/admin/readylaunch/options.php:138
#: app/views/admin/readylaunch/options.php:154
msgid "Customize"
msgstr ""

#: app/views/admin/readylaunch/options.php:101
msgid "Registration Page"
msgstr ""

#: app/views/admin/readylaunch/options.php:118
msgid "Thank You Page"
msgstr ""

#: app/views/admin/readylaunch/pricing.php:10
msgid "Pricing Settings"
msgstr ""

#: app/views/admin/readylaunch/pricing.php:38
msgid "CTA Button Color"
msgstr ""

#: app/views/admin/readylaunch/pricing.php:51
msgid "Subheadline"
msgstr ""

#: app/views/admin/readylaunch/thankyou.php:9
msgid "Thank You Settings"
msgstr ""

#: app/views/admin/readylaunch/thankyou.php:88
msgid "Custom Message"
msgstr ""

#: app/views/admin/readylaunch/thankyou.php:95
#: app/views/admin/readylaunch/thankyou.php:100
msgid "Thank You Message"
msgstr ""

#: app/views/admin/readylaunch/thankyou.php:101
msgid "Add custom message that will be displayed after a successful purchase."
msgstr ""

#: app/views/admin/reminders/emails.php:6
msgid "Send only for specific Memberships"
msgstr ""

#: app/views/admin/reminders/emails.php:8
msgid "Memberships for this Reminder"
msgstr ""

#: app/views/admin/reminders/trigger.php:7
msgid "hours"
msgstr ""

#: app/views/admin/reminders/trigger.php:15
msgid "after Member Signs Up"
msgstr ""

#: app/views/admin/reminders/trigger.php:16
msgid "after Signup Abandoned"
msgstr ""

#: app/views/admin/reminders/trigger.php:17
msgid "before Subscription Expires"
msgstr ""

#: app/views/admin/reminders/trigger.php:18
msgid "after Subscription Expires"
msgstr ""

#: app/views/admin/reminders/trigger.php:19
msgid "before Subscription Renews"
msgstr ""

#: app/views/admin/reminders/trigger.php:20
msgid "after Subscription Renews"
msgstr ""

#: app/views/admin/reminders/trigger.php:21
msgid "before Subscription Trial Ends"
msgstr ""

#: app/views/admin/reminders/trigger.php:22
msgid "before Credit Card Expires"
msgstr ""

#: app/views/admin/reminders/trigger.php:23
msgid "after Credit Card Expires"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:6
#: app/views/admin/reports/month_info_blocks.php:6
#: app/views/admin/reports/summary_email.php:99
#: app/views/admin/reports/year_info_blocks.php:6
#: app/views/admin/widgets/admin_stats_widget.php:10
msgid "Pending Transactions"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:11
#: app/views/admin/reports/month_info_blocks.php:11
#: app/views/admin/reports/summary_email.php:106
#: app/views/admin/reports/year_info_blocks.php:11
#: app/views/admin/widgets/admin_stats_widget.php:15
msgid "Failed Transactions"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:16
#: app/views/admin/reports/month_info_blocks.php:16
#: app/views/admin/reports/summary_email.php:92
#: app/views/admin/reports/year_info_blocks.php:16
#: app/views/admin/widgets/admin_stats_widget.php:20
msgid "Refunded Transactions"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:21
#: app/views/admin/reports/month_info_blocks.php:21
#: app/views/admin/reports/summary_email.php:118
#: app/views/admin/reports/year_info_blocks.php:21
#: app/views/admin/widgets/admin_stats_widget.php:25
msgid "Completed Transactions"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:26
#: app/views/admin/reports/month_info_blocks.php:26
#: app/views/admin/reports/summary_email.php:42
#: app/views/admin/reports/year_info_blocks.php:26
#: app/views/admin/widgets/admin_stats_widget.php:30
msgid "Amount Collected"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:31
#: app/views/admin/reports/month_info_blocks.php:31
#: app/views/admin/reports/summary_email.php:49
#: app/views/admin/reports/year_info_blocks.php:31
#: app/views/admin/widgets/admin_stats_widget.php:35
msgid "Amount Refunded"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:36
#: app/views/admin/reports/month_info_blocks.php:36
#: app/views/admin/reports/year_info_blocks.php:36
msgid "Taxes Collected"
msgstr ""

#: app/views/admin/reports/all_time_info_blocks.php:41
#: app/views/admin/reports/month_info_blocks.php:41
msgid "Total Net Income"
msgstr ""

#: app/views/admin/reports/main.php:15
msgid "All-Time"
msgstr ""

#: app/views/admin/reports/main.php:38
#: app/views/admin/reports/main.php:76
msgid "Amounts"
msgstr ""

#: app/views/admin/reports/main.php:121
msgid "Total Transactions By Membership"
msgstr ""

#: app/views/admin/reports/main.php:122
msgid "Amounts By Day Of Month"
msgstr ""

#: app/views/admin/reports/main.php:123
msgid "Transactions By Day Of Month"
msgstr ""

#: app/views/admin/reports/main.php:124
msgid "Day Of Month"
msgstr ""

#: app/views/admin/reports/main.php:125
msgid "Amounts By Month Of Year"
msgstr ""

#: app/views/admin/reports/main.php:126
msgid "Transactions By Month Of Year"
msgstr ""

#: app/views/admin/reports/main.php:127
msgid "Month Of Year"
msgstr ""

#: app/views/admin/reports/month_table.php:13
#: app/views/admin/reports/skeleton_table.php:13
#: app/views/admin/reports/year_table.php:13
msgid "Collected"
msgstr ""

#: app/views/admin/reports/month_table.php:16
#: app/views/admin/reports/skeleton_table.php:16
#: app/views/admin/reports/year_table.php:16
msgid "Net Total"
msgstr ""

#: app/views/admin/reports/month_table.php:90
#: app/views/admin/reports/year_table.php:92
msgid "Totals"
msgstr ""

#: app/views/admin/reports/month_table.php:122
#: app/views/admin/reports/year_table.php:124
#: app/views/admin/widgets/admin_stats_widget.php:61
msgid "Export as CSV"
msgstr ""

#: app/views/admin/reports/overall_info_blocks.php:17
msgid "Total Members"
msgstr ""

#: app/views/admin/reports/overall_info_blocks.php:22
msgid "Total WP Users"
msgstr ""

#: app/views/admin/reports/overall_info_blocks.php:27
msgid "Active Free Members"
msgstr ""

#: app/views/admin/reports/overall_info_blocks.php:32
msgid "Active Paid Members"
msgstr ""

#: app/views/admin/reports/overall_info_blocks.php:36
msgid "Avg Mbr Lifetime Val"
msgstr ""

#. Translators: %1$s: the site title, %2$s: the week date range.
#: app/views/admin/reports/summary_email.php:26
msgid "Here's the summary report for %1$s for the week of %2$s. Enjoy!"
msgstr ""

#: app/views/admin/reports/summary_email.php:56
#: app/views/admin/reports/year_info_blocks.php:41
#: app/views/admin/widgets/admin_stats_widget.php:40
msgid "Total Income"
msgstr ""

#: app/views/admin/reports/summary_email.php:68
msgid "Recurring Income"
msgstr ""

#: app/views/admin/reports/summary_email.php:75
msgid "New Income"
msgstr ""

#: app/views/admin/reports/summary_email.php:85
msgid "We've also included a breakdown of your transactions below."
msgstr ""

#: app/views/admin/reports/summary_email.php:143
msgid "P.S. Want to unsubscribe from these emails? %1$sClick here to access the MemberPress settings%2$s where you can disable the Weekly Summary Email."
msgstr ""

#: app/views/admin/rules/access_row.php:8
msgid "OR"
msgstr ""

#: app/views/admin/rules/access_row.php:44
msgid "Remove Access Rule"
msgstr ""

#: app/views/admin/rules/drip_form.php:9
msgid "Enable Drip"
msgstr ""

#: app/views/admin/rules/drip_form.php:11
msgid "Drip this content"
msgstr ""

#: app/views/admin/rules/drip_form.php:14
#: app/views/admin/rules/drip_form.php:25
msgid "after"
msgstr ""

#: app/views/admin/rules/drip_form.php:20
msgid "Enable Expiration"
msgstr ""

#: app/views/admin/rules/drip_form.php:22
msgid "Expire this content"
msgstr ""

#: app/views/admin/rules/form.php:13
#: app/views/admin/rules/form.php:16
msgid "Protected Content"
msgstr ""

#: app/views/admin/rules/form.php:17
msgid "This selects the content on your site that will be protected by this rule. If a piece of content is selected by this rule it will be protected from non-logged in visitors and from logged-in users who don't meet the conditions you specify in the 'Access Rules' section below."
msgstr ""

#: app/views/admin/rules/form.php:27
#: app/views/admin/rules/form.php:30
msgid "Access Conditions"
msgstr ""

#: app/views/admin/rules/form.php:31
msgid "If %1$sany%2$s of these conditions match for a logged-in user then he / she will be granted access to the protected content for this rule -- otherwise he / she will be denied."
msgstr ""

#: app/views/admin/rules/form.php:34
msgid "Grant access to the protected content above if a logged-in user matches any of the following conditions:"
msgstr ""

#: app/views/admin/rules/form.php:48
msgid "Add Access Rule"
msgstr ""

#: app/views/admin/rules/form.php:52
msgid "Partial Content Codes"
msgstr ""

#: app/views/admin/rules/form.php:54
msgid "Examples:"
msgstr ""

#: app/views/admin/rules/form.php:56
#: app/views/admin/rules/form.php:58
msgid "Shortcode:"
msgstr ""

#: app/views/admin/rules/form.php:56
msgid "This content is shown only to authorized members. It is hidden from everyone else."
msgstr ""

#: app/views/admin/rules/form.php:58
msgid "This content is shown to everyone except authorized members."
msgstr ""

#: app/views/admin/rules/form.php:60
msgid "PHP Snippet:"
msgstr ""

#: app/views/admin/rules/form.php:62
msgid "Content to protect goes inbetween."
msgstr ""

#: app/views/admin/rules/form.php:66
msgid "Learn More:"
msgstr ""

#: app/views/admin/rules/form.php:68
msgid "shortcode documentation"
msgstr ""

#: app/views/admin/rules/form.php:69
msgid "Protecting partial content"
msgstr ""

#: app/views/admin/rules/form.php:78
#: app/views/admin/rules/form.php:85
msgid "Save Rule"
msgstr ""

#: app/views/admin/rules/form.php:79
#: app/views/admin/rules/form.php:86
msgid "Rule Saved"
msgstr ""

#: app/views/admin/rules/form.php:83
msgid "You cannot create rules until you have added at least 1 Membership."
msgstr ""

#: app/views/admin/rules/rules_meta_box.php:5
msgid "Access Rules Protected by:"
msgstr ""

#: app/views/admin/rules/rules_meta_box.php:6
msgid "These rules protect the current content."
msgstr ""

#: app/views/admin/rules/rules_meta_box.php:21
msgid "Memberships with Access:"
msgstr ""

#: app/views/admin/rules/rules_meta_box.php:22
msgid "Members who are active on any of these memberships will be able to access this content. This list is calculated from the Rules that protect this content."
msgstr ""

#: app/views/admin/rules/rules_meta_box.php:38
msgid "Members with Access:"
msgstr ""

#: app/views/admin/rules/rules_meta_box.php:39
msgid "Members below will be able to access this content. This list is calculated from the Rules that protect this content."
msgstr ""

#: app/views/admin/rules/unauth_meta_box.php:2
msgid "Unauthorized access handling for content protected by this rule"
msgstr ""

#: app/views/admin/rules/unauth_meta_box.php:3
msgid "Note: This overrides the global settings for unauthorized access handling in MemberPress Options for content protected by this rule."
msgstr ""

#: app/views/admin/rules/unauth_meta_box.php:6
msgid "Use modern PayWall"
msgstr ""

#. Translators: %1$s: open b tag, %2$s: close b tag.
#: app/views/admin/stripe_checkout_deprecated.php:9
msgid "The %1$sUse Stripe Checkout (Beta)%2$s option is deprecated, please go to the Payments tab and disable it."
msgstr ""

#: app/views/admin/subscriptions/edit.php:6
#: app/views/admin/subscriptions/row.php:73
msgid "Edit Subscription"
msgstr ""

#: app/views/admin/subscriptions/edit.php:20
msgid "Subscription ID:"
msgstr ""

#: app/views/admin/subscriptions/form.php:9
msgid "Subscription Number*:"
msgstr ""

#: app/views/admin/subscriptions/form.php:12
msgid "A unique subscription number for this subscription. Only edit this if you absolutely have to."
msgstr ""

#: app/views/admin/subscriptions/form.php:17
#: app/views/admin/transactions/trans_form.php:16
msgid "User*:"
msgstr ""

#: app/views/admin/subscriptions/form.php:20
msgid "The user for this subscription."
msgstr ""

#: app/views/admin/subscriptions/form.php:25
#: app/views/admin/transactions/trans_form.php:26
msgid "Membership*:"
msgstr ""

#: app/views/admin/subscriptions/form.php:36
#: app/views/admin/transactions/trans_form.php:37
msgid "The membership that was purchased"
msgstr ""

#: app/views/admin/subscriptions/form.php:41
#: app/views/admin/transactions/trans_form.php:42
msgid "Sub-Total*:"
msgstr ""

#: app/views/admin/subscriptions/form.php:45
#: app/views/admin/subscriptions/form.php:117
msgid "The sub-total (amount before tax) of this subscription"
msgstr ""

#: app/views/admin/subscriptions/form.php:50
msgid "Tax Amount:"
msgstr ""

#: app/views/admin/subscriptions/form.php:54
msgid "The amount of taxes for this subscription"
msgstr ""

#: app/views/admin/subscriptions/form.php:59
msgid "Tax Rate:"
msgstr ""

#: app/views/admin/subscriptions/form.php:63
#: app/views/admin/transactions/trans_form.php:64
msgid "The tax rate in percentage. (Ex: %s for 10%%)"
msgstr ""

#: app/views/admin/subscriptions/form.php:68
msgid "Status*:"
msgstr ""

#: app/views/admin/subscriptions/form.php:76
msgid "The current status of the subscription"
msgstr ""

#: app/views/admin/subscriptions/form.php:84
msgid "The payment method associated with this subscription."
msgstr ""

#: app/views/admin/subscriptions/form.php:89
#: app/views/admin/transactions/trans_form.php:98
msgid "Created (UTC/GMT):"
msgstr ""

#: app/views/admin/subscriptions/form.php:92
msgid "The date that this subscription was created on. This field is displayed in UTC/GMT."
msgstr ""

#: app/views/admin/subscriptions/form.php:97
msgid "Trial:"
msgstr ""

#: app/views/admin/subscriptions/form.php:100
msgid "The trial period for this subscription"
msgstr ""

#: app/views/admin/subscriptions/form.php:105
msgid "Trial Days:"
msgstr ""

#: app/views/admin/subscriptions/form.php:108
msgid "The number of days for this trial period"
msgstr ""

#: app/views/admin/subscriptions/list.php:5
#: app/views/admin/transactions/list.php:8
msgid "for"
msgstr ""

#: app/views/admin/subscriptions/new.php:6
msgid "New Subscription"
msgstr ""

#: app/views/admin/subscriptions/row.php:46
msgid "Manually add a transaction to this subscription"
msgstr ""

#: app/views/admin/subscriptions/row.php:46
msgid "Add Txn"
msgstr ""

#: app/views/admin/subscriptions/row.php:47
msgid "View related transactions"
msgstr ""

#: app/views/admin/subscriptions/row.php:47
msgid "View Txns"
msgstr ""

#: app/views/admin/subscriptions/row.php:63
msgid "Pause Subscription"
msgstr ""

#: app/views/admin/subscriptions/row.php:66
msgid "Resume Subscription"
msgstr ""

#: app/views/admin/subscriptions/row.php:79
msgid "Cancel Subscription"
msgstr ""

#: app/views/admin/subscriptions/row.php:84
msgid "Delete Subscription"
msgstr ""

#: app/views/admin/subscriptions/row.php:99
msgid "Show related transaction"
msgstr ""

#: app/views/admin/subscriptions/row.php:108
msgid "Show related transactions"
msgstr ""

#: app/views/admin/subscriptions/row.php:220
msgid "Editing Subscription Status"
msgstr ""

#: app/views/admin/subscriptions/row.php:221
msgid "Modifying the Auto Rebill status here will change the status of the Subscription ONLY on your site, not at the Gateway itself. To cancel a Subscription, either you or the member must click on Cancel."
msgstr ""

#: app/views/admin/subscriptions/row.php:235
msgid "Saving..."
msgstr ""

#: app/views/admin/subscriptions/search_box.php:12
#: app/views/admin/transactions/search_box.php:12
msgid "All Statuses"
msgstr ""

#: app/views/admin/subscriptions/search_box.php:16
msgid "Cancelled"
msgstr ""

#: app/views/admin/subscriptions/search_box.php:20
#: app/views/admin/transactions/search_box.php:20
msgid "All Gateways"
msgstr ""

#: app/views/admin/subscriptions/tabs.php:4
msgid "Recurring (%d)"
msgstr ""

#: app/views/admin/subscriptions/tabs.php:7
msgid "Non-Recurring (%d)"
msgstr ""

#: app/views/admin/support/view.php:6
msgid "Frequently Asked Questions"
msgstr ""

#: app/views/admin/support/view.php:10
msgid "How To"
msgstr ""

#: app/views/admin/support/view.php:11
msgid "Frequently \"How To...\" questions."
msgstr ""

#: app/views/admin/support/view.php:15
msgid "Getting Started"
msgstr ""

#: app/views/admin/support/view.php:19
msgid "Installation and Upgrading"
msgstr ""

#: app/views/admin/support/view.php:20
msgid "How to install, activate, and upgrade MemberPress."
msgstr ""

#: app/views/admin/support/view.php:25
msgid "Configuring Settings"
msgstr ""

#: app/views/admin/support/view.php:26
msgid "How to configure your settings in MemberPress."
msgstr ""

#: app/views/admin/support/view.php:31
msgid "Supported Payment Gateways"
msgstr ""

#: app/views/admin/support/view.php:32
msgid "Pick your payment gateway and get it setup."
msgstr ""

#: app/views/admin/support/view.php:37
msgid "Migrating and Importing/Exporting"
msgstr ""

#: app/views/admin/support/view.php:38
msgid "Migrate from a different membership system, or from a different server."
msgstr ""

#: app/views/admin/support/view.php:43
msgid "Using MemberPress"
msgstr ""

#: app/views/admin/support/view.php:47
msgid "Creating and Managing Memberships"
msgstr ""

#: app/views/admin/support/view.php:48
msgid "How to configure and use our Memberships."
msgstr ""

#: app/views/admin/support/view.php:53
msgid "Protecting Content"
msgstr ""

#: app/views/admin/support/view.php:54
msgid "How to use rules to protect content on your site."
msgstr ""

#: app/views/admin/support/view.php:59
msgid "Creating and Managing Groups"
msgstr ""

#: app/views/admin/support/view.php:60
msgid "Configure groups for easy pricing pages and upgrade/downgrade paths."
msgstr ""

#: app/views/admin/support/view.php:65
msgid "Front End Pages"
msgstr ""

#: app/views/admin/support/view.php:66
msgid "How to configure and use Login, Account, Registration, and other front end pages."
msgstr ""

#: app/views/admin/support/view.php:71
msgid "Shortcodes, Widgets, and Email Parameters"
msgstr ""

#: app/views/admin/support/view.php:72
msgid "Customize emails and pages to match your site."
msgstr ""

#: app/views/admin/support/view.php:77
msgid "Managing Members and their Subscriptions"
msgstr ""

#: app/views/admin/support/view.php:78
msgid "Managing Members(users) and their subscriptions to memberships."
msgstr ""

#: app/views/admin/support/view.php:83
msgid "Creating and Managing Coupons"
msgstr ""

#: app/views/admin/support/view.php:84
msgid "Create and manage coupons for discounts and promotions."
msgstr ""

#: app/views/admin/support/view.php:89
msgid "Creating and Managing Reminder Emails"
msgstr ""

#: app/views/admin/support/view.php:90
msgid "Create and use reminder emails to keep your members informed."
msgstr ""

#: app/views/admin/support/view.php:95
msgid "Viewing Reports"
msgstr ""

#: app/views/admin/support/view.php:96
msgid "Use MemberPress reports to keep a finger on the pulse of your site."
msgstr ""

#: app/views/admin/support/view.php:101
msgid "Extensions"
msgstr ""

#: app/views/admin/support/view.php:105
msgid "Add-Ons"
msgstr ""

#: app/views/admin/support/view.php:106
msgid "MemberPress created and supported extensions."
msgstr ""

#: app/views/admin/support/view.php:111
msgid "Email Marketing"
msgstr ""

#: app/views/admin/support/view.php:112
msgid "Integrate MemberPress with your email marketing platform."
msgstr ""

#: app/views/admin/support/view.php:117
msgid "Third-Party Integrations"
msgstr ""

#: app/views/admin/support/view.php:118
msgid "Recommend integrations built and supported by others."
msgstr ""

#: app/views/admin/support/view.php:124
msgid "Offer your courses through MemberPress without our official add-on."
msgstr ""

#: app/views/admin/support/view.php:129
msgid "MemberPress Downloads"
msgstr ""

#: app/views/admin/support/view.php:130
msgid "Protect files, images, and more with the official MemberPressDownloads add-on."
msgstr ""

#: app/views/admin/support/view.php:135
msgid "Advanced Topics"
msgstr ""

#: app/views/admin/support/view.php:139
msgid "Privacy (GDPR)"
msgstr ""

#: app/views/admin/support/view.php:140
msgid "What data MemberPress stores and how it works with GDPR."
msgstr ""

#: app/views/admin/support/view.php:145
msgid "MemberPress Developer Tools"
msgstr ""

#: app/views/admin/support/view.php:146
msgid "MemberPress offers webhook support and a REST API for developers."
msgstr ""

#: app/views/admin/support/view.php:151
msgid "Other Tools/Suggestions"
msgstr ""

#: app/views/admin/support/view.php:152
msgid "Advanced to ways to further customize and configure MemberPress."
msgstr ""

#: app/views/admin/support/view.php:157
msgid "Developer Resources"
msgstr ""

#: app/views/admin/support/view.php:158
msgid "Resources about the inner workings of MemberPress for developers."
msgstr ""

#: app/views/admin/table_controls.php:13
msgid "Search"
msgstr ""

#: app/views/admin/table_controls.php:15
msgid "by Field"
msgstr ""

#: app/views/admin/table_controls.php:20
msgid "Any (Slow)"
msgstr ""

#: app/views/admin/taxes/avalara_options.php:6
msgid "Enable Avalara TaxRates"
msgstr ""

#: app/views/admin/taxes/avalara_options.php:9
msgid "Get US Tax Rates from Avalara"
msgstr ""

#: app/views/admin/taxes/avalara_options.php:10
msgid "Avalara has a great Tax Rate API for automatically finding the correct tax rate for your customers in the US. Check this option to pull in rates automatically from Avalara on each transaction.<br/><br/>NOTE: This will override any tax rates for the US you've imported via CSV."
msgstr ""

#: app/views/admin/taxes/avalara_options.php:26
#: app/views/admin/taxes/avalara_options.php:29
msgid "Avalara Account ID"
msgstr ""

#: app/views/admin/taxes/avalara_options.php:30
msgid "You can create a free account by registering for Avalara's Tax Rate API at taxratesapi.avalara.com."
msgstr ""

#: app/views/admin/taxes/avalara_options.php:39
#: app/views/admin/taxes/avalara_options.php:42
msgid "Avalara License Key"
msgstr ""

#: app/views/admin/taxes/avalara_options.php:43
msgid "You can get a free License Key by registering for Avalara's Tax Rate API at taxratesapi.avalara.com."
msgstr ""

#: app/views/admin/taxes/options.php:12
#: app/views/admin/taxes/options.php:16
msgid "Enable Tax Calculations"
msgstr ""

#: app/views/admin/taxes/options.php:17
msgid "Enable tax calculations on transactions on all memberships other than those where tax calculations have been specifically disabled."
msgstr ""

#: app/views/admin/taxes/options.php:29
msgid "Tax Options"
msgstr ""

#: app/views/admin/taxes/options.php:34
msgid "Prices Entered With Tax"
msgstr ""

#: app/views/admin/taxes/options.php:38
msgid "Are membership prices exclusive or inclusive tax?"
msgstr ""

#: app/views/admin/taxes/options.php:39
msgid "<strong>Exclusive:</strong> The customer will pay the price of the membership plus the cost of the tax.</br></br><strong>Inclusive:</strong> The customer will pay just the price of the membership but will include the tax."
msgstr ""

#: app/views/admin/taxes/options.php:45
msgid "Prices entered are exclusive of tax"
msgstr ""

#: app/views/admin/taxes/options.php:46
msgid "Prices entered are inclusive of tax"
msgstr ""

#: app/views/admin/taxes/options.php:52
msgid "Calculate Tax Based On"
msgstr ""

#: app/views/admin/taxes/options.php:56
msgid "What location should taxes be based on?"
msgstr ""

#: app/views/admin/taxes/options.php:57
msgid "This option will determine whether tax should be calculated based on the address that the customer enters or the address you entered on the \"Info\" tab here in MemberPress Options."
msgstr ""

#: app/views/admin/taxes/options.php:63
msgid "Customer Address"
msgstr ""

#: app/views/admin/taxes/options.php:64
#: app/views/admin/taxes/options.php:82
msgid "Merchant Address"
msgstr ""

#: app/views/admin/taxes/options.php:70
msgid "Default Address"
msgstr ""

#: app/views/admin/taxes/options.php:74
msgid "What default customer address should be used?"
msgstr ""

#: app/views/admin/taxes/options.php:75
msgid "If there is no address set for the customer then MemberPress can either use the merchant address to calculate the tax or use no address at all which would result in no tax being applied to the sale."
msgstr ""

#: app/views/admin/taxes/options.php:81
msgid "No Address"
msgstr ""

#: app/views/admin/taxes/options.php:90
msgid "Tax Rates"
msgstr ""

#: app/views/admin/taxes/options.php:98
msgid "Custom Tax Rates"
msgstr ""

#: app/views/admin/taxes/options.php:105
msgid "State"
msgstr ""

#: app/views/admin/taxes/options.php:106
msgid "Postcode"
msgstr ""

#: app/views/admin/taxes/options.php:107
msgid "City"
msgstr ""

#: app/views/admin/taxes/options.php:108
msgid "Rate %"
msgstr ""

#: app/views/admin/taxes/options.php:109
msgid "Desc"
msgstr ""

#: app/views/admin/taxes/options.php:110
msgid "Priority"
msgstr ""

#: app/views/admin/taxes/options.php:148
msgid "Export Tax Rates"
msgstr ""

#: app/views/admin/taxes/options.php:149
msgid "Clear Tax Rates"
msgstr ""

#: app/views/admin/taxes/options.php:153
msgid "No custom tax rates have been set. To add some, upload a csv file."
msgstr ""

#: app/views/admin/taxes/options.php:160
#: app/views/admin/taxes/options.php:164
msgid "Upload Tax Rates"
msgstr ""

#: app/views/admin/taxes/options.php:165
msgid "Upload Tax Rates via a CSV file. Use this to select a csv file ... then to upload, just hit the \"Update Options\" button."
msgstr ""

#: app/views/admin/taxes/stripe_tax_options.php:6
#: app/views/admin/taxes/stripe_tax_options.php:10
msgid "Enable Stripe Tax"
msgstr ""

#: app/views/admin/taxes/stripe_tax_options.php:11
msgid "Use Stripe Tax for automatic tax calculations."
msgstr ""

#: app/views/admin/taxes/stripe_tax_options.php:18
msgid "You must add a Stripe payment method on the Payments tab and connect to Stripe to use Stripe Tax."
msgstr ""

#: app/views/admin/taxes/stripe_tax_options.php:33
#: app/views/admin/taxes/stripe_tax_options.php:37
msgid "Stripe Tax Payment Method"
msgstr ""

#: app/views/admin/taxes/stripe_tax_options.php:38
msgid "Choose which Stripe payment method to use for Stripe Tax."
msgstr ""

#: app/views/admin/taxes/stripe_tax_options.php:45
msgid "Please select"
msgstr ""

#: app/views/admin/taxes/stripe_tax_options.php:53
msgid "Stripe Tax is not active on that Stripe account"
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:6
msgid "Enable TaxJar"
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:9
msgid "Get US Tax Rates from TaxJar"
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:10
msgid "TaxJar automate your sales tax calculations, reporting, and filings in minutes.<br/><br/>NOTE: This will override any tax rates for the US you've imported via CSV."
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:26
#: app/views/admin/taxes/taxjar_options.php:29
msgid "TaxJar API Key (Live)"
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:30
msgid "Live API key for use in production environments."
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:39
#: app/views/admin/taxes/taxjar_options.php:42
msgid "TaxJar API Key (Sandbox)"
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:43
msgid "Sandbox API key for use in testing and staging environments."
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:52
#: app/views/admin/taxes/taxjar_options.php:55
msgid "Enable TaxJar Sandbox"
msgstr ""

#: app/views/admin/taxes/taxjar_options.php:56
msgid "Enable a sandbox for use in testing and staging environments."
msgstr ""

#: app/views/admin/taxes/vat_options.php:7
msgid "Enable VAT"
msgstr ""

#: app/views/admin/taxes/vat_options.php:10
msgid "Enable VAT Tax Calculations"
msgstr ""

#: app/views/admin/taxes/vat_options.php:11
msgid "When this is checked then MemberPress will calculate VAT taxes for any customers who are located in the European Union."
msgstr ""

#: app/views/admin/taxes/vat_options.php:26
#: app/views/admin/taxes/vat_options.php:29
msgid "Merchant VAT Country"
msgstr ""

#: app/views/admin/taxes/vat_options.php:30
msgid "This is the country of the VAT MOSS your business is registered with."
msgstr ""

#: app/views/admin/taxes/vat_options.php:43
#: app/views/admin/taxes/vat_options.php:46
msgid "Tax all EU Businesses"
msgstr ""

#: app/views/admin/taxes/vat_options.php:47
msgid "When this option is checked all EU Businesses will be taxed--even if they have a valid VAT number."
msgstr ""

#: app/views/admin/taxes/vat_options.php:56
#: app/views/admin/taxes/vat_options.php:59
msgid "Remove tax from valid Businesses"
msgstr ""

#: app/views/admin/taxes/vat_options.php:60
msgid "When enabled, and prices are \"inclusive\" of tax, then valid EU Businesses will have their tax removed and pay only the NET price of the membership. Invoice will show 0 tax by default."
msgstr ""

#: app/views/admin/taxes/vat_options.php:69
#: app/views/admin/taxes/vat_options.php:72
msgid "Show negative tax on Invoice"
msgstr ""

#: app/views/admin/taxes/vat_options.php:73
msgid "When enabled the tax which is removed for valid EU Businesses will show as a negative amount on the invoice."
msgstr ""

#: app/views/admin/taxes/vat_options.php:82
#: app/views/admin/taxes/vat_options.php:85
msgid "Disable VAT VIES database lookup"
msgstr ""

#: app/views/admin/taxes/vat_options.php:86
msgid "When this option is checked, the VAT number will not be validated by VIES online service."
msgstr ""

#: app/views/admin/taxes/vat_profile_fields.php:7
msgid "VAT Customer Type"
msgstr ""

#: app/views/admin/taxes/vat_profile_fields.php:16
#: app/views/admin/taxes/vat_profile_fields.php:27
msgid "Consumer"
msgstr ""

#: app/views/admin/taxes/vat_profile_fields.php:20
#: app/views/admin/taxes/vat_profile_fields.php:29
msgid "Business"
msgstr ""

#: app/views/admin/taxes/vat_profile_fields.php:51
msgid "Not Applicable"
msgstr ""

#: app/views/admin/taxes/vat_profile_fields.php:53
msgid "Not Set"
msgstr ""

#: app/views/admin/transactions/edit_trans.php:6
msgid "Edit Transaction"
msgstr ""

#: app/views/admin/transactions/edit_trans.php:15
msgid "Deleted Gateway"
msgstr ""

#: app/views/admin/transactions/edit_trans.php:27
msgid "Transaction ID:"
msgstr ""

#: app/views/admin/transactions/list.php:21
msgid "All Transactions for Coupon"
msgstr ""

#: app/views/admin/transactions/new_trans.php:6
msgid "New Transaction"
msgstr ""

#: app/views/admin/transactions/row.php:74
#: app/views/admin/transactions/row.php:76
msgid "Edit transaction"
msgstr ""

#: app/views/admin/transactions/row.php:79
msgid "Send Receipt"
msgstr ""

#: app/views/admin/transactions/row.php:81
msgid "Send Welcome"
msgstr ""

#: app/views/admin/transactions/row.php:103
msgid "Refund Transaction"
msgstr ""

#: app/views/admin/transactions/row.php:103
msgid "Refund"
msgstr ""

#: app/views/admin/transactions/row.php:110
msgid "Refund Transaction and Cancel Subscription"
msgstr ""

#: app/views/admin/transactions/row.php:110
msgid "Refund & Cancel"
msgstr ""

#: app/views/admin/transactions/row.php:116
msgid "Delete Transaction"
msgstr ""

#: app/views/admin/transactions/row.php:125
msgid "View Subscription"
msgstr ""

#: app/views/admin/transactions/row.php:154
msgid "Change transaction's status"
msgstr ""

#: app/views/admin/transactions/row.php:160
msgid "Editing Transaction Status"
msgstr ""

#: app/views/admin/transactions/row.php:161
msgid "Changing the status here will ONLY change the status on your site, not at the Gateway itself. To cancel a Transaction either you or the member should click the Cancel link next to the Subscription. You must use your Payment Gateway's web interface to refund a transaction."
msgstr ""

#: app/views/admin/transactions/row.php:174
msgid "Saving ..."
msgstr ""

#: app/views/admin/transactions/trans_form.php:6
msgid "Transaction Number*:"
msgstr ""

#: app/views/admin/transactions/trans_form.php:9
msgid "A unique ID for this Transaction. Only edit this if you absolutely have to."
msgstr ""

#: app/views/admin/transactions/trans_form.php:21
msgid "The user who made this transaction."
msgstr ""

#: app/views/admin/transactions/trans_form.php:46
msgid "The sub-total (amount before tax) of this transaction"
msgstr ""

#: app/views/admin/transactions/trans_form.php:51
msgid "Tax Amount*:"
msgstr ""

#: app/views/admin/transactions/trans_form.php:55
msgid "The amount of taxes for this transaction"
msgstr ""

#: app/views/admin/transactions/trans_form.php:60
msgid "Tax Rate*:"
msgstr ""

#: app/views/admin/transactions/trans_form.php:77
msgid "The current status of the transaction"
msgstr ""

#: app/views/admin/transactions/trans_form.php:85
msgid "The payment method associated with the transaction."
msgstr ""

#: app/views/admin/transactions/trans_form.php:90
msgid "Subscription:"
msgstr ""

#: app/views/admin/transactions/trans_form.php:93
msgid "The optional subscription to associate this transaction with."
msgstr ""

#: app/views/admin/transactions/trans_form.php:101
msgid "The date that the transaction was created on. This field is displayed in UTC/GMT."
msgstr ""

#: app/views/admin/transactions/trans_form.php:106
msgid "Expiration Date (UTC/GMT):"
msgstr ""

#: app/views/admin/transactions/trans_form.php:109
msgid "The date that the transaction will expire. This is used to determine how long the user will have access to the membership until another transaction needs to be made. This field is displayed in UTC/GMT"
msgstr ""

#: app/views/admin/transactions/trans_form.php:110
msgid "<b>Note:</b> Blank indicates a <b>lifetime</b> expiration."
msgstr ""

#: app/views/admin/unauthorized.php:5
msgid "Nope"
msgstr ""

#: app/views/admin/update/activation_warning.php:9
msgid "Error with MEMBERPRESS_LICENSE_KEY: %s"
msgstr ""

#: app/views/admin/update/activation_warning.php:22
msgid "<b>MemberPress doesn't have a valid license key installed.</b> Go to the MemberPress %1$ssettings page%2$s to activate your license or go to %3$smemberpress.com%4$s to get one."
msgstr ""

#: app/views/admin/usage/option.php:5
msgid "Usage"
msgstr ""

#: app/views/admin/usage/option.php:11
#: app/views/admin/usage/option.php:16
msgid "Help Improve MemberPress by Sending Usage Data"
msgstr ""

#: app/views/admin/usage/option.php:17
msgid "In order to help us improve MemberPress you can allow MemberPress to send usage data back to our developers. Any data that is sent to us will help us to fix issues, identify new features and generally improve MemberPress."
msgstr ""

#: app/views/admin/usage/option.php:32
#: app/views/admin/usage/option.php:36
msgid "Hide Announcements"
msgstr ""

#: app/views/admin/usage/option.php:37
msgid "Enabling this option will hide announcements/notifications from MemberPress."
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:5
msgid "Membership Information"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:17
msgid "User has consented to the Privacy Policy"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:19
msgid "User has NOT consented to the Privacy Policy"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:28
msgid "Terms of Service"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:33
msgid "User has consented to the Terms of Service"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:35
msgid "User has NOT consented to the Terms of Service"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:43
msgid "Signup Location"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:55
msgid "Detected on user's initial signup"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:65
msgid "View Member's Transactions"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:70
msgid "View Member's Subscriptions"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:78
msgid "Resend MemberPress Welcome Email"
msgstr ""

#: app/views/admin/users/extra_profile_fields.php:85
msgid "Custom MemberPress Account Message"
msgstr ""

#: app/views/admin/users/login_page_meta_box.php:7
msgid "Manually place the login form on the page"
msgstr ""

#: app/views/admin/users/login_page_meta_box.php:11
msgid "Manually place Login form"
msgstr ""

#: app/views/admin/users/login_page_meta_box.php:12
msgid "By default MemberPress will append the login form to the end of the Login page. If you would like to show it in a different place on the Login page check this box then copy and paste the shortcode that appears below where you want it to appear on the page. The [mepr-login-form] shortcode can be used on any page or post on your site to display a login form."
msgstr ""

#: app/views/admin/widgets/admin_stats_widget.php:7
msgid "Your 7-Day membership activity:"
msgstr ""

#: app/views/admin/widgets/admin_stats_widget.php:65
msgid "View More MemberPress Reports"
msgstr ""

#: app/views/checkout/form.php:23
#: app/views/checkout/spc_form.php:36
msgid "There are no active payment methods. Please contact the site administrator."
msgstr ""

#: app/views/checkout/form.php:30
#: app/views/checkout/spc_form.php:23
msgctxt "ui"
msgid "Price:"
msgstr ""

#: app/views/checkout/form.php:30
#: app/views/checkout/invoice.php:13
#: app/views/checkout/invoice_order_bumps.php:7
#: app/views/checkout/spc_form.php:23
#: app/views/emails/admin_cancelled_sub.php:15
#: app/views/emails/admin_downgraded_sub.php:15
#: app/views/emails/admin_new_sub.php:15
#: app/views/emails/admin_resumed_sub.php:15
#: app/views/emails/admin_suspended_sub.php:15
#: app/views/emails/admin_upgraded_sub.php:15
#: app/views/emails/user_cancelled_sub.php:12
#: app/views/emails/user_downgraded_sub.php:12
#: app/views/emails/user_resumed_sub.php:12
#: app/views/emails/user_stripe_invoice.php:15
#: app/views/emails/user_suspended_sub.php:12
#: app/views/emails/user_upgraded_sub.php:12
#: app/views/readylaunch/checkout/invoice.php:15
#: app/views/readylaunch/checkout/invoice_order_bumps.php:15
msgctxt "ui"
msgid "Terms:"
msgstr ""

#: app/views/checkout/form.php:84
#: app/views/checkout/spc_form.php:85
#: app/views/readylaunch/checkout/form.php:100
msgctxt "ui"
msgid "Username:*"
msgstr ""

#: app/views/checkout/form.php:85
#: app/views/checkout/spc_form.php:86
#: app/views/readylaunch/checkout/form.php:101
msgctxt "ui"
msgid "Invalid Username"
msgstr ""

#: app/views/checkout/form.php:104
#: app/views/checkout/spc_form.php:105
#: app/views/readylaunch/checkout/form.php:123
msgctxt "ui"
msgid "Password:*"
msgstr ""

#: app/views/checkout/form.php:105
#: app/views/checkout/spc_form.php:106
#: app/views/readylaunch/checkout/form.php:124
msgctxt "ui"
msgid "Invalid Password"
msgstr ""

#: app/views/checkout/form.php:116
#: app/views/checkout/spc_form.php:117
#: app/views/readylaunch/checkout/form.php:140
msgctxt "ui"
msgid "Password Confirmation:*"
msgstr ""

#: app/views/checkout/form.php:117
#: app/views/checkout/spc_form.php:118
#: app/views/readylaunch/checkout/form.php:141
msgctxt "ui"
msgid "Password Confirmation Doesn't Match"
msgstr ""

#: app/views/checkout/form.php:143
#: app/views/checkout/spc_form.php:144
#: app/views/readylaunch/checkout/form.php:271
msgctxt "ui"
msgid "Coupon Code:"
msgstr ""

#: app/views/checkout/form.php:145
#: app/views/checkout/form.php:195
#: app/views/checkout/spc_form.php:146
#: app/views/checkout/spc_form.php:162
#: app/views/checkout/spc_form.php:192
#: app/views/checkout/spc_form.php:231
#: app/views/readylaunch/checkout/form.php:190
#: app/views/readylaunch/checkout/form.php:238
#: app/views/readylaunch/checkout/form.php:265
#: app/views/readylaunch/checkout/form.php:287
msgctxt "ui"
msgid "Loading icon"
msgstr ""

#: app/views/checkout/form.php:147
#: app/views/checkout/spc_form.php:148
#: app/views/readylaunch/checkout/form.php:267
msgctxt "ui"
msgid "Invalid Coupon"
msgstr ""

#: app/views/checkout/form.php:148
#: app/views/checkout/spc_form.php:149
#: app/views/readylaunch/checkout/form.php:268
msgctxt "ui"
msgid "Coupon applied successfully"
msgstr ""

#: app/views/checkout/form.php:189
#: app/views/checkout/spc_form.php:225
#: app/views/readylaunch/checkout/form.php:231
msgctxt "ui"
msgid "No val"
msgstr ""

#: app/views/checkout/invoice.php:20
#: app/views/checkout/invoice_order_bumps.php:14
msgctxt "ui"
msgid "Description"
msgstr ""

#: app/views/checkout/invoice.php:22
#: app/views/checkout/invoice_order_bumps.php:16
msgctxt "ui"
msgid "Quantity"
msgstr ""

#: app/views/checkout/invoice.php:24
#: app/views/checkout/invoice_order_bumps.php:18
msgctxt "ui"
msgid "Amount"
msgstr ""

#: app/views/checkout/invoice.php:59
#: app/views/checkout/invoice_order_bumps.php:47
#: app/views/readylaunch/checkout/invoice.php:67
#: app/views/readylaunch/checkout/invoice_order_bumps.php:64
msgctxt "ui"
msgid "Sub-Total"
msgstr ""

#: app/views/checkout/MeprAuthorizeGateway/payment_gateway_fields.php:5
msgid "First Name on Card"
msgstr ""

#: app/views/checkout/MeprAuthorizeGateway/payment_gateway_fields.php:10
msgid "Last Name on Card"
msgstr ""

#: app/views/checkout/MeprPayPalCommerceGateway/payment_form.php:27
msgid "JavaScript is disabled in your browser. You will not be able to complete your purchase until you either enable JavaScript in your browser, or switch to a browser that supports it."
msgstr ""

#: app/views/checkout/payment_form.php:8
msgctxt "ui"
msgid "Credit Card Number"
msgstr ""

#: app/views/checkout/payment_form.php:9
msgctxt "ui"
msgid "Invalid Credit Card Number"
msgstr ""

#: app/views/checkout/payment_form.php:17
msgctxt "ui"
msgid "Expiration"
msgstr ""

#: app/views/checkout/payment_form.php:18
msgctxt "ui"
msgid "Invalid Expiration"
msgstr ""

#: app/views/checkout/payment_form.php:20
msgctxt "ui"
msgid "mm/yy"
msgstr ""

#: app/views/checkout/payment_form.php:26
msgctxt "ui"
msgid "CVC"
msgstr ""

#: app/views/checkout/payment_form.php:27
msgctxt "ui"
msgid "Invalid CVC Code"
msgstr ""

#: app/views/checkout/signup_row.php:10
msgctxt "ui"
msgid "%s is Required"
msgstr ""

#: app/views/checkout/signup_row.php:10
msgctxt "ui"
msgid "%s is not valid"
msgstr ""

#: app/views/checkout/spc_form.php:172
#: app/views/readylaunch/checkout/form.php:167
msgctxt "ui"
msgid "Select Payment Method"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:5
msgctxt "ui"
msgid "Subscription Cancelled"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:6
#: app/views/emails/admin_downgraded_sub.php:6
#: app/views/emails/admin_new_sub.php:6
#: app/views/emails/admin_resumed_sub.php:6
#: app/views/emails/admin_suspended_sub.php:6
#: app/views/emails/admin_upgraded_sub.php:6
msgctxt "ui"
msgid "{$subscr_num} &ndash; {$user_full_name}"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:9
msgctxt "ui"
msgid "A subscription was just cancelled on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:11
#: app/views/emails/admin_cc_expires_reminder.php:16
#: app/views/emails/admin_downgraded_sub.php:11
#: app/views/emails/admin_failed_txn.php:16
#: app/views/emails/admin_member_signup_reminder.php:16
#: app/views/emails/admin_new_one_off.php:11
#: app/views/emails/admin_new_sub.php:11
#: app/views/emails/admin_refunded_txn.php:16
#: app/views/emails/admin_resumed_sub.php:11
#: app/views/emails/admin_signup.php:12
#: app/views/emails/admin_signup_abandoned_reminder.php:13
#: app/views/emails/admin_sub_expires_reminder.php:16
#: app/views/emails/admin_sub_renews_reminder.php:16
#: app/views/emails/admin_suspended_sub.php:11
#: app/views/emails/admin_upgraded_sub.php:11
#: app/views/readylaunch/account/home.php:108
#: app/views/readylaunch/checkout/form.php:42
msgctxt "ui"
msgid "Name:"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:12
#: app/views/emails/admin_cc_expires_reminder.php:17
#: app/views/emails/admin_downgraded_sub.php:12
#: app/views/emails/admin_failed_txn.php:17
#: app/views/emails/admin_member_signup_reminder.php:17
#: app/views/emails/admin_new_one_off.php:12
#: app/views/emails/admin_new_sub.php:12
#: app/views/emails/admin_refunded_txn.php:17
#: app/views/emails/admin_resumed_sub.php:12
#: app/views/emails/admin_signup.php:13
#: app/views/emails/admin_signup_abandoned_reminder.php:14
#: app/views/emails/admin_sub_expires_reminder.php:17
#: app/views/emails/admin_sub_renews_reminder.php:17
#: app/views/emails/admin_suspended_sub.php:12
#: app/views/emails/admin_upgraded_sub.php:12
#: app/views/emails/user_cancelled_sub.php:16
#: app/views/emails/user_downgraded_sub.php:16
#: app/views/emails/user_failed_txn.php:17
#: app/views/emails/user_refunded_txn.php:17
#: app/views/emails/user_resumed_sub.php:16
#: app/views/emails/user_stripe_invoice.php:18
#: app/views/emails/user_suspended_sub.php:16
#: app/views/emails/user_upgraded_sub.php:16
msgctxt "ui"
msgid "Email:"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:13
#: app/views/emails/admin_cc_expires_reminder.php:18
#: app/views/emails/admin_downgraded_sub.php:13
#: app/views/emails/admin_failed_txn.php:18
#: app/views/emails/admin_member_signup_reminder.php:18
#: app/views/emails/admin_new_one_off.php:13
#: app/views/emails/admin_new_sub.php:13
#: app/views/emails/admin_refunded_txn.php:18
#: app/views/emails/admin_resumed_sub.php:13
#: app/views/emails/admin_signup.php:14
#: app/views/emails/admin_signup_abandoned_reminder.php:15
#: app/views/emails/admin_sub_expires_reminder.php:18
#: app/views/emails/admin_sub_renews_reminder.php:18
#: app/views/emails/admin_suspended_sub.php:13
#: app/views/emails/admin_upgraded_sub.php:13
#: app/views/emails/user_cancelled_sub.php:17
#: app/views/emails/user_downgraded_sub.php:17
#: app/views/emails/user_failed_txn.php:18
#: app/views/emails/user_refunded_txn.php:18
#: app/views/emails/user_resumed_sub.php:17
#: app/views/emails/user_stripe_invoice.php:19
#: app/views/emails/user_suspended_sub.php:17
#: app/views/emails/user_upgraded_sub.php:17
msgctxt "ui"
msgid "Login:"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:14
#: app/views/emails/admin_cc_expires_reminder.php:12
#: app/views/emails/admin_cc_expiring.php:12
#: app/views/emails/admin_downgraded_sub.php:14
#: app/views/emails/admin_member_signup_reminder.php:12
#: app/views/emails/admin_new_sub.php:14
#: app/views/emails/admin_resumed_sub.php:14
#: app/views/emails/admin_sub_expires_reminder.php:12
#: app/views/emails/admin_sub_renews_reminder.php:12
#: app/views/emails/admin_suspended_sub.php:14
#: app/views/emails/admin_upgraded_sub.php:14
#: app/views/emails/user_cancelled_sub.php:13
#: app/views/emails/user_cc_expiring.php:13
#: app/views/emails/user_downgraded_sub.php:13
#: app/views/emails/user_resumed_sub.php:13
#: app/views/emails/user_stripe_invoice.php:16
#: app/views/emails/user_suspended_sub.php:13
#: app/views/emails/user_upgraded_sub.php:13
msgctxt "ui"
msgid "Subscription:"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:16
#: app/views/emails/admin_downgraded_sub.php:16
#: app/views/emails/admin_new_sub.php:16
#: app/views/emails/admin_resumed_sub.php:16
#: app/views/emails/admin_suspended_sub.php:16
#: app/views/emails/admin_upgraded_sub.php:16
#: app/views/emails/user_cancelled_sub.php:14
#: app/views/emails/user_downgraded_sub.php:14
#: app/views/emails/user_resumed_sub.php:14
#: app/views/emails/user_stripe_invoice.php:17
#: app/views/emails/user_suspended_sub.php:14
#: app/views/emails/user_upgraded_sub.php:14
msgctxt "ui"
msgid "Started:"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:17
msgctxt "ui"
msgid "Auto Rebill:"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:17
#: app/views/emails/user_cancelled_sub.php:15
msgctxt "ui"
msgid "Stopped"
msgstr ""

#: app/views/emails/admin_cancelled_sub.php:18
#: app/views/emails/admin_cc_expiring.php:17
#: app/views/emails/admin_downgraded_sub.php:17
#: app/views/emails/admin_failed_txn.php:15
#: app/views/emails/admin_new_one_off.php:16
#: app/views/emails/admin_new_sub.php:18
#: app/views/emails/admin_receipt.php:14
#: app/views/emails/admin_refunded_txn.php:15
#: app/views/emails/admin_resumed_sub.php:18
#: app/views/emails/admin_suspended_sub.php:18
#: app/views/emails/admin_upgraded_sub.php:17
msgctxt "ui"
msgid "Payment System:"
msgstr ""

#: app/views/emails/admin_cc_expires_reminder.php:5
#: app/views/emails/admin_member_signup_reminder.php:5
#: app/views/emails/admin_signup_abandoned_reminder.php:5
#: app/views/emails/admin_sub_expires_reminder.php:5
#: app/views/emails/admin_sub_renews_reminder.php:5
msgctxt "ui"
msgid "%s Reminder Sent"
msgstr ""

#: app/views/emails/admin_cc_expires_reminder.php:9
#: app/views/emails/admin_member_signup_reminder.php:9
#: app/views/emails/admin_signup_abandoned_reminder.php:9
#: app/views/emails/admin_sub_expires_reminder.php:9
#: app/views/emails/admin_sub_renews_reminder.php:9
msgctxt "ui"
msgid "A %1$s Reminder (%2$s) was just sent to %3$s for the following subscription:"
msgstr ""

#: app/views/emails/admin_cc_expires_reminder.php:11
#: app/views/emails/admin_member_signup_reminder.php:11
#: app/views/emails/admin_signup_abandoned_reminder.php:11
#: app/views/emails/admin_sub_expires_reminder.php:11
#: app/views/emails/admin_sub_renews_reminder.php:11
msgctxt "ui"
msgid "Membership:"
msgstr ""

#: app/views/emails/admin_cc_expires_reminder.php:13
#: app/views/emails/admin_member_signup_reminder.php:13
#: app/views/emails/admin_signup_abandoned_reminder.php:12
#: app/views/emails/admin_sub_expires_reminder.php:13
#: app/views/emails/admin_sub_renews_reminder.php:13
msgctxt "ui"
msgid "Created:"
msgstr ""

#: app/views/emails/admin_cc_expires_reminder.php:14
#: app/views/emails/admin_member_signup_reminder.php:14
#: app/views/emails/admin_sub_expires_reminder.php:14
msgctxt "ui"
msgid "Expires:"
msgstr ""

#: app/views/emails/admin_cc_expires_reminder.php:15
#: app/views/emails/admin_member_signup_reminder.php:15
#: app/views/emails/admin_sub_expires_reminder.php:15
#: app/views/emails/admin_sub_renews_reminder.php:15
msgctxt "ui"
msgid "CC Expires:"
msgstr ""

#: app/views/emails/admin_cc_expires_reminder.php:19
#: app/views/emails/admin_member_signup_reminder.php:19
#: app/views/emails/admin_signup_abandoned_reminder.php:16
#: app/views/emails/admin_sub_expires_reminder.php:19
#: app/views/emails/admin_sub_renews_reminder.php:19
msgctxt "ui"
msgid "User ID:"
msgstr ""

#: app/views/emails/admin_cc_expiring.php:5
msgctxt "ui"
msgid "A Credit Card is Expiring on {$subscr_cc_month_exp}/{$subscr_cc_year_exp}"
msgstr ""

#: app/views/emails/admin_cc_expiring.php:9
msgctxt "ui"
msgid "A member's Credit Card will expire before their next billing on {$blog_name}."
msgstr ""

#: app/views/emails/admin_cc_expiring.php:11
#: app/views/emails/user_cancelled_sub.php:11
#: app/views/emails/user_cc_expiring.php:12
#: app/views/emails/user_downgraded_sub.php:11
#: app/views/emails/user_failed_txn.php:12
#: app/views/emails/user_refunded_txn.php:12
#: app/views/emails/user_resumed_sub.php:11
#: app/views/emails/user_stripe_invoice.php:14
#: app/views/emails/user_suspended_sub.php:11
#: app/views/emails/user_upgraded_sub.php:11
msgctxt "ui"
msgid "Website:"
msgstr ""

#: app/views/emails/admin_cc_expiring.php:13
#: app/views/emails/user_cc_expiring.php:14
msgctxt "ui"
msgid "Subscription Num:"
msgstr ""

#: app/views/emails/admin_cc_expiring.php:14
#: app/views/emails/user_cc_expiring.php:15
msgctxt "ui"
msgid "Credit Card Num:"
msgstr ""

#: app/views/emails/admin_cc_expiring.php:15
#: app/views/emails/user_cc_expiring.php:16
msgctxt "ui"
msgid "Credit Card Exp:"
msgstr ""

#: app/views/emails/admin_cc_expiring.php:16
#: app/views/emails/user_cc_expiring.php:17
msgctxt "ui"
msgid "Next Billing:"
msgstr ""

#: app/views/emails/admin_cc_expiring.php:23
#: app/views/emails/admin_receipt.php:20
#: app/views/emails/user_cc_expiring.php:23
#: app/views/emails/user_receipt.php:29
msgctxt "ui"
msgid "Billed to"
msgstr ""

#: app/views/emails/admin_downgraded_sub.php:5
msgctxt "ui"
msgid "Subscription Downgraded"
msgstr ""

#: app/views/emails/admin_downgraded_sub.php:9
msgctxt "ui"
msgid "A user downgraded their subscription on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_failed_txn.php:5
msgctxt "ui"
msgid "Transaction Failed"
msgstr ""

#: app/views/emails/admin_failed_txn.php:6
#: app/views/emails/admin_new_one_off.php:6
#: app/views/emails/admin_refunded_txn.php:6
msgctxt "ui"
msgid "{$trans_num} &ndash; {$user_full_name}"
msgstr ""

#: app/views/emails/admin_failed_txn.php:9
msgctxt "ui"
msgid "A transaction just failed on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_failed_txn.php:11
#: app/views/emails/admin_new_one_off.php:15
#: app/views/emails/admin_refunded_txn.php:11
#: app/views/emails/user_failed_txn.php:13
#: app/views/emails/user_receipt.php:10
#: app/views/emails/user_refunded_txn.php:13
msgctxt "ui"
msgid "Amount:"
msgstr ""

#: app/views/emails/admin_failed_txn.php:12
#: app/views/emails/admin_new_one_off.php:14
#: app/views/emails/admin_receipt.php:13
#: app/views/emails/admin_refunded_txn.php:12
#: app/views/emails/user_failed_txn.php:14
#: app/views/emails/user_receipt.php:13
#: app/views/emails/user_refunded_txn.php:14
msgctxt "ui"
msgid "Transaction:"
msgstr ""

#: app/views/emails/admin_failed_txn.php:13
#: app/views/emails/admin_refunded_txn.php:13
#: app/views/emails/user_failed_txn.php:15
#: app/views/emails/user_receipt.php:11
#: app/views/emails/user_refunded_txn.php:15
msgctxt "ui"
msgid "Date:"
msgstr ""

#: app/views/emails/admin_failed_txn.php:14
#: app/views/emails/admin_refunded_txn.php:14
#: app/views/emails/user_cancelled_sub.php:15
#: app/views/emails/user_downgraded_sub.php:15
#: app/views/emails/user_failed_txn.php:16
#: app/views/emails/user_refunded_txn.php:16
#: app/views/emails/user_resumed_sub.php:15
#: app/views/emails/user_suspended_sub.php:15
#: app/views/emails/user_upgraded_sub.php:15
msgctxt "ui"
msgid "Status:"
msgstr ""

#: app/views/emails/admin_failed_txn.php:14
#: app/views/emails/user_failed_txn.php:16
msgctxt "ui"
msgid "Failed"
msgstr ""

#: app/views/emails/admin_new_one_off.php:5
msgctxt "ui"
msgid "New One-Time Subscription"
msgstr ""

#: app/views/emails/admin_new_one_off.php:9
msgctxt "ui"
msgid "A non-recurring subscription was just created on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_new_sub.php:5
msgctxt "ui"
msgid "New Recurring Subscription"
msgstr ""

#: app/views/emails/admin_new_sub.php:9
msgctxt "ui"
msgid "A subscription was just created on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_new_sub.php:17
#: app/views/emails/admin_resumed_sub.php:17
#: app/views/emails/admin_suspended_sub.php:17
msgctxt "ui"
msgid "Auto-Rebilling:"
msgstr ""

#: app/views/emails/admin_password_reset.php:6
msgctxt "ui"
msgid "Password Lost and Changed for user "
msgstr ""

#: app/views/emails/admin_receipt.php:5
msgctxt "ui"
msgid "Payment from {$user_full_name}"
msgstr ""

#: app/views/emails/admin_receipt.php:6
msgctxt "ui"
msgid "{$product_name} &ndash; {$trans_num}"
msgstr ""

#: app/views/emails/admin_receipt.php:10
msgctxt "ui"
msgid "Payment Amount:"
msgstr ""

#: app/views/emails/admin_receipt.php:11
msgctxt "ui"
msgid "Invoice Number:"
msgstr ""

#: app/views/emails/admin_receipt.php:12
msgctxt "ui"
msgid "Invoice Date:"
msgstr ""

#: app/views/emails/admin_refunded_txn.php:5
msgctxt "ui"
msgid "Transaction Refunded"
msgstr ""

#: app/views/emails/admin_refunded_txn.php:9
msgctxt "ui"
msgid "A transaction was just refunded on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_refunded_txn.php:14
#: app/views/emails/user_refunded_txn.php:16
msgctxt "ui"
msgid "Refunded"
msgstr ""

#: app/views/emails/admin_resumed_sub.php:5
msgctxt "ui"
msgid "Subscription Resumed"
msgstr ""

#: app/views/emails/admin_resumed_sub.php:9
msgctxt "ui"
msgid "A user resumed their subscription on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_signup.php:5
msgctxt "ui"
msgid "New Signup"
msgstr ""

#: app/views/emails/admin_signup.php:6
msgctxt "ui"
msgid "{$user_full_name} ({$username})"
msgstr ""

#: app/views/emails/admin_signup.php:9
msgctxt "ui"
msgid "A new user just signed up on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_signup.php:11
msgctxt "ui"
msgid "ID:"
msgstr ""

#: app/views/emails/admin_signup.php:15
msgctxt "ui"
msgid "IP:"
msgstr ""

#: app/views/emails/admin_sub_renews_reminder.php:14
msgctxt "ui"
msgid "Renews:"
msgstr ""

#: app/views/emails/admin_suspended_sub.php:5
msgctxt "ui"
msgid "Subscription Paused"
msgstr ""

#: app/views/emails/admin_suspended_sub.php:9
msgctxt "ui"
msgid "A subscription was paused on {$blog_name}:"
msgstr ""

#: app/views/emails/admin_suspended_sub.php:17
#: app/views/emails/user_suspended_sub.php:15
msgctxt "ui"
msgid "Paused"
msgstr ""

#: app/views/emails/admin_upgraded_sub.php:5
msgctxt "ui"
msgid "Subscription Upgraded"
msgstr ""

#: app/views/emails/admin_upgraded_sub.php:9
msgctxt "ui"
msgid "A user upgraded their subscription on {$blog_name}:"
msgstr ""

#: app/views/emails/user_cancelled_sub.php:5
msgctxt "ui"
msgid "Your Automatic Payments Have Been Stopped"
msgstr ""

#: app/views/emails/user_cancelled_sub.php:6
#: app/views/emails/user_downgraded_sub.php:6
#: app/views/emails/user_resumed_sub.php:6
#: app/views/emails/user_stripe_invoice.php:6
#: app/views/emails/user_suspended_sub.php:6
#: app/views/emails/user_upgraded_sub.php:6
msgctxt "ui"
msgid "{$subscr_num} &ndash; {$blog_name}"
msgstr ""

#: app/views/emails/user_cancelled_sub.php:9
msgctxt "ui"
msgid "Your subscription on {$blog_name} was cancelled:"
msgstr ""

#: app/views/emails/user_cc_expires_reminder.php:9
#: app/views/emails/user_member_signup_reminder.php:9
#: app/views/emails/user_set_password.php:6
#: app/views/emails/user_signup_abandoned_reminder.php:9
#: app/views/emails/user_sub_expires_reminder.php:9
#: app/views/emails/user_sub_renews_reminder.php:9
msgctxt "ui"
msgid "Hi %s,"
msgstr ""

#: app/views/emails/user_cc_expires_reminder.php:10
#: app/views/emails/user_sub_expires_reminder.php:10
#: app/views/emails/user_sub_renews_reminder.php:10
msgctxt "ui"
msgid "Just a friendly reminder that your %1$s on <strong>%2$s</strong>."
msgstr ""

#: app/views/emails/user_cc_expires_reminder.php:11
msgctxt "ui"
msgid "We wouldn't want you to miss out on any of the great content we're working on so <strong>make sure you %1$supdate your credit card today%2$s</strong>."
msgstr ""

#: app/views/emails/user_cc_expires_reminder.php:12
#: app/views/emails/user_member_signup_reminder.php:11
#: app/views/emails/user_product_welcome.php:17
#: app/views/emails/user_signup_abandoned_reminder.php:12
#: app/views/emails/user_sub_expires_reminder.php:12
#: app/views/emails/user_sub_renews_reminder.php:12
#: app/views/emails/user_welcome.php:17
msgctxt "ui"
msgid "Cheers!"
msgstr ""

#: app/views/emails/user_cc_expires_reminder.php:13
#: app/views/emails/user_member_signup_reminder.php:12
#: app/views/emails/user_product_welcome.php:18
#: app/views/emails/user_signup_abandoned_reminder.php:13
#: app/views/emails/user_sub_expires_reminder.php:13
#: app/views/emails/user_sub_renews_reminder.php:13
#: app/views/emails/user_welcome.php:18
msgctxt "ui"
msgid "The {$blog_name} Team"
msgstr ""

#: app/views/emails/user_cc_expiring.php:5
msgctxt "ui"
msgid "Your Credit Card is Expiring on {$subscr_cc_month_exp}/{$subscr_cc_year_exp}"
msgstr ""

#: app/views/emails/user_cc_expiring.php:9
msgctxt "ui"
msgid "Your Credit Card will expire before your next billing on {$blog_name}."
msgstr ""

#: app/views/emails/user_cc_expiring.php:10
msgctxt "ui"
msgid "To avoid any interruptions in your subscription, please %1$slogin to your account%2$s and update your credit card information."
msgstr ""

#: app/views/emails/user_confirm_payment.php:5
msgctxt "ui"
msgid "Confirm subscription payment"
msgstr ""

#. Translators: %1$s: subscription ID, %2$s: blog name.
#: app/views/emails/user_confirm_payment.php:10
msgctxt "ui"
msgid "%1$s &ndash; %2$s"
msgstr ""

#. Translators: %s: blog name.
#: app/views/emails/user_confirm_payment.php:22
msgctxt "ui"
msgid "We tried to process a payment for your subscription to %s, but your bank requested authentication for this transaction. Please confirm the payment by clicking on the following link:"
msgstr ""

#: app/views/emails/user_downgraded_sub.php:5
msgctxt "ui"
msgid "Your subscription has been downgraded"
msgstr ""

#: app/views/emails/user_downgraded_sub.php:9
msgctxt "ui"
msgid "Your subscription on {$blog_name} was downgraded:"
msgstr ""

#: app/views/emails/user_failed_txn.php:5
msgctxt "ui"
msgid "Your Transaction Failed"
msgstr ""

#: app/views/emails/user_failed_txn.php:6
#: app/views/emails/user_refunded_txn.php:6
msgctxt "ui"
msgid "{$trans_num} &ndash; {$blog_name}"
msgstr ""

#: app/views/emails/user_failed_txn.php:9
msgctxt "ui"
msgid "Sorry, your transaction on {$blog_name} failed. Please update your payment details and retry your purchase."
msgstr ""

#: app/views/emails/user_failed_txn.php:10
msgctxt "ui"
msgid "Here are the details of your transaction:"
msgstr ""

#: app/views/emails/user_member_signup_reminder.php:10
msgctxt "ui"
msgid "Thanks for registering for {$product_name}!"
msgstr ""

#: app/views/emails/user_password_was_reset.php:7
msgctxt "ui"
msgid "Your password was successfully reset on %1$s!"
msgstr ""

#: app/views/emails/user_password_was_reset.php:10
msgctxt "ui"
msgid "Username: %1$s"
msgstr ""

#: app/views/emails/user_password_was_reset.php:12
msgctxt "ui"
msgid "Password: *** Successfully Reset ***"
msgstr ""

#: app/views/emails/user_password_was_reset.php:14
msgctxt "ui"
msgid "You can now login here: %1$s"
msgstr ""

#: app/views/emails/user_product_welcome.php:5
msgctxt "ui"
msgid "Thanks for Purchasing {$product_name}"
msgstr ""

#: app/views/emails/user_product_welcome.php:9
#: app/views/emails/user_welcome.php:9
msgctxt "ui"
msgid "You can login here: {$login_page}"
msgstr ""

#: app/views/emails/user_product_welcome.php:10
#: app/views/emails/user_welcome.php:10
msgctxt "ui"
msgid "Using this username and password:"
msgstr ""

#: app/views/emails/user_product_welcome.php:13
#: app/views/emails/user_welcome.php:13
msgctxt "ui"
msgid "Username:"
msgstr ""

#: app/views/emails/user_product_welcome.php:14
#: app/views/emails/user_welcome.php:14
msgctxt "ui"
msgid "Password:"
msgstr ""

#: app/views/emails/user_product_welcome.php:14
#: app/views/emails/user_welcome.php:14
msgctxt "ui"
msgid "*** Password you set during signup ***"
msgstr ""

#: app/views/emails/user_receipt.php:5
msgctxt "ui"
msgid "Payment Receipt"
msgstr ""

#: app/views/emails/user_receipt.php:6
msgctxt "ui"
msgid "for your payment to {$blog_name}"
msgstr ""

#: app/views/emails/user_receipt.php:12
msgctxt "ui"
msgid "Invoice:"
msgstr ""

#: app/views/emails/user_receipt.php:19
msgctxt "ui"
msgid "Paid to"
msgstr ""

#: app/views/emails/user_refunded_txn.php:5
msgctxt "ui"
msgid "Your Transaction Was Refunded"
msgstr ""

#: app/views/emails/user_refunded_txn.php:10
msgctxt "ui"
msgid "Your transaction on {$blog_name} was refunded:"
msgstr ""

#: app/views/emails/user_reset_password.php:6
msgctxt "ui"
msgid "Someone requested to reset your password for %1$s on %2$s at %3$s"
msgstr ""

#: app/views/emails/user_reset_password.php:7
msgctxt "ui"
msgid "To reset your password visit the following address, otherwise just ignore this email and nothing will happen."
msgstr ""

#: app/views/emails/user_resumed_sub.php:5
msgctxt "ui"
msgid "Your subscription has resumed"
msgstr ""

#: app/views/emails/user_resumed_sub.php:9
msgctxt "ui"
msgid "Your subscription on {$blog_name} has resumed:"
msgstr ""

#: app/views/emails/user_set_password.php:7
msgctxt "ui"
msgid "You can create a new password for %1$s on %2$s at %3$s by clicking on the following link:"
msgstr ""

#: app/views/emails/user_signup_abandoned_reminder.php:5
msgctxt "ui"
msgid "Please complete your signup"
msgstr ""

#: app/views/emails/user_signup_abandoned_reminder.php:10
msgctxt "ui"
msgid "We just saw that you weren't able to complete your signup for %1$s on %2$s."
msgstr ""

#: app/views/emails/user_signup_abandoned_reminder.php:11
msgctxt "ui"
msgid "We'd be really sad if you missed out so we just wanted to drop you a line to let you to know that it's easy to <strong>%1$scomplete your signup today%2$s</strong>."
msgstr ""

#: app/views/emails/user_stripe_invoice.php:5
msgctxt "ui"
msgid "Your Subscription Payment Failed"
msgstr ""

#: app/views/emails/user_stripe_invoice.php:9
msgctxt "ui"
msgid "Sorry, your latest subscription payment on {$blog_name} failed. Please click the link below to pay the invoice to keep your subscription active."
msgstr ""

#: app/views/emails/user_stripe_invoice.php:11
msgctxt "ui"
msgid "Pay Invoice"
msgstr ""

#: app/views/emails/user_sub_expires_reminder.php:11
msgctxt "ui"
msgid "We wouldn't want you to miss out on any of the great content we're working on so <strong>make sure you %1$srenew it today%2$s</strong>."
msgstr ""

#: app/views/emails/user_sub_renews_reminder.php:11
msgctxt "ui"
msgid "If this isn't correct you can update your %1$saccount%2$s."
msgstr ""

#: app/views/emails/user_suspended_sub.php:5
msgctxt "ui"
msgid "Your subscription has been paused"
msgstr ""

#: app/views/emails/user_suspended_sub.php:9
msgctxt "ui"
msgid "Your subscription on {$blog_name} was paused:"
msgstr ""

#: app/views/emails/user_upgraded_sub.php:5
msgctxt "ui"
msgid "Your subscription has been upgraded"
msgstr ""

#: app/views/emails/user_upgraded_sub.php:9
msgctxt "ui"
msgid "Your subscription on {$blog_name} was upgraded:"
msgstr ""

#: app/views/emails/user_welcome.php:5
msgctxt "ui"
msgid "Welcome {$user_first_name}!"
msgstr ""

#: app/views/login/already_logged_in.php:15
#: app/views/login/form.php:14
#: app/views/readylaunch/login/form.php:54
msgctxt "ui"
msgid "You're already logged in. %1$sLogout.%2$s"
msgstr ""

#: app/views/login/forgot_password.php:6
#: app/views/readylaunch/login/forgot_password.php:11
msgctxt "ui"
msgid "Request a Password Reset"
msgstr ""

#: app/views/login/forgot_password.php:10
#: app/views/readylaunch/login/forgot_password.php:15
msgctxt "ui"
msgid "Enter Your Username or Email Address"
msgstr ""

#: app/views/login/forgot_password.php:16
#: app/views/readylaunch/login/forgot_password.php:21
msgctxt "ui"
msgid "Request Password Reset"
msgstr ""

#: app/views/login/forgot_password_requested.php:11
#: app/views/readylaunch/login/forgot_password_requested.php:14
msgctxt "ui"
msgid "Password could not be reset."
msgstr ""

#: app/views/login/forgot_password_requested.php:13
#: app/views/readylaunch/login/forgot_password_requested.php:16
msgctxt "ui"
msgid "Please contact us for further assistance."
msgstr ""

#: app/views/login/forgot_password_requested.php:18
#: app/views/readylaunch/login/forgot_password_requested.php:21
msgctxt "ui"
msgid "Successfully requested password reset"
msgstr ""

#: app/views/login/forgot_password_requested.php:19
#: app/views/readylaunch/login/forgot_password_requested.php:22
msgctxt "ui"
msgid "If a matching account is found, you'll receive a password reset email soon. Click the link found in that email to reset your password."
msgstr ""

#: app/views/login/form.php:29
#: app/views/readylaunch/login/form.php:89
msgctxt "ui"
msgid "Username or E-mail"
msgstr ""

#: app/views/login/form.php:30
#: app/views/readylaunch/login/form.php:90
msgctxt "ui"
msgid "Username"
msgstr ""

#: app/views/login/form.php:37
#: app/views/login/reset_password.php:11
#: app/views/readylaunch/login/form.php:97
#: app/views/readylaunch/login/form.php:99
#: app/views/readylaunch/login/reset_password.php:15
msgctxt "ui"
msgid "Password"
msgstr ""

#: app/views/login/form.php:48
#: app/views/readylaunch/login/form.php:109
msgctxt "ui"
msgid "Remember Me"
msgstr ""

#: app/views/login/form.php:52
#: app/views/readylaunch/login/form.php:114
msgctxt "ui"
msgid "Log In"
msgstr ""

#: app/views/login/form.php:60
msgctxt "ui"
msgid "Forgot Password"
msgstr ""

#: app/views/login/reset_password.php:6
#: app/views/readylaunch/login/reset_password.php:10
msgctxt "ui"
msgid "Enter your new password"
msgstr ""

#: app/views/login/reset_password.php:22
#: app/views/readylaunch/login/reset_password.php:26
msgctxt "ui"
msgid "Password Confirmation"
msgstr ""

#: app/views/readylaunch/account/home.php:8
msgctxt "ui"
msgid "Profile"
msgstr ""

#: app/views/readylaunch/account/home.php:26
msgctxt "ui"
msgid "Name"
msgstr ""

#: app/views/readylaunch/account/home.php:36
#: app/views/readylaunch/account/payments.php:20
#: app/views/readylaunch/account/payments.php:60
msgctxt "ui"
msgid "Email"
msgstr ""

#: app/views/readylaunch/account/home.php:46
msgctxt "ui"
msgid "Billing Address"
msgstr ""

#: app/views/readylaunch/account/home.php:62
msgctxt "ui"
msgid "View Payments"
msgstr ""

#: app/views/readylaunch/account/home.php:63
msgctxt "ui"
msgid "View Subscriptions"
msgstr ""

#: app/views/readylaunch/account/home.php:114
#: app/views/readylaunch/checkout/form.php:49
msgctxt "ui"
msgid "First Name"
msgstr ""

#: app/views/readylaunch/account/home.php:118
#: app/views/readylaunch/checkout/form.php:55
msgctxt "ui"
msgid "Last Name"
msgstr ""

#: app/views/readylaunch/account/home.php:140
#: app/views/readylaunch/account/home.php:146
msgctxt "ui"
msgid "Change your billing address:*"
msgstr ""

#: app/views/readylaunch/account/home.php:159
msgctxt "ui"
msgid "Save Changes"
msgstr ""

#: app/views/readylaunch/account/nav.php:10
msgctxt "ui"
msgid "My Profile"
msgstr ""

#: app/views/readylaunch/account/payments.php:18
#: app/views/readylaunch/account/payments.php:51
msgctxt "ui"
msgid "Card"
msgstr ""

#: app/views/readylaunch/account/subscriptions.php:22
#: app/views/readylaunch/account/subscriptions.php:171
msgctxt "ui"
msgid "Dates"
msgstr ""

#: app/views/readylaunch/account/subscriptions.php:180
#: app/views/readylaunch/account/subscriptions.php:186
msgctxt "ui"
msgid "Expired: %s"
msgstr ""

#: app/views/readylaunch/checkout/form.php:245
msgctxt "ui"
msgid "Pay %s"
msgstr ""

#: app/views/readylaunch/login/form.php:93
msgctxt "ui"
msgid "Username (email)"
msgstr ""

#: app/views/readylaunch/login/form.php:122
msgctxt "ui"
msgid "Forgot Password?"
msgstr ""

#: app/views/readylaunch/login/form.php:122
msgctxt "ui"
msgid "Click here"
msgstr ""

#: app/views/readylaunch/shared/expired_password_reset.php:6
#: app/views/shared/expired_password_reset.php:6
msgctxt "ui"
msgid "The link you clicked has expired, please attempt to %s."
msgstr ""

#: app/views/readylaunch/shared/expired_password_reset.php:6
#: app/views/shared/expired_password_reset.php:6
msgctxt "ui"
msgid "reset your password again"
msgstr ""

#: app/views/readylaunch/shared/unauthorized.php:4
#: app/views/shared/unauthorized.php:6
msgctxt "ui"
msgid "You're unauthorized to view this page. Why don't you %s and try again."
msgstr ""

#: app/views/readylaunch/thankyou.php:17
msgctxt "ui"
msgid "Thank you for your purchase"
msgstr ""

#: app/views/readylaunch/thankyou.php:31
msgctxt "ui"
msgid "Payment Successful"
msgstr ""

#: app/views/readylaunch/thankyou.php:34
msgctxt "ui"
msgid "Order: "
msgstr ""

#: app/views/readylaunch/thankyou.php:73
msgctxt "ui"
msgid "Print"
msgstr ""

#: app/views/readylaunch/thankyou.php:84
msgctxt "ui"
msgid "Back to home"
msgstr ""

#: app/views/shared/errors.php:10
msgctxt "ui"
msgid "ERROR"
msgstr ""

#: app/views/shared/has_errors.php:4
msgctxt "ui"
msgid "Please fix the errors above"
msgstr ""

#: app/views/shared/unknown_error.php:5
msgctxt "ui"
msgid "An Unknown Error Occurred"
msgstr ""

#: app/views/shortcodes/list_users_subscriptions.php:19
msgctxt "ui"
msgid "Expires"
msgstr ""

#: app/views/shortcodes/list_users_subscriptions.php:32
msgctxt "ui"
msgid "Expired"
msgstr ""

#: app/views/shortcodes/list_users_subscriptions.php:38
msgctxt "ui"
msgid "You have no subscriptions"
msgstr ""

#: app/views/taxes/vat_signup.php:5
msgctxt "ui"
msgid "Customer Type:"
msgstr ""

#: app/views/taxes/vat_signup.php:9
msgctxt "ui"
msgid "Consumer"
msgstr ""

#: app/views/taxes/vat_signup.php:13
msgctxt "ui"
msgid "Business"
msgstr ""

#: app/views/taxes/vat_signup.php:19
msgctxt "ui"
msgid "VAT Number:"
msgstr ""

#: app/views/taxes/vat_signup.php:20
msgctxt "ui"
msgid "Invalid VAT Number"
msgstr ""

#: i18n/countries.php:8
msgctxt "ui"
msgid "Afghanistan"
msgstr ""

#: i18n/countries.php:9
msgctxt "ui"
msgid "&#197;land Islands"
msgstr ""

#: i18n/countries.php:10
msgctxt "ui"
msgid "Albania"
msgstr ""

#: i18n/countries.php:11
msgctxt "ui"
msgid "Algeria"
msgstr ""

#: i18n/countries.php:12
msgctxt "ui"
msgid "Andorra"
msgstr ""

#: i18n/countries.php:13
msgctxt "ui"
msgid "Angola"
msgstr ""

#: i18n/countries.php:14
msgctxt "ui"
msgid "Anguilla"
msgstr ""

#: i18n/countries.php:15
msgctxt "ui"
msgid "Antarctica"
msgstr ""

#: i18n/countries.php:16
msgctxt "ui"
msgid "Antigua and Barbuda"
msgstr ""

#: i18n/countries.php:17
msgctxt "ui"
msgid "Argentina"
msgstr ""

#: i18n/countries.php:18
msgctxt "ui"
msgid "Armenia"
msgstr ""

#: i18n/countries.php:19
msgctxt "ui"
msgid "Aruba"
msgstr ""

#: i18n/countries.php:20
msgctxt "ui"
msgid "Australia"
msgstr ""

#: i18n/countries.php:21
msgctxt "ui"
msgid "Austria"
msgstr ""

#: i18n/countries.php:22
msgctxt "ui"
msgid "Azerbaijan"
msgstr ""

#: i18n/countries.php:23
msgctxt "ui"
msgid "Bahamas"
msgstr ""

#: i18n/countries.php:24
msgctxt "ui"
msgid "Bahrain"
msgstr ""

#: i18n/countries.php:25
msgctxt "ui"
msgid "Bangladesh"
msgstr ""

#: i18n/countries.php:26
msgctxt "ui"
msgid "Barbados"
msgstr ""

#: i18n/countries.php:27
msgctxt "ui"
msgid "Belarus"
msgstr ""

#: i18n/countries.php:28
msgctxt "ui"
msgid "Belgium"
msgstr ""

#: i18n/countries.php:29
msgctxt "ui"
msgid "Belau"
msgstr ""

#: i18n/countries.php:30
msgctxt "ui"
msgid "Belize"
msgstr ""

#: i18n/countries.php:31
msgctxt "ui"
msgid "Benin"
msgstr ""

#: i18n/countries.php:32
msgctxt "ui"
msgid "Bermuda"
msgstr ""

#: i18n/countries.php:33
msgctxt "ui"
msgid "Bhutan"
msgstr ""

#: i18n/countries.php:34
msgctxt "ui"
msgid "Bolivia"
msgstr ""

#: i18n/countries.php:35
msgctxt "ui"
msgid "Bonaire, Saint Eustatius and Saba"
msgstr ""

#: i18n/countries.php:36
msgctxt "ui"
msgid "Bosnia and Herzegovina"
msgstr ""

#: i18n/countries.php:37
msgctxt "ui"
msgid "Botswana"
msgstr ""

#: i18n/countries.php:38
msgctxt "ui"
msgid "Bouvet Island"
msgstr ""

#: i18n/countries.php:39
msgctxt "ui"
msgid "Brazil"
msgstr ""

#: i18n/countries.php:40
msgctxt "ui"
msgid "British Indian Ocean Territory"
msgstr ""

#: i18n/countries.php:41
msgctxt "ui"
msgid "British Virgin Islands"
msgstr ""

#: i18n/countries.php:42
msgctxt "ui"
msgid "Brunei"
msgstr ""

#: i18n/countries.php:43
msgctxt "ui"
msgid "Bulgaria"
msgstr ""

#: i18n/countries.php:44
msgctxt "ui"
msgid "Burkina Faso"
msgstr ""

#: i18n/countries.php:45
msgctxt "ui"
msgid "Burundi"
msgstr ""

#: i18n/countries.php:46
msgctxt "ui"
msgid "Cambodia"
msgstr ""

#: i18n/countries.php:47
msgctxt "ui"
msgid "Cameroon"
msgstr ""

#: i18n/countries.php:48
msgctxt "ui"
msgid "Canada"
msgstr ""

#: i18n/countries.php:49
msgctxt "ui"
msgid "Cape Verde"
msgstr ""

#: i18n/countries.php:50
msgctxt "ui"
msgid "Cayman Islands"
msgstr ""

#: i18n/countries.php:51
msgctxt "ui"
msgid "Central African Republic"
msgstr ""

#: i18n/countries.php:52
msgctxt "ui"
msgid "Chad"
msgstr ""

#: i18n/countries.php:53
msgctxt "ui"
msgid "Chile"
msgstr ""

#: i18n/countries.php:54
msgctxt "ui"
msgid "China"
msgstr ""

#: i18n/countries.php:55
msgctxt "ui"
msgid "Christmas Island"
msgstr ""

#: i18n/countries.php:56
msgctxt "ui"
msgid "Cocos (Keeling) Islands"
msgstr ""

#: i18n/countries.php:57
msgctxt "ui"
msgid "Colombia"
msgstr ""

#: i18n/countries.php:58
msgctxt "ui"
msgid "Comoros"
msgstr ""

#: i18n/countries.php:59
msgctxt "ui"
msgid "Congo (Brazzaville)"
msgstr ""

#: i18n/countries.php:60
msgctxt "ui"
msgid "Congo (Kinshasa)"
msgstr ""

#: i18n/countries.php:61
msgctxt "ui"
msgid "Cook Islands"
msgstr ""

#: i18n/countries.php:62
msgctxt "ui"
msgid "Costa Rica"
msgstr ""

#: i18n/countries.php:63
msgctxt "ui"
msgid "Croatia"
msgstr ""

#: i18n/countries.php:64
msgctxt "ui"
msgid "Cuba"
msgstr ""

#: i18n/countries.php:65
msgctxt "ui"
msgid "Cura&Ccedil;ao"
msgstr ""

#: i18n/countries.php:66
msgctxt "ui"
msgid "Cyprus"
msgstr ""

#: i18n/countries.php:67
msgctxt "ui"
msgid "Czech Republic"
msgstr ""

#: i18n/countries.php:68
msgctxt "ui"
msgid "Denmark"
msgstr ""

#: i18n/countries.php:69
msgctxt "ui"
msgid "Djibouti"
msgstr ""

#: i18n/countries.php:70
msgctxt "ui"
msgid "Dominica"
msgstr ""

#: i18n/countries.php:71
msgctxt "ui"
msgid "Dominican Republic"
msgstr ""

#: i18n/countries.php:72
msgctxt "ui"
msgid "Ecuador"
msgstr ""

#: i18n/countries.php:73
msgctxt "ui"
msgid "Egypt"
msgstr ""

#: i18n/countries.php:74
msgctxt "ui"
msgid "El Salvador"
msgstr ""

#: i18n/countries.php:75
msgctxt "ui"
msgid "Equatorial Guinea"
msgstr ""

#: i18n/countries.php:76
msgctxt "ui"
msgid "Eritrea"
msgstr ""

#: i18n/countries.php:77
msgctxt "ui"
msgid "Estonia"
msgstr ""

#: i18n/countries.php:78
msgctxt "ui"
msgid "Ethiopia"
msgstr ""

#: i18n/countries.php:79
msgctxt "ui"
msgid "Falkland Islands"
msgstr ""

#: i18n/countries.php:80
msgctxt "ui"
msgid "Faroe Islands"
msgstr ""

#: i18n/countries.php:81
msgctxt "ui"
msgid "Fiji"
msgstr ""

#: i18n/countries.php:82
msgctxt "ui"
msgid "Finland"
msgstr ""

#: i18n/countries.php:83
msgctxt "ui"
msgid "France"
msgstr ""

#: i18n/countries.php:84
msgctxt "ui"
msgid "French Guiana"
msgstr ""

#: i18n/countries.php:85
msgctxt "ui"
msgid "French Polynesia"
msgstr ""

#: i18n/countries.php:86
msgctxt "ui"
msgid "French Southern Territories"
msgstr ""

#: i18n/countries.php:87
msgctxt "ui"
msgid "Gabon"
msgstr ""

#: i18n/countries.php:88
msgctxt "ui"
msgid "Gambia"
msgstr ""

#: i18n/countries.php:89
msgctxt "ui"
msgid "Georgia"
msgstr ""

#: i18n/countries.php:90
msgctxt "ui"
msgid "Germany"
msgstr ""

#: i18n/countries.php:91
msgctxt "ui"
msgid "Ghana"
msgstr ""

#: i18n/countries.php:92
msgctxt "ui"
msgid "Gibraltar"
msgstr ""

#: i18n/countries.php:93
msgctxt "ui"
msgid "Greece"
msgstr ""

#: i18n/countries.php:94
msgctxt "ui"
msgid "Greenland"
msgstr ""

#: i18n/countries.php:95
msgctxt "ui"
msgid "Grenada"
msgstr ""

#: i18n/countries.php:96
msgctxt "ui"
msgid "Guadeloupe"
msgstr ""

#: i18n/countries.php:97
msgctxt "ui"
msgid "Guatemala"
msgstr ""

#: i18n/countries.php:98
msgctxt "ui"
msgid "Guernsey"
msgstr ""

#: i18n/countries.php:99
msgctxt "ui"
msgid "Guinea"
msgstr ""

#: i18n/countries.php:100
msgctxt "ui"
msgid "Guinea-Bissau"
msgstr ""

#: i18n/countries.php:101
msgctxt "ui"
msgid "Guyana"
msgstr ""

#: i18n/countries.php:102
msgctxt "ui"
msgid "Haiti"
msgstr ""

#: i18n/countries.php:103
msgctxt "ui"
msgid "Heard Island and McDonald Islands"
msgstr ""

#: i18n/countries.php:104
msgctxt "ui"
msgid "Honduras"
msgstr ""

#: i18n/countries.php:105
msgctxt "ui"
msgid "Hong Kong"
msgstr ""

#: i18n/countries.php:106
msgctxt "ui"
msgid "Hungary"
msgstr ""

#: i18n/countries.php:107
msgctxt "ui"
msgid "Iceland"
msgstr ""

#: i18n/countries.php:108
msgctxt "ui"
msgid "India"
msgstr ""

#: i18n/countries.php:109
msgctxt "ui"
msgid "Indonesia"
msgstr ""

#: i18n/countries.php:110
msgctxt "ui"
msgid "Iran"
msgstr ""

#: i18n/countries.php:111
msgctxt "ui"
msgid "Iraq"
msgstr ""

#: i18n/countries.php:112
msgctxt "ui"
msgid "Republic of Ireland"
msgstr ""

#: i18n/countries.php:113
msgctxt "ui"
msgid "Isle of Man"
msgstr ""

#: i18n/countries.php:114
msgctxt "ui"
msgid "Israel"
msgstr ""

#: i18n/countries.php:115
msgctxt "ui"
msgid "Italy"
msgstr ""

#: i18n/countries.php:116
msgctxt "ui"
msgid "Ivory Coast"
msgstr ""

#: i18n/countries.php:117
msgctxt "ui"
msgid "Jamaica"
msgstr ""

#: i18n/countries.php:118
msgctxt "ui"
msgid "Japan"
msgstr ""

#: i18n/countries.php:119
msgctxt "ui"
msgid "Jersey"
msgstr ""

#: i18n/countries.php:120
msgctxt "ui"
msgid "Jordan"
msgstr ""

#: i18n/countries.php:121
msgctxt "ui"
msgid "Kazakhstan"
msgstr ""

#: i18n/countries.php:122
msgctxt "ui"
msgid "Kenya"
msgstr ""

#: i18n/countries.php:123
msgctxt "ui"
msgid "Kiribati"
msgstr ""

#: i18n/countries.php:124
msgctxt "ui"
msgid "Kuwait"
msgstr ""

#: i18n/countries.php:125
msgctxt "ui"
msgid "Kyrgyzstan"
msgstr ""

#: i18n/countries.php:126
msgctxt "ui"
msgid "Laos"
msgstr ""

#: i18n/countries.php:127
msgctxt "ui"
msgid "Latvia"
msgstr ""

#: i18n/countries.php:128
msgctxt "ui"
msgid "Lebanon"
msgstr ""

#: i18n/countries.php:129
msgctxt "ui"
msgid "Lesotho"
msgstr ""

#: i18n/countries.php:130
msgctxt "ui"
msgid "Liberia"
msgstr ""

#: i18n/countries.php:131
msgctxt "ui"
msgid "Libya"
msgstr ""

#: i18n/countries.php:132
msgctxt "ui"
msgid "Liechtenstein"
msgstr ""

#: i18n/countries.php:133
msgctxt "ui"
msgid "Lithuania"
msgstr ""

#: i18n/countries.php:134
msgctxt "ui"
msgid "Luxembourg"
msgstr ""

#: i18n/countries.php:135
msgctxt "ui"
msgid "Macao S.A.R., China"
msgstr ""

#: i18n/countries.php:136
msgctxt "ui"
msgid "Macedonia"
msgstr ""

#: i18n/countries.php:137
msgctxt "ui"
msgid "Madagascar"
msgstr ""

#: i18n/countries.php:138
msgctxt "ui"
msgid "Malawi"
msgstr ""

#: i18n/countries.php:139
msgctxt "ui"
msgid "Malaysia"
msgstr ""

#: i18n/countries.php:140
msgctxt "ui"
msgid "Maldives"
msgstr ""

#: i18n/countries.php:141
msgctxt "ui"
msgid "Mali"
msgstr ""

#: i18n/countries.php:142
msgctxt "ui"
msgid "Malta"
msgstr ""

#: i18n/countries.php:143
msgctxt "ui"
msgid "Marshall Islands"
msgstr ""

#: i18n/countries.php:144
msgctxt "ui"
msgid "Martinique"
msgstr ""

#: i18n/countries.php:145
msgctxt "ui"
msgid "Mauritania"
msgstr ""

#: i18n/countries.php:146
msgctxt "ui"
msgid "Mauritius"
msgstr ""

#: i18n/countries.php:147
msgctxt "ui"
msgid "Mayotte"
msgstr ""

#: i18n/countries.php:148
msgctxt "ui"
msgid "Mexico"
msgstr ""

#: i18n/countries.php:149
msgctxt "ui"
msgid "Micronesia"
msgstr ""

#: i18n/countries.php:150
msgctxt "ui"
msgid "Moldova"
msgstr ""

#: i18n/countries.php:151
msgctxt "ui"
msgid "Monaco"
msgstr ""

#: i18n/countries.php:152
msgctxt "ui"
msgid "Mongolia"
msgstr ""

#: i18n/countries.php:153
msgctxt "ui"
msgid "Montenegro"
msgstr ""

#: i18n/countries.php:154
msgctxt "ui"
msgid "Montserrat"
msgstr ""

#: i18n/countries.php:155
msgctxt "ui"
msgid "Morocco"
msgstr ""

#: i18n/countries.php:156
msgctxt "ui"
msgid "Mozambique"
msgstr ""

#: i18n/countries.php:157
msgctxt "ui"
msgid "Myanmar"
msgstr ""

#: i18n/countries.php:158
msgctxt "ui"
msgid "Namibia"
msgstr ""

#: i18n/countries.php:159
msgctxt "ui"
msgid "Nauru"
msgstr ""

#: i18n/countries.php:160
msgctxt "ui"
msgid "Nepal"
msgstr ""

#: i18n/countries.php:161
msgctxt "ui"
msgid "Netherlands"
msgstr ""

#: i18n/countries.php:162
msgctxt "ui"
msgid "Netherlands Antilles"
msgstr ""

#: i18n/countries.php:163
msgctxt "ui"
msgid "New Caledonia"
msgstr ""

#: i18n/countries.php:164
msgctxt "ui"
msgid "New Zealand"
msgstr ""

#: i18n/countries.php:165
msgctxt "ui"
msgid "Nicaragua"
msgstr ""

#: i18n/countries.php:166
msgctxt "ui"
msgid "Niger"
msgstr ""

#: i18n/countries.php:167
msgctxt "ui"
msgid "Nigeria"
msgstr ""

#: i18n/countries.php:168
msgctxt "ui"
msgid "Niue"
msgstr ""

#: i18n/countries.php:169
msgctxt "ui"
msgid "Norfolk Island"
msgstr ""

#: i18n/countries.php:170
msgctxt "ui"
msgid "North Korea"
msgstr ""

#: i18n/countries.php:171
msgctxt "ui"
msgid "Norway"
msgstr ""

#: i18n/countries.php:172
msgctxt "ui"
msgid "Oman"
msgstr ""

#: i18n/countries.php:173
msgctxt "ui"
msgid "Pakistan"
msgstr ""

#: i18n/countries.php:174
msgctxt "ui"
msgid "Palestinian Territory"
msgstr ""

#: i18n/countries.php:175
msgctxt "ui"
msgid "Panama"
msgstr ""

#: i18n/countries.php:176
msgctxt "ui"
msgid "Papua New Guinea"
msgstr ""

#: i18n/countries.php:177
msgctxt "ui"
msgid "Paraguay"
msgstr ""

#: i18n/countries.php:178
msgctxt "ui"
msgid "Peru"
msgstr ""

#: i18n/countries.php:179
msgctxt "ui"
msgid "Philippines"
msgstr ""

#: i18n/countries.php:180
msgctxt "ui"
msgid "Pitcairn"
msgstr ""

#: i18n/countries.php:181
msgctxt "ui"
msgid "Poland"
msgstr ""

#: i18n/countries.php:182
msgctxt "ui"
msgid "Portugal"
msgstr ""

#: i18n/countries.php:183
#: i18n/states/US.php:65
msgctxt "ui"
msgid "Puerto Rico"
msgstr ""

#: i18n/countries.php:184
msgctxt "ui"
msgid "Qatar"
msgstr ""

#: i18n/countries.php:185
msgctxt "ui"
msgid "Reunion"
msgstr ""

#: i18n/countries.php:186
msgctxt "ui"
msgid "Romania"
msgstr ""

#: i18n/countries.php:187
msgctxt "ui"
msgid "Russia"
msgstr ""

#: i18n/countries.php:188
msgctxt "ui"
msgid "Rwanda"
msgstr ""

#: i18n/countries.php:189
msgctxt "ui"
msgid "Saint Barth&eacute;lemy"
msgstr ""

#: i18n/countries.php:190
msgctxt "ui"
msgid "Saint Helena"
msgstr ""

#: i18n/countries.php:191
msgctxt "ui"
msgid "Saint Kitts and Nevis"
msgstr ""

#: i18n/countries.php:192
msgctxt "ui"
msgid "Saint Lucia"
msgstr ""

#: i18n/countries.php:193
msgctxt "ui"
msgid "Saint Martin (French part)"
msgstr ""

#: i18n/countries.php:194
msgctxt "ui"
msgid "Saint Martin (Dutch part)"
msgstr ""

#: i18n/countries.php:195
msgctxt "ui"
msgid "Saint Pierre and Miquelon"
msgstr ""

#: i18n/countries.php:196
msgctxt "ui"
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: i18n/countries.php:197
msgctxt "ui"
msgid "San Marino"
msgstr ""

#: i18n/countries.php:198
msgctxt "ui"
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr ""

#: i18n/countries.php:199
msgctxt "ui"
msgid "Saudi Arabia"
msgstr ""

#: i18n/countries.php:200
msgctxt "ui"
msgid "Senegal"
msgstr ""

#: i18n/countries.php:201
msgctxt "ui"
msgid "Serbia"
msgstr ""

#: i18n/countries.php:202
msgctxt "ui"
msgid "Seychelles"
msgstr ""

#: i18n/countries.php:203
msgctxt "ui"
msgid "Sierra Leone"
msgstr ""

#: i18n/countries.php:204
msgctxt "ui"
msgid "Singapore"
msgstr ""

#: i18n/countries.php:205
msgctxt "ui"
msgid "Slovakia"
msgstr ""

#: i18n/countries.php:206
msgctxt "ui"
msgid "Slovenia"
msgstr ""

#: i18n/countries.php:207
msgctxt "ui"
msgid "Solomon Islands"
msgstr ""

#: i18n/countries.php:208
msgctxt "ui"
msgid "Somalia"
msgstr ""

#: i18n/countries.php:209
msgctxt "ui"
msgid "South Africa"
msgstr ""

#: i18n/countries.php:210
msgctxt "ui"
msgid "South Georgia/Sandwich Islands"
msgstr ""

#: i18n/countries.php:211
msgctxt "ui"
msgid "South Korea"
msgstr ""

#: i18n/countries.php:212
msgctxt "ui"
msgid "South Sudan"
msgstr ""

#: i18n/countries.php:213
msgctxt "ui"
msgid "Spain"
msgstr ""

#: i18n/countries.php:214
msgctxt "ui"
msgid "Sri Lanka"
msgstr ""

#: i18n/countries.php:215
msgctxt "ui"
msgid "Sudan"
msgstr ""

#: i18n/countries.php:216
msgctxt "ui"
msgid "Suriname"
msgstr ""

#: i18n/countries.php:217
msgctxt "ui"
msgid "Svalbard and Jan Mayen"
msgstr ""

#: i18n/countries.php:218
msgctxt "ui"
msgid "Swaziland"
msgstr ""

#: i18n/countries.php:219
msgctxt "ui"
msgid "Sweden"
msgstr ""

#: i18n/countries.php:220
msgctxt "ui"
msgid "Switzerland"
msgstr ""

#: i18n/countries.php:221
msgctxt "ui"
msgid "Syria"
msgstr ""

#: i18n/countries.php:222
msgctxt "ui"
msgid "Taiwan"
msgstr ""

#: i18n/countries.php:223
msgctxt "ui"
msgid "Tajikistan"
msgstr ""

#: i18n/countries.php:224
msgctxt "ui"
msgid "Tanzania"
msgstr ""

#: i18n/countries.php:225
msgctxt "ui"
msgid "Thailand"
msgstr ""

#: i18n/countries.php:226
msgctxt "ui"
msgid "Timor-Leste"
msgstr ""

#: i18n/countries.php:227
msgctxt "ui"
msgid "Togo"
msgstr ""

#: i18n/countries.php:228
msgctxt "ui"
msgid "Tokelau"
msgstr ""

#: i18n/countries.php:229
msgctxt "ui"
msgid "Tonga"
msgstr ""

#: i18n/countries.php:230
msgctxt "ui"
msgid "Trinidad and Tobago"
msgstr ""

#: i18n/countries.php:231
msgctxt "ui"
msgid "Tunisia"
msgstr ""

#: i18n/countries.php:232
msgctxt "ui"
msgid "Turkey"
msgstr ""

#: i18n/countries.php:233
msgctxt "ui"
msgid "Turkmenistan"
msgstr ""

#: i18n/countries.php:234
msgctxt "ui"
msgid "Turks and Caicos Islands"
msgstr ""

#: i18n/countries.php:235
msgctxt "ui"
msgid "Tuvalu"
msgstr ""

#: i18n/countries.php:236
msgctxt "ui"
msgid "Uganda"
msgstr ""

#: i18n/countries.php:237
msgctxt "ui"
msgid "Ukraine"
msgstr ""

#: i18n/countries.php:238
msgctxt "ui"
msgid "United Arab Emirates"
msgstr ""

#: i18n/countries.php:239
msgctxt "ui"
msgid "United Kingdom (UK)"
msgstr ""

#: i18n/countries.php:240
msgctxt "ui"
msgid "United States (US)"
msgstr ""

#: i18n/countries.php:241
msgctxt "ui"
msgid "Uruguay"
msgstr ""

#: i18n/countries.php:242
msgctxt "ui"
msgid "Uzbekistan"
msgstr ""

#: i18n/countries.php:243
msgctxt "ui"
msgid "Vanuatu"
msgstr ""

#: i18n/countries.php:244
msgctxt "ui"
msgid "Vatican"
msgstr ""

#: i18n/countries.php:245
msgctxt "ui"
msgid "Venezuela"
msgstr ""

#: i18n/countries.php:246
msgctxt "ui"
msgid "Vietnam"
msgstr ""

#: i18n/countries.php:247
msgctxt "ui"
msgid "Wallis and Futuna"
msgstr ""

#: i18n/countries.php:248
msgctxt "ui"
msgid "Western Sahara"
msgstr ""

#: i18n/countries.php:249
msgctxt "ui"
msgid "Western Samoa"
msgstr ""

#: i18n/countries.php:250
msgctxt "ui"
msgid "Yemen"
msgstr ""

#: i18n/countries.php:251
msgctxt "ui"
msgid "Zambia"
msgstr ""

#: i18n/countries.php:252
msgctxt "ui"
msgid "Zimbabwe"
msgstr ""

#: i18n/states/AT.php:8
msgctxt "ui"
msgid "Burgenland"
msgstr ""

#: i18n/states/AT.php:9
msgctxt "ui"
msgid "Kärnten"
msgstr ""

#: i18n/states/AT.php:10
msgctxt "ui"
msgid "Niederösterreich"
msgstr ""

#: i18n/states/AT.php:11
msgctxt "ui"
msgid "Oberösterreich"
msgstr ""

#: i18n/states/AT.php:12
msgctxt "ui"
msgid "Salzburg"
msgstr ""

#: i18n/states/AT.php:13
msgctxt "ui"
msgid "Steiermark"
msgstr ""

#: i18n/states/AT.php:14
msgctxt "ui"
msgid "Tirol"
msgstr ""

#: i18n/states/AT.php:15
msgctxt "ui"
msgid "Vorarlberg"
msgstr ""

#: i18n/states/AT.php:16
msgctxt "ui"
msgid "Wien"
msgstr ""

#: i18n/states/AU.php:8
msgctxt "ui"
msgid "Australian Capital Territory"
msgstr ""

#: i18n/states/AU.php:9
msgctxt "ui"
msgid "New South Wales"
msgstr ""

#: i18n/states/AU.php:10
msgctxt "ui"
msgid "Northern Territory"
msgstr ""

#: i18n/states/AU.php:11
msgctxt "ui"
msgid "Queensland"
msgstr ""

#: i18n/states/AU.php:12
msgctxt "ui"
msgid "South Australia"
msgstr ""

#: i18n/states/AU.php:13
msgctxt "ui"
msgid "Tasmania"
msgstr ""

#: i18n/states/AU.php:14
msgctxt "ui"
msgid "Victoria"
msgstr ""

#: i18n/states/AU.php:15
msgctxt "ui"
msgid "Western Australia"
msgstr ""

#: i18n/states/BD.php:8
msgctxt "ui"
msgid "Bagerhat"
msgstr ""

#: i18n/states/BD.php:9
msgctxt "ui"
msgid "Bandarban"
msgstr ""

#: i18n/states/BD.php:10
msgctxt "ui"
msgid "Barguna"
msgstr ""

#: i18n/states/BD.php:11
msgctxt "ui"
msgid "Barisal"
msgstr ""

#: i18n/states/BD.php:12
msgctxt "ui"
msgid "Bhola"
msgstr ""

#: i18n/states/BD.php:13
msgctxt "ui"
msgid "Bogra"
msgstr ""

#: i18n/states/BD.php:14
msgctxt "ui"
msgid "Brahmanbaria"
msgstr ""

#: i18n/states/BD.php:15
msgctxt "ui"
msgid "Chandpur"
msgstr ""

#: i18n/states/BD.php:16
msgctxt "ui"
msgid "Chittagong"
msgstr ""

#: i18n/states/BD.php:17
msgctxt "ui"
msgid "Chuadanga"
msgstr ""

#: i18n/states/BD.php:18
msgctxt "ui"
msgid "Comilla"
msgstr ""

#: i18n/states/BD.php:19
msgctxt "ui"
msgid "Cox's Bazar"
msgstr ""

#: i18n/states/BD.php:20
msgctxt "ui"
msgid "Dhaka"
msgstr ""

#: i18n/states/BD.php:21
msgctxt "ui"
msgid "Dinajpur"
msgstr ""

#: i18n/states/BD.php:22
msgctxt "ui"
msgid "Faridpur "
msgstr ""

#: i18n/states/BD.php:23
msgctxt "ui"
msgid "Feni"
msgstr ""

#: i18n/states/BD.php:24
msgctxt "ui"
msgid "Gaibandha"
msgstr ""

#: i18n/states/BD.php:25
msgctxt "ui"
msgid "Gazipur"
msgstr ""

#: i18n/states/BD.php:26
msgctxt "ui"
msgid "Gopalganj"
msgstr ""

#: i18n/states/BD.php:27
msgctxt "ui"
msgid "Habiganj"
msgstr ""

#: i18n/states/BD.php:28
msgctxt "ui"
msgid "Jamalpur"
msgstr ""

#: i18n/states/BD.php:29
msgctxt "ui"
msgid "Jessore"
msgstr ""

#: i18n/states/BD.php:30
msgctxt "ui"
msgid "Jhalokati"
msgstr ""

#: i18n/states/BD.php:31
msgctxt "ui"
msgid "Jhenaidah"
msgstr ""

#: i18n/states/BD.php:32
msgctxt "ui"
msgid "Joypurhat"
msgstr ""

#: i18n/states/BD.php:33
msgctxt "ui"
msgid "Khagrachhari"
msgstr ""

#: i18n/states/BD.php:34
msgctxt "ui"
msgid "Khulna"
msgstr ""

#: i18n/states/BD.php:35
msgctxt "ui"
msgid "Kishoreganj"
msgstr ""

#: i18n/states/BD.php:36
msgctxt "ui"
msgid "Kurigram"
msgstr ""

#: i18n/states/BD.php:37
msgctxt "ui"
msgid "Kushtia"
msgstr ""

#: i18n/states/BD.php:38
msgctxt "ui"
msgid "Lakshmipur"
msgstr ""

#: i18n/states/BD.php:39
msgctxt "ui"
msgid "Lalmonirhat"
msgstr ""

#: i18n/states/BD.php:40
msgctxt "ui"
msgid "Madaripur"
msgstr ""

#: i18n/states/BD.php:41
msgctxt "ui"
msgid "Magura"
msgstr ""

#: i18n/states/BD.php:42
msgctxt "ui"
msgid "Manikganj "
msgstr ""

#: i18n/states/BD.php:43
msgctxt "ui"
msgid "Meherpur"
msgstr ""

#: i18n/states/BD.php:44
msgctxt "ui"
msgid "Moulvibazar"
msgstr ""

#: i18n/states/BD.php:45
msgctxt "ui"
msgid "Munshiganj"
msgstr ""

#: i18n/states/BD.php:46
msgctxt "ui"
msgid "Mymensingh"
msgstr ""

#: i18n/states/BD.php:47
msgctxt "ui"
msgid "Naogaon"
msgstr ""

#: i18n/states/BD.php:48
msgctxt "ui"
msgid "Narail"
msgstr ""

#: i18n/states/BD.php:49
msgctxt "ui"
msgid "Narayanganj"
msgstr ""

#: i18n/states/BD.php:50
msgctxt "ui"
msgid "Narsingdi"
msgstr ""

#: i18n/states/BD.php:51
msgctxt "ui"
msgid "Natore"
msgstr ""

#: i18n/states/BD.php:52
msgctxt "ui"
msgid "Nawabganj"
msgstr ""

#: i18n/states/BD.php:53
msgctxt "ui"
msgid "Netrakona"
msgstr ""

#: i18n/states/BD.php:54
msgctxt "ui"
msgid "Nilphamari"
msgstr ""

#: i18n/states/BD.php:55
msgctxt "ui"
msgid "Noakhali"
msgstr ""

#: i18n/states/BD.php:56
msgctxt "ui"
msgid "Pabna"
msgstr ""

#: i18n/states/BD.php:57
msgctxt "ui"
msgid "Panchagarh"
msgstr ""

#: i18n/states/BD.php:58
msgctxt "ui"
msgid "Patuakhali"
msgstr ""

#: i18n/states/BD.php:59
msgctxt "ui"
msgid "Pirojpur"
msgstr ""

#: i18n/states/BD.php:60
msgctxt "ui"
msgid "Rajbari"
msgstr ""

#: i18n/states/BD.php:61
msgctxt "ui"
msgid "Rajshahi"
msgstr ""

#: i18n/states/BD.php:62
msgctxt "ui"
msgid "Rangamati"
msgstr ""

#: i18n/states/BD.php:63
msgctxt "ui"
msgid "Rangpur"
msgstr ""

#: i18n/states/BD.php:64
msgctxt "ui"
msgid "Satkhira"
msgstr ""

#: i18n/states/BD.php:65
msgctxt "ui"
msgid "Shariatpur"
msgstr ""

#: i18n/states/BD.php:66
msgctxt "ui"
msgid "Sherpur"
msgstr ""

#: i18n/states/BD.php:67
msgctxt "ui"
msgid "Sirajganj"
msgstr ""

#: i18n/states/BD.php:68
msgctxt "ui"
msgid "Sunamganj"
msgstr ""

#: i18n/states/BD.php:69
msgctxt "ui"
msgid "Sylhet"
msgstr ""

#: i18n/states/BD.php:70
msgctxt "ui"
msgid "Tangail"
msgstr ""

#: i18n/states/BD.php:71
msgctxt "ui"
msgid "Thakurgaon"
msgstr ""

#: i18n/states/BG.php:8
msgctxt "ui"
msgid "Blagoevgrad"
msgstr ""

#: i18n/states/BG.php:9
msgctxt "ui"
msgid "Burgas"
msgstr ""

#: i18n/states/BG.php:10
msgctxt "ui"
msgid "Dobrich"
msgstr ""

#: i18n/states/BG.php:11
msgctxt "ui"
msgid "Gabrovo"
msgstr ""

#: i18n/states/BG.php:12
msgctxt "ui"
msgid "Haskovo"
msgstr ""

#: i18n/states/BG.php:13
msgctxt "ui"
msgid "Kardzhali"
msgstr ""

#: i18n/states/BG.php:14
msgctxt "ui"
msgid "Kyustendil"
msgstr ""

#: i18n/states/BG.php:15
msgctxt "ui"
msgid "Lovech"
msgstr ""

#: i18n/states/BG.php:16
#: i18n/states/US.php:34
msgctxt "ui"
msgid "Montana"
msgstr ""

#: i18n/states/BG.php:17
msgctxt "ui"
msgid "Pazardzhik"
msgstr ""

#: i18n/states/BG.php:18
msgctxt "ui"
msgid "Pernik"
msgstr ""

#: i18n/states/BG.php:19
msgctxt "ui"
msgid "Pleven"
msgstr ""

#: i18n/states/BG.php:20
msgctxt "ui"
msgid "Plovdiv"
msgstr ""

#: i18n/states/BG.php:21
msgctxt "ui"
msgid "Razgrad"
msgstr ""

#: i18n/states/BG.php:22
msgctxt "ui"
msgid "Ruse"
msgstr ""

#: i18n/states/BG.php:23
msgctxt "ui"
msgid "Shumen"
msgstr ""

#: i18n/states/BG.php:24
msgctxt "ui"
msgid "Silistra"
msgstr ""

#: i18n/states/BG.php:25
msgctxt "ui"
msgid "Sliven"
msgstr ""

#: i18n/states/BG.php:26
msgctxt "ui"
msgid "Smolyan"
msgstr ""

#: i18n/states/BG.php:27
msgctxt "ui"
msgid "Sofia"
msgstr ""

#: i18n/states/BG.php:28
msgctxt "ui"
msgid "Sofia-Grad"
msgstr ""

#: i18n/states/BG.php:29
msgctxt "ui"
msgid "Stara Zagora"
msgstr ""

#: i18n/states/BG.php:30
msgctxt "ui"
msgid "Targovishte"
msgstr ""

#: i18n/states/BG.php:31
msgctxt "ui"
msgid "Varna"
msgstr ""

#: i18n/states/BG.php:32
msgctxt "ui"
msgid "Veliko Tarnovo"
msgstr ""

#: i18n/states/BG.php:33
msgctxt "ui"
msgid "Vidin"
msgstr ""

#: i18n/states/BG.php:34
msgctxt "ui"
msgid "Vratsa"
msgstr ""

#: i18n/states/BG.php:35
msgctxt "ui"
msgid "Yambol"
msgstr ""

#: i18n/states/BR.php:8
msgctxt "ui"
msgid "Acre"
msgstr ""

#: i18n/states/BR.php:9
msgctxt "ui"
msgid "Alagoas"
msgstr ""

#: i18n/states/BR.php:10
msgctxt "ui"
msgid "Amap&aacute;"
msgstr ""

#: i18n/states/BR.php:11
#: i18n/states/PE.php:8
msgctxt "ui"
msgid "Amazonas"
msgstr ""

#: i18n/states/BR.php:12
msgctxt "ui"
msgid "Bahia"
msgstr ""

#: i18n/states/BR.php:13
msgctxt "ui"
msgid "Cear&aacute;"
msgstr ""

#: i18n/states/BR.php:14
msgctxt "ui"
msgid "Distrito Federal"
msgstr ""

#: i18n/states/BR.php:15
msgctxt "ui"
msgid "Esp&iacute;rito Santo"
msgstr ""

#: i18n/states/BR.php:16
msgctxt "ui"
msgid "Goi&aacute;s"
msgstr ""

#: i18n/states/BR.php:17
msgctxt "ui"
msgid "Maranh&atilde;o"
msgstr ""

#: i18n/states/BR.php:18
msgctxt "ui"
msgid "Mato Grosso"
msgstr ""

#: i18n/states/BR.php:19
msgctxt "ui"
msgid "Mato Grosso do Sul"
msgstr ""

#: i18n/states/BR.php:20
msgctxt "ui"
msgid "Minas Gerais"
msgstr ""

#: i18n/states/BR.php:21
msgctxt "ui"
msgid "Par&aacute;"
msgstr ""

#: i18n/states/BR.php:22
msgctxt "ui"
msgid "Para&iacute;ba"
msgstr ""

#: i18n/states/BR.php:23
msgctxt "ui"
msgid "Paran&aacute;"
msgstr ""

#: i18n/states/BR.php:24
msgctxt "ui"
msgid "Pernambuco"
msgstr ""

#: i18n/states/BR.php:25
msgctxt "ui"
msgid "Piau&iacute;"
msgstr ""

#: i18n/states/BR.php:26
msgctxt "ui"
msgid "Rio de Janeiro"
msgstr ""

#: i18n/states/BR.php:27
msgctxt "ui"
msgid "Rio Grande do Norte"
msgstr ""

#: i18n/states/BR.php:28
msgctxt "ui"
msgid "Rio Grande do Sul"
msgstr ""

#: i18n/states/BR.php:29
msgctxt "ui"
msgid "Rond&ocirc;nia"
msgstr ""

#: i18n/states/BR.php:30
msgctxt "ui"
msgid "Roraima"
msgstr ""

#: i18n/states/BR.php:31
msgctxt "ui"
msgid "Santa Catarina"
msgstr ""

#: i18n/states/BR.php:32
msgctxt "ui"
msgid "S&atilde;o Paulo"
msgstr ""

#: i18n/states/BR.php:33
msgctxt "ui"
msgid "Sergipe"
msgstr ""

#: i18n/states/BR.php:34
msgctxt "ui"
msgid "Tocantins"
msgstr ""

#: i18n/states/CA.php:8
msgctxt "ui"
msgid "Alberta"
msgstr ""

#: i18n/states/CA.php:9
msgctxt "ui"
msgid "British Columbia"
msgstr ""

#: i18n/states/CA.php:10
msgctxt "ui"
msgid "Manitoba"
msgstr ""

#: i18n/states/CA.php:11
msgctxt "ui"
msgid "New Brunswick"
msgstr ""

#: i18n/states/CA.php:12
msgctxt "ui"
msgid "Newfoundland"
msgstr ""

#: i18n/states/CA.php:13
msgctxt "ui"
msgid "Northwest Territories"
msgstr ""

#: i18n/states/CA.php:14
msgctxt "ui"
msgid "Nova Scotia"
msgstr ""

#: i18n/states/CA.php:15
msgctxt "ui"
msgid "Nunavut"
msgstr ""

#: i18n/states/CA.php:16
msgctxt "ui"
msgid "Ontario"
msgstr ""

#: i18n/states/CA.php:17
msgctxt "ui"
msgid "Prince Edward Island"
msgstr ""

#: i18n/states/CA.php:18
msgctxt "ui"
msgid "Quebec"
msgstr ""

#: i18n/states/CA.php:19
msgctxt "ui"
msgid "Saskatchewan"
msgstr ""

#: i18n/states/CA.php:20
msgctxt "ui"
msgid "Yukon Territory"
msgstr ""

#: i18n/states/CH.php:8
msgctxt "ui"
msgid "Aargau"
msgstr ""

#: i18n/states/CH.php:9
msgctxt "ui"
msgid "Appenzell Ausserrhoden"
msgstr ""

#: i18n/states/CH.php:10
msgctxt "ui"
msgid "Appenzell Innerrhoden"
msgstr ""

#: i18n/states/CH.php:11
msgctxt "ui"
msgid "Basel-Landschaft"
msgstr ""

#: i18n/states/CH.php:12
msgctxt "ui"
msgid "Basel-Stadt"
msgstr ""

#: i18n/states/CH.php:13
msgctxt "ui"
msgid "Bern"
msgstr ""

#: i18n/states/CH.php:14
msgctxt "ui"
msgid "Freiburg"
msgstr ""

#: i18n/states/CH.php:15
msgctxt "ui"
msgid "Genève"
msgstr ""

#: i18n/states/CH.php:16
msgctxt "ui"
msgid "Glarus"
msgstr ""

#: i18n/states/CH.php:17
msgctxt "ui"
msgid "Graubünden"
msgstr ""

#: i18n/states/CH.php:18
msgctxt "ui"
msgid "Jura"
msgstr ""

#: i18n/states/CH.php:19
msgctxt "ui"
msgid "Luzern"
msgstr ""

#: i18n/states/CH.php:20
msgctxt "ui"
msgid "Neuchâtel"
msgstr ""

#: i18n/states/CH.php:21
msgctxt "ui"
msgid "Nidwalden"
msgstr ""

#: i18n/states/CH.php:22
msgctxt "ui"
msgid "Obwalden"
msgstr ""

#: i18n/states/CH.php:23
msgctxt "ui"
msgid "Schaffhausen"
msgstr ""

#: i18n/states/CH.php:24
msgctxt "ui"
msgid "Schwyz"
msgstr ""

#: i18n/states/CH.php:25
msgctxt "ui"
msgid "Solothurn"
msgstr ""

#: i18n/states/CH.php:26
msgctxt "ui"
msgid "St. Gallen"
msgstr ""

#: i18n/states/CH.php:27
msgctxt "ui"
msgid "Thurgau"
msgstr ""

#: i18n/states/CH.php:28
msgctxt "ui"
msgid "Ticino"
msgstr ""

#: i18n/states/CH.php:29
msgctxt "ui"
msgid "Uri"
msgstr ""

#: i18n/states/CH.php:30
msgctxt "ui"
msgid "Valais"
msgstr ""

#: i18n/states/CH.php:31
msgctxt "ui"
msgid "Vaud"
msgstr ""

#: i18n/states/CH.php:32
msgctxt "ui"
msgid "Zug"
msgstr ""

#: i18n/states/CH.php:33
msgctxt "ui"
msgid "Zürich"
msgstr ""

#: i18n/states/CN.php:8
msgctxt "ui"
msgid "Yunnan / &#20113;&#21335;"
msgstr ""

#: i18n/states/CN.php:9
msgctxt "ui"
msgid "Beijing / &#21271;&#20140;"
msgstr ""

#: i18n/states/CN.php:10
msgctxt "ui"
msgid "Tianjin / &#22825;&#27941;"
msgstr ""

#: i18n/states/CN.php:11
msgctxt "ui"
msgid "Hebei / &#27827;&#21271;"
msgstr ""

#: i18n/states/CN.php:12
msgctxt "ui"
msgid "Shanxi / &#23665;&#35199;"
msgstr ""

#: i18n/states/CN.php:13
msgctxt "ui"
msgid "Inner Mongolia / &#20839;&#33945;&#21476;"
msgstr ""

#: i18n/states/CN.php:14
msgctxt "ui"
msgid "Liaoning / &#36797;&#23425;"
msgstr ""

#: i18n/states/CN.php:15
msgctxt "ui"
msgid "Jilin / &#21513;&#26519;"
msgstr ""

#: i18n/states/CN.php:16
msgctxt "ui"
msgid "Heilongjiang / &#40657;&#40857;&#27743;"
msgstr ""

#: i18n/states/CN.php:17
msgctxt "ui"
msgid "Shanghai / &#19978;&#28023;"
msgstr ""

#: i18n/states/CN.php:18
msgctxt "ui"
msgid "Jiangsu / &#27743;&#33487;"
msgstr ""

#: i18n/states/CN.php:19
msgctxt "ui"
msgid "Zhejiang / &#27993;&#27743;"
msgstr ""

#: i18n/states/CN.php:20
msgctxt "ui"
msgid "Anhui / &#23433;&#24509;"
msgstr ""

#: i18n/states/CN.php:21
msgctxt "ui"
msgid "Fujian / &#31119;&#24314;"
msgstr ""

#: i18n/states/CN.php:22
msgctxt "ui"
msgid "Jiangxi / &#27743;&#35199;"
msgstr ""

#: i18n/states/CN.php:23
msgctxt "ui"
msgid "Shandong / &#23665;&#19996;"
msgstr ""

#: i18n/states/CN.php:24
msgctxt "ui"
msgid "Henan / &#27827;&#21335;"
msgstr ""

#: i18n/states/CN.php:25
msgctxt "ui"
msgid "Hubei / &#28246;&#21271;"
msgstr ""

#: i18n/states/CN.php:26
msgctxt "ui"
msgid "Hunan / &#28246;&#21335;"
msgstr ""

#: i18n/states/CN.php:27
msgctxt "ui"
msgid "Guangdong / &#24191;&#19996;"
msgstr ""

#: i18n/states/CN.php:28
msgctxt "ui"
msgid "Guangxi Zhuang / &#24191;&#35199;&#22766;&#26063;"
msgstr ""

#: i18n/states/CN.php:29
msgctxt "ui"
msgid "Hainan / &#28023;&#21335;"
msgstr ""

#: i18n/states/CN.php:30
msgctxt "ui"
msgid "Chongqing / &#37325;&#24198;"
msgstr ""

#: i18n/states/CN.php:31
msgctxt "ui"
msgid "Sichuan / &#22235;&#24029;"
msgstr ""

#: i18n/states/CN.php:32
msgctxt "ui"
msgid "Guizhou / &#36149;&#24030;"
msgstr ""

#: i18n/states/CN.php:33
msgctxt "ui"
msgid "Shaanxi / &#38485;&#35199;"
msgstr ""

#: i18n/states/CN.php:34
msgctxt "ui"
msgid "Gansu / &#29976;&#32899;"
msgstr ""

#: i18n/states/CN.php:35
msgctxt "ui"
msgid "Qinghai / &#38738;&#28023;"
msgstr ""

#: i18n/states/CN.php:36
msgctxt "ui"
msgid "Ningxia Hui / &#23425;&#22799;"
msgstr ""

#: i18n/states/CN.php:37
msgctxt "ui"
msgid "Macau / &#28595;&#38376;"
msgstr ""

#: i18n/states/CN.php:38
msgctxt "ui"
msgid "Tibet / &#35199;&#34255;"
msgstr ""

#: i18n/states/CN.php:39
msgctxt "ui"
msgid "Xinjiang / &#26032;&#30086;"
msgstr ""

#: i18n/states/DE.php:8
msgid "Baden-Württemberg"
msgstr ""

#: i18n/states/DE.php:9
msgid "Bavaria"
msgstr ""

#: i18n/states/DE.php:10
msgid "Berlin"
msgstr ""

#: i18n/states/DE.php:11
msgid "Brandenburg"
msgstr ""

#: i18n/states/DE.php:12
msgid "Bremen"
msgstr ""

#: i18n/states/DE.php:13
msgid "Hamburg"
msgstr ""

#: i18n/states/DE.php:14
msgid "Hesse"
msgstr ""

#: i18n/states/DE.php:15
msgid "Lower Saxony"
msgstr ""

#: i18n/states/DE.php:16
msgid "Mecklenburg-Vorpommern"
msgstr ""

#: i18n/states/DE.php:17
msgid "North Rhine-Westphalia"
msgstr ""

#: i18n/states/DE.php:18
msgid "Rhineland-Palatinate"
msgstr ""

#: i18n/states/DE.php:19
msgid "Saarland"
msgstr ""

#: i18n/states/DE.php:20
msgid "Saxony"
msgstr ""

#: i18n/states/DE.php:21
msgid "Saxony-Anhalt"
msgstr ""

#: i18n/states/DE.php:22
msgid "Schleswig-Holstein"
msgstr ""

#: i18n/states/DE.php:23
msgid "Thuringia"
msgstr ""

#: i18n/states/ES.php:8
msgctxt "ui"
msgid "A Coru&ntilde;a"
msgstr ""

#: i18n/states/ES.php:9
msgctxt "ui"
msgid "Araba/&Aacute;lava"
msgstr ""

#: i18n/states/ES.php:10
msgctxt "ui"
msgid "Albacete"
msgstr ""

#: i18n/states/ES.php:11
msgctxt "ui"
msgid "Alicante"
msgstr ""

#: i18n/states/ES.php:12
msgctxt "ui"
msgid "Almer&iacute;a"
msgstr ""

#: i18n/states/ES.php:13
msgctxt "ui"
msgid "Asturias"
msgstr ""

#: i18n/states/ES.php:14
msgctxt "ui"
msgid "&Aacute;vila"
msgstr ""

#: i18n/states/ES.php:15
msgctxt "ui"
msgid "Badajoz"
msgstr ""

#: i18n/states/ES.php:16
msgctxt "ui"
msgid "Baleares"
msgstr ""

#: i18n/states/ES.php:17
msgctxt "ui"
msgid "Barcelona"
msgstr ""

#: i18n/states/ES.php:18
msgctxt "ui"
msgid "Burgos"
msgstr ""

#: i18n/states/ES.php:19
msgctxt "ui"
msgid "C&aacute;ceres"
msgstr ""

#: i18n/states/ES.php:20
msgctxt "ui"
msgid "C&aacute;diz"
msgstr ""

#: i18n/states/ES.php:21
msgctxt "ui"
msgid "Cantabria"
msgstr ""

#: i18n/states/ES.php:22
msgctxt "ui"
msgid "Castell&oacute;n"
msgstr ""

#: i18n/states/ES.php:23
msgctxt "ui"
msgid "Ceuta"
msgstr ""

#: i18n/states/ES.php:24
msgctxt "ui"
msgid "Ciudad Real"
msgstr ""

#: i18n/states/ES.php:25
msgctxt "ui"
msgid "C&oacute;rdoba"
msgstr ""

#: i18n/states/ES.php:26
msgctxt "ui"
msgid "Cuenca"
msgstr ""

#: i18n/states/ES.php:27
msgctxt "ui"
msgid "Girona"
msgstr ""

#: i18n/states/ES.php:28
msgctxt "ui"
msgid "Granada"
msgstr ""

#: i18n/states/ES.php:29
msgctxt "ui"
msgid "Guadalajara"
msgstr ""

#: i18n/states/ES.php:30
msgctxt "ui"
msgid "Gipuzkoa"
msgstr ""

#: i18n/states/ES.php:31
msgctxt "ui"
msgid "Huelva"
msgstr ""

#: i18n/states/ES.php:32
msgctxt "ui"
msgid "Huesca"
msgstr ""

#: i18n/states/ES.php:33
msgctxt "ui"
msgid "Ja&eacute;n"
msgstr ""

#: i18n/states/ES.php:34
msgctxt "ui"
msgid "La Rioja"
msgstr ""

#: i18n/states/ES.php:35
msgctxt "ui"
msgid "Las Palmas"
msgstr ""

#: i18n/states/ES.php:36
msgctxt "ui"
msgid "Le&oacute;n"
msgstr ""

#: i18n/states/ES.php:37
msgctxt "ui"
msgid "Lleida"
msgstr ""

#: i18n/states/ES.php:38
msgctxt "ui"
msgid "Lugo"
msgstr ""

#: i18n/states/ES.php:39
msgctxt "ui"
msgid "Madrid"
msgstr ""

#: i18n/states/ES.php:40
msgctxt "ui"
msgid "M&aacute;laga"
msgstr ""

#: i18n/states/ES.php:41
msgctxt "ui"
msgid "Melilla"
msgstr ""

#: i18n/states/ES.php:42
msgctxt "ui"
msgid "Murcia"
msgstr ""

#: i18n/states/ES.php:43
msgctxt "ui"
msgid "Navarra"
msgstr ""

#: i18n/states/ES.php:44
msgctxt "ui"
msgid "Ourense"
msgstr ""

#: i18n/states/ES.php:45
msgctxt "ui"
msgid "Palencia"
msgstr ""

#: i18n/states/ES.php:46
msgctxt "ui"
msgid "Pontevedra"
msgstr ""

#: i18n/states/ES.php:47
msgctxt "ui"
msgid "Salamanca"
msgstr ""

#: i18n/states/ES.php:48
msgctxt "ui"
msgid "Santa Cruz de Tenerife"
msgstr ""

#: i18n/states/ES.php:49
msgctxt "ui"
msgid "Segovia"
msgstr ""

#: i18n/states/ES.php:50
msgctxt "ui"
msgid "Sevilla"
msgstr ""

#: i18n/states/ES.php:51
msgctxt "ui"
msgid "Soria"
msgstr ""

#: i18n/states/ES.php:52
msgctxt "ui"
msgid "Tarragona"
msgstr ""

#: i18n/states/ES.php:53
msgctxt "ui"
msgid "Teruel"
msgstr ""

#: i18n/states/ES.php:54
msgctxt "ui"
msgid "Toledo"
msgstr ""

#: i18n/states/ES.php:55
msgctxt "ui"
msgid "Valencia"
msgstr ""

#: i18n/states/ES.php:56
msgctxt "ui"
msgid "Valladolid"
msgstr ""

#: i18n/states/ES.php:57
msgctxt "ui"
msgid "Bizkaia"
msgstr ""

#: i18n/states/ES.php:58
msgctxt "ui"
msgid "Zamora"
msgstr ""

#: i18n/states/ES.php:59
msgctxt "ui"
msgid "Zaragoza"
msgstr ""

#: i18n/states/HK.php:8
msgctxt "ui"
msgid "Hong Kong Island"
msgstr ""

#: i18n/states/HK.php:9
msgctxt "ui"
msgid "Kowloon"
msgstr ""

#: i18n/states/HK.php:10
msgctxt "ui"
msgid "New Territories"
msgstr ""

#: i18n/states/HU.php:8
msgctxt "ui"
msgid "Bács-Kiskun"
msgstr ""

#: i18n/states/HU.php:9
msgctxt "ui"
msgid "Békés"
msgstr ""

#: i18n/states/HU.php:10
msgctxt "ui"
msgid "Baranya"
msgstr ""

#: i18n/states/HU.php:11
msgctxt "ui"
msgid "Borsod-Abaúj-Zemplén"
msgstr ""

#: i18n/states/HU.php:12
msgctxt "ui"
msgid "Budapest"
msgstr ""

#: i18n/states/HU.php:13
msgctxt "ui"
msgid "Csongrád"
msgstr ""

#: i18n/states/HU.php:14
msgctxt "ui"
msgid "Fejér"
msgstr ""

#: i18n/states/HU.php:15
msgctxt "ui"
msgid "Győr-Moson-Sopron"
msgstr ""

#: i18n/states/HU.php:16
msgctxt "ui"
msgid "Hajdú-Bihar"
msgstr ""

#: i18n/states/HU.php:17
msgctxt "ui"
msgid "Heves"
msgstr ""

#: i18n/states/HU.php:18
msgctxt "ui"
msgid "Jász-Nagykun-Szolnok"
msgstr ""

#: i18n/states/HU.php:19
msgctxt "ui"
msgid "Komárom-Esztergom"
msgstr ""

#: i18n/states/HU.php:20
msgctxt "ui"
msgid "Nógrád"
msgstr ""

#: i18n/states/HU.php:21
msgctxt "ui"
msgid "Pest"
msgstr ""

#: i18n/states/HU.php:22
msgctxt "ui"
msgid "Somogy"
msgstr ""

#: i18n/states/HU.php:23
msgctxt "ui"
msgid "Szabolcs-Szatmár-Bereg"
msgstr ""

#: i18n/states/HU.php:24
msgctxt "ui"
msgid "Tolna"
msgstr ""

#: i18n/states/HU.php:25
msgctxt "ui"
msgid "Vas"
msgstr ""

#: i18n/states/HU.php:26
msgctxt "ui"
msgid "Veszprém"
msgstr ""

#: i18n/states/HU.php:27
msgctxt "ui"
msgid "Zala"
msgstr ""

#: i18n/states/ID.php:8
msgctxt "ui"
msgid "Bali"
msgstr ""

#: i18n/states/ID.php:9
msgctxt "ui"
msgid "Bangka Belitung"
msgstr ""

#: i18n/states/ID.php:10
msgctxt "ui"
msgid "Banten"
msgstr ""

#: i18n/states/ID.php:11
msgctxt "ui"
msgid "Bengkulu"
msgstr ""

#: i18n/states/ID.php:12
msgctxt "ui"
msgid "Daerah Istimewa Aceh"
msgstr ""

#: i18n/states/ID.php:13
msgctxt "ui"
msgid "Daerah Istimewa Yogyakarta"
msgstr ""

#: i18n/states/ID.php:14
msgctxt "ui"
msgid "DKI Jakarta"
msgstr ""

#: i18n/states/ID.php:15
msgctxt "ui"
msgid "Gorontalo"
msgstr ""

#: i18n/states/ID.php:16
msgctxt "ui"
msgid "Jambi"
msgstr ""

#: i18n/states/ID.php:17
msgctxt "ui"
msgid "Jawa Barat"
msgstr ""

#: i18n/states/ID.php:18
msgctxt "ui"
msgid "Jawa Tengah"
msgstr ""

#: i18n/states/ID.php:19
msgctxt "ui"
msgid "Jawa Timur"
msgstr ""

#: i18n/states/ID.php:20
msgctxt "ui"
msgid "Kalimantan Barat"
msgstr ""

#: i18n/states/ID.php:21
msgctxt "ui"
msgid "Kalimantan Selatan"
msgstr ""

#: i18n/states/ID.php:22
msgctxt "ui"
msgid "Kalimantan Tengah"
msgstr ""

#: i18n/states/ID.php:23
msgctxt "ui"
msgid "Kalimantan Timur"
msgstr ""

#: i18n/states/ID.php:24
msgctxt "ui"
msgid "Kalimantan Utara"
msgstr ""

#: i18n/states/ID.php:25
msgctxt "ui"
msgid "Kepulauan Riau"
msgstr ""

#: i18n/states/ID.php:26
msgctxt "ui"
msgid "Lampung"
msgstr ""

#: i18n/states/ID.php:27
msgctxt "ui"
msgid "Maluku"
msgstr ""

#: i18n/states/ID.php:28
msgctxt "ui"
msgid "Maluku Utara"
msgstr ""

#: i18n/states/ID.php:29
msgctxt "ui"
msgid "Nusa Tenggara Barat"
msgstr ""

#: i18n/states/ID.php:30
msgctxt "ui"
msgid "Nusa Tenggara Timur"
msgstr ""

#: i18n/states/ID.php:31
msgctxt "ui"
msgid "Papua"
msgstr ""

#: i18n/states/ID.php:32
msgctxt "ui"
msgid "Papua Barat"
msgstr ""

#: i18n/states/ID.php:33
msgctxt "ui"
msgid "Riau"
msgstr ""

#: i18n/states/ID.php:34
msgctxt "ui"
msgid "Sulawesi Barat"
msgstr ""

#: i18n/states/ID.php:35
msgctxt "ui"
msgid "Sulawesi Selatan"
msgstr ""

#: i18n/states/ID.php:36
msgctxt "ui"
msgid "Sulawesi Utara"
msgstr ""

#: i18n/states/ID.php:37
msgctxt "ui"
msgid "Sulawesi Tengah"
msgstr ""

#: i18n/states/ID.php:38
msgctxt "ui"
msgid "Sulawesi Tenggara"
msgstr ""

#: i18n/states/ID.php:39
msgctxt "ui"
msgid "Sumatera Barat"
msgstr ""

#: i18n/states/ID.php:40
msgctxt "ui"
msgid "Sumatera Selatan"
msgstr ""

#: i18n/states/ID.php:41
msgctxt "ui"
msgid "Sumatera Utara"
msgstr ""

#: i18n/states/IE.php:8
msgid "Carlow"
msgstr ""

#: i18n/states/IE.php:9
msgid "Cavan"
msgstr ""

#: i18n/states/IE.php:10
msgid "Clare"
msgstr ""

#: i18n/states/IE.php:11
msgid "Cork"
msgstr ""

#: i18n/states/IE.php:12
msgid "Donegal"
msgstr ""

#: i18n/states/IE.php:13
msgid "Dublin"
msgstr ""

#: i18n/states/IE.php:14
msgid "Galway"
msgstr ""

#: i18n/states/IE.php:15
msgid "Kerry"
msgstr ""

#: i18n/states/IE.php:16
msgid "Kildare"
msgstr ""

#: i18n/states/IE.php:17
msgid "Kilkenny"
msgstr ""

#: i18n/states/IE.php:18
msgid "Laois"
msgstr ""

#: i18n/states/IE.php:19
msgid "Leitrim"
msgstr ""

#: i18n/states/IE.php:20
msgid "Limerick"
msgstr ""

#: i18n/states/IE.php:21
msgid "Longford"
msgstr ""

#: i18n/states/IE.php:22
msgid "Louth"
msgstr ""

#: i18n/states/IE.php:23
msgid "Mayo"
msgstr ""

#: i18n/states/IE.php:24
msgid "Meath"
msgstr ""

#: i18n/states/IE.php:25
msgid "Monaghan"
msgstr ""

#: i18n/states/IE.php:26
msgid "Offaly"
msgstr ""

#: i18n/states/IE.php:27
msgid "Roscommon"
msgstr ""

#: i18n/states/IE.php:28
msgid "Sligo"
msgstr ""

#: i18n/states/IE.php:29
msgid "Tipperary"
msgstr ""

#: i18n/states/IE.php:30
msgid "Waterford"
msgstr ""

#: i18n/states/IE.php:31
msgid "Westmeath"
msgstr ""

#: i18n/states/IE.php:32
msgid "Wexford"
msgstr ""

#: i18n/states/IE.php:33
msgid "Wicklow"
msgstr ""

#: i18n/states/IN.php:8
msgctxt "ui"
msgid "Andra Pradesh"
msgstr ""

#: i18n/states/IN.php:9
msgctxt "ui"
msgid "Arunachal Pradesh"
msgstr ""

#: i18n/states/IN.php:10
msgctxt "ui"
msgid "Assam"
msgstr ""

#: i18n/states/IN.php:11
msgctxt "ui"
msgid "Bihar"
msgstr ""

#: i18n/states/IN.php:12
msgctxt "ui"
msgid "Chhattisgarh"
msgstr ""

#: i18n/states/IN.php:13
msgctxt "ui"
msgid "Goa"
msgstr ""

#: i18n/states/IN.php:14
msgctxt "ui"
msgid "Gujarat"
msgstr ""

#: i18n/states/IN.php:15
msgctxt "ui"
msgid "Haryana"
msgstr ""

#: i18n/states/IN.php:16
msgctxt "ui"
msgid "Himachal Pradesh"
msgstr ""

#: i18n/states/IN.php:17
msgctxt "ui"
msgid "Jammu and Kashmir"
msgstr ""

#: i18n/states/IN.php:18
msgctxt "ui"
msgid "Jharkhand"
msgstr ""

#: i18n/states/IN.php:19
msgctxt "ui"
msgid "Karnataka"
msgstr ""

#: i18n/states/IN.php:20
msgctxt "ui"
msgid "Kerala"
msgstr ""

#: i18n/states/IN.php:21
msgctxt "ui"
msgid "Madhya Pradesh"
msgstr ""

#: i18n/states/IN.php:22
msgctxt "ui"
msgid "Maharashtra"
msgstr ""

#: i18n/states/IN.php:23
msgctxt "ui"
msgid "Manipur"
msgstr ""

#: i18n/states/IN.php:24
msgctxt "ui"
msgid "Meghalaya"
msgstr ""

#: i18n/states/IN.php:25
msgctxt "ui"
msgid "Mizoram"
msgstr ""

#: i18n/states/IN.php:26
msgctxt "ui"
msgid "Nagaland"
msgstr ""

#: i18n/states/IN.php:27
msgctxt "ui"
msgid "Orissa"
msgstr ""

#: i18n/states/IN.php:28
msgctxt "ui"
msgid "Punjab"
msgstr ""

#: i18n/states/IN.php:29
msgctxt "ui"
msgid "Rajasthan"
msgstr ""

#: i18n/states/IN.php:30
msgctxt "ui"
msgid "Sikkim"
msgstr ""

#: i18n/states/IN.php:31
msgctxt "ui"
msgid "Tamil Nadu"
msgstr ""

#: i18n/states/IN.php:32
msgctxt "ui"
msgid "Telangana"
msgstr ""

#: i18n/states/IN.php:33
msgctxt "ui"
msgid "Tripura"
msgstr ""

#: i18n/states/IN.php:34
msgctxt "ui"
msgid "Uttarakhand"
msgstr ""

#: i18n/states/IN.php:35
msgctxt "ui"
msgid "Uttar Pradesh"
msgstr ""

#: i18n/states/IN.php:36
msgctxt "ui"
msgid "West Bengal"
msgstr ""

#: i18n/states/IN.php:37
msgctxt "ui"
msgid "Andaman and Nicobar Islands"
msgstr ""

#: i18n/states/IN.php:38
msgctxt "ui"
msgid "Chandigarh"
msgstr ""

#: i18n/states/IN.php:39
msgctxt "ui"
msgid "Dadar and Nagar Haveli"
msgstr ""

#: i18n/states/IN.php:40
msgctxt "ui"
msgid "Daman and Diu"
msgstr ""

#: i18n/states/IN.php:41
msgctxt "ui"
msgid "Delhi"
msgstr ""

#: i18n/states/IN.php:42
msgctxt "ui"
msgid "Lakshadeep"
msgstr ""

#: i18n/states/IN.php:43
msgctxt "ui"
msgid "Pondicherry (Puducherry)"
msgstr ""

#: i18n/states/IR.php:8
msgctxt "ui"
msgid "Alborz (البرز)"
msgstr ""

#: i18n/states/IR.php:9
msgctxt "ui"
msgid "Ardabil (اردبیل)"
msgstr ""

#: i18n/states/IR.php:10
msgctxt "ui"
msgid "Bushehr (بوشهر)"
msgstr ""

#: i18n/states/IR.php:11
msgctxt "ui"
msgid "Chaharmahal and Bakhtiari (چهارمحال و بختیاری)"
msgstr ""

#: i18n/states/IR.php:12
msgctxt "ui"
msgid "East Azarbaijan (آذربایجان شرقی)"
msgstr ""

#: i18n/states/IR.php:13
msgctxt "ui"
msgid "Fars (فارس)"
msgstr ""

#: i18n/states/IR.php:14
msgctxt "ui"
msgid "Ghazvin (قزوین)"
msgstr ""

#: i18n/states/IR.php:15
msgctxt "ui"
msgid "Gilan (گیلان)"
msgstr ""

#: i18n/states/IR.php:16
msgctxt "ui"
msgid "Golestan (گلستان)"
msgstr ""

#: i18n/states/IR.php:17
msgctxt "ui"
msgid "Hamadan (همدان)"
msgstr ""

#: i18n/states/IR.php:18
msgctxt "ui"
msgid "Hormozgan (هرمزگان)"
msgstr ""

#: i18n/states/IR.php:19
msgctxt "ui"
msgid "Ilaam (ایلام)"
msgstr ""

#: i18n/states/IR.php:20
msgctxt "ui"
msgid "Isfahan (اصفهان)"
msgstr ""

#: i18n/states/IR.php:21
msgctxt "ui"
msgid "Kerman (کرمان)"
msgstr ""

#: i18n/states/IR.php:22
msgctxt "ui"
msgid "Kermanshah (کرمانشاه)"
msgstr ""

#: i18n/states/IR.php:23
msgctxt "ui"
msgid "Khuzestan  (خوزستان)"
msgstr ""

#: i18n/states/IR.php:24
msgctxt "ui"
msgid "Kohgiluyeh and BoyerAhmad (کهگیلوییه و بویراحمد)"
msgstr ""

#: i18n/states/IR.php:25
msgctxt "ui"
msgid "Kurdistan / کردستان)"
msgstr ""

#: i18n/states/IR.php:26
msgctxt "ui"
msgid "Luristan (لرستان)"
msgstr ""

#: i18n/states/IR.php:27
msgctxt "ui"
msgid "Mazandaran (مازندران)"
msgstr ""

#: i18n/states/IR.php:28
msgctxt "ui"
msgid "Markazi (مرکزی)"
msgstr ""

#: i18n/states/IR.php:29
msgctxt "ui"
msgid "North Khorasan (خراسان جنوبی)"
msgstr ""

#: i18n/states/IR.php:30
msgctxt "ui"
msgid "Qom (قم)"
msgstr ""

#: i18n/states/IR.php:31
msgctxt "ui"
msgid "Razavi Khorasan (خراسان رضوی)"
msgstr ""

#: i18n/states/IR.php:32
msgctxt "ui"
msgid "Semnan (سمنان)"
msgstr ""

#: i18n/states/IR.php:33
msgctxt "ui"
msgid "Sistan and Baluchestan (سیستان و بلوچستان)"
msgstr ""

#: i18n/states/IR.php:34
msgctxt "ui"
msgid "South Khorasan (خراسان جنوبی)"
msgstr ""

#: i18n/states/IR.php:35
msgctxt "ui"
msgid "Tehran  (تهران)"
msgstr ""

#: i18n/states/IR.php:36
msgctxt "ui"
msgid "West Azarbaijan (آذربایجان غربی)"
msgstr ""

#: i18n/states/IR.php:37
msgctxt "ui"
msgid "Yazd (یزد)"
msgstr ""

#: i18n/states/IR.php:38
msgctxt "ui"
msgid "Zanjan (زنجان)"
msgstr ""

#: i18n/states/IT.php:8
msgctxt "ui"
msgid "Agrigento"
msgstr ""

#: i18n/states/IT.php:9
msgctxt "ui"
msgid "Alessandria"
msgstr ""

#: i18n/states/IT.php:10
msgctxt "ui"
msgid "Ancona"
msgstr ""

#: i18n/states/IT.php:11
msgctxt "ui"
msgid "Aosta"
msgstr ""

#: i18n/states/IT.php:12
msgctxt "ui"
msgid "Arezzo"
msgstr ""

#: i18n/states/IT.php:13
msgctxt "ui"
msgid "Ascoli Piceno"
msgstr ""

#: i18n/states/IT.php:14
msgctxt "ui"
msgid "Asti"
msgstr ""

#: i18n/states/IT.php:15
msgctxt "ui"
msgid "Avellino"
msgstr ""

#: i18n/states/IT.php:16
msgctxt "ui"
msgid "Bari"
msgstr ""

#: i18n/states/IT.php:17
msgctxt "ui"
msgid "Barletta-Andria-Trani"
msgstr ""

#: i18n/states/IT.php:18
msgctxt "ui"
msgid "Belluno"
msgstr ""

#: i18n/states/IT.php:19
msgctxt "ui"
msgid "Benevento"
msgstr ""

#: i18n/states/IT.php:20
msgctxt "ui"
msgid "Bergamo"
msgstr ""

#: i18n/states/IT.php:21
msgctxt "ui"
msgid "Biella"
msgstr ""

#: i18n/states/IT.php:22
msgctxt "ui"
msgid "Bologna"
msgstr ""

#: i18n/states/IT.php:23
msgctxt "ui"
msgid "Bolzano"
msgstr ""

#: i18n/states/IT.php:24
msgctxt "ui"
msgid "Brescia"
msgstr ""

#: i18n/states/IT.php:25
msgctxt "ui"
msgid "Brindisi"
msgstr ""

#: i18n/states/IT.php:26
msgctxt "ui"
msgid "Cagliari"
msgstr ""

#: i18n/states/IT.php:27
msgctxt "ui"
msgid "Caltanissetta"
msgstr ""

#: i18n/states/IT.php:28
msgctxt "ui"
msgid "Campobasso"
msgstr ""

#: i18n/states/IT.php:29
msgctxt "ui"
msgid "Carbonia-Iglesias"
msgstr ""

#: i18n/states/IT.php:30
msgctxt "ui"
msgid "Caserta"
msgstr ""

#: i18n/states/IT.php:31
msgctxt "ui"
msgid "Catania"
msgstr ""

#: i18n/states/IT.php:32
msgctxt "ui"
msgid "Catanzaro"
msgstr ""

#: i18n/states/IT.php:33
msgctxt "ui"
msgid "Chieti"
msgstr ""

#: i18n/states/IT.php:34
msgctxt "ui"
msgid "Como"
msgstr ""

#: i18n/states/IT.php:35
msgctxt "ui"
msgid "Cosenza"
msgstr ""

#: i18n/states/IT.php:36
msgctxt "ui"
msgid "Cremona"
msgstr ""

#: i18n/states/IT.php:37
msgctxt "ui"
msgid "Crotone"
msgstr ""

#: i18n/states/IT.php:38
msgctxt "ui"
msgid "Cuneo"
msgstr ""

#: i18n/states/IT.php:39
msgctxt "ui"
msgid "Enna"
msgstr ""

#: i18n/states/IT.php:40
msgctxt "ui"
msgid "Fermo"
msgstr ""

#: i18n/states/IT.php:41
msgctxt "ui"
msgid "Ferrara"
msgstr ""

#: i18n/states/IT.php:42
msgctxt "ui"
msgid "Firenze"
msgstr ""

#: i18n/states/IT.php:43
msgctxt "ui"
msgid "Foggia"
msgstr ""

#: i18n/states/IT.php:44
msgctxt "ui"
msgid "Forlì-Cesena"
msgstr ""

#: i18n/states/IT.php:45
msgctxt "ui"
msgid "Frosinone"
msgstr ""

#: i18n/states/IT.php:46
msgctxt "ui"
msgid "Genova"
msgstr ""

#: i18n/states/IT.php:47
msgctxt "ui"
msgid "Gorizia"
msgstr ""

#: i18n/states/IT.php:48
msgctxt "ui"
msgid "Grosseto"
msgstr ""

#: i18n/states/IT.php:49
msgctxt "ui"
msgid "Imperia"
msgstr ""

#: i18n/states/IT.php:50
msgctxt "ui"
msgid "Isernia"
msgstr ""

#: i18n/states/IT.php:51
msgctxt "ui"
msgid "La Spezia"
msgstr ""

#: i18n/states/IT.php:52
msgctxt "ui"
msgid "L&apos;Aquila"
msgstr ""

#: i18n/states/IT.php:53
msgctxt "ui"
msgid "Latina"
msgstr ""

#: i18n/states/IT.php:54
msgctxt "ui"
msgid "Lecce"
msgstr ""

#: i18n/states/IT.php:55
msgctxt "ui"
msgid "Lecco"
msgstr ""

#: i18n/states/IT.php:56
msgctxt "ui"
msgid "Livorno"
msgstr ""

#: i18n/states/IT.php:57
msgctxt "ui"
msgid "Lodi"
msgstr ""

#: i18n/states/IT.php:58
msgctxt "ui"
msgid "Lucca"
msgstr ""

#: i18n/states/IT.php:59
msgctxt "ui"
msgid "Macerata"
msgstr ""

#: i18n/states/IT.php:60
msgctxt "ui"
msgid "Mantova"
msgstr ""

#: i18n/states/IT.php:61
msgctxt "ui"
msgid "Massa-Carrara"
msgstr ""

#: i18n/states/IT.php:62
msgctxt "ui"
msgid "Matera"
msgstr ""

#: i18n/states/IT.php:63
msgctxt "ui"
msgid "Messina"
msgstr ""

#: i18n/states/IT.php:64
msgctxt "ui"
msgid "Milano"
msgstr ""

#: i18n/states/IT.php:65
msgctxt "ui"
msgid "Modena"
msgstr ""

#: i18n/states/IT.php:66
msgctxt "ui"
msgid "Monza e della Brianza"
msgstr ""

#: i18n/states/IT.php:67
msgctxt "ui"
msgid "Napoli"
msgstr ""

#: i18n/states/IT.php:68
msgctxt "ui"
msgid "Novara"
msgstr ""

#: i18n/states/IT.php:69
msgctxt "ui"
msgid "Nuoro"
msgstr ""

#: i18n/states/IT.php:70
msgctxt "ui"
msgid "Olbia-Tempio"
msgstr ""

#: i18n/states/IT.php:71
msgctxt "ui"
msgid "Oristano"
msgstr ""

#: i18n/states/IT.php:72
msgctxt "ui"
msgid "Padova"
msgstr ""

#: i18n/states/IT.php:73
msgctxt "ui"
msgid "Palermo"
msgstr ""

#: i18n/states/IT.php:74
msgctxt "ui"
msgid "Parma"
msgstr ""

#: i18n/states/IT.php:75
msgctxt "ui"
msgid "Pavia"
msgstr ""

#: i18n/states/IT.php:76
msgctxt "ui"
msgid "Perugia"
msgstr ""

#: i18n/states/IT.php:77
msgctxt "ui"
msgid "Pesaro e Urbino"
msgstr ""

#: i18n/states/IT.php:78
msgctxt "ui"
msgid "Pescara"
msgstr ""

#: i18n/states/IT.php:79
msgctxt "ui"
msgid "Piacenza"
msgstr ""

#: i18n/states/IT.php:80
msgctxt "ui"
msgid "Pisa"
msgstr ""

#: i18n/states/IT.php:81
msgctxt "ui"
msgid "Pistoia"
msgstr ""

#: i18n/states/IT.php:82
msgctxt "ui"
msgid "Pordenone"
msgstr ""

#: i18n/states/IT.php:83
msgctxt "ui"
msgid "Potenza"
msgstr ""

#: i18n/states/IT.php:84
msgctxt "ui"
msgid "Prato"
msgstr ""

#: i18n/states/IT.php:85
msgctxt "ui"
msgid "Ragusa"
msgstr ""

#: i18n/states/IT.php:86
msgctxt "ui"
msgid "Ravenna"
msgstr ""

#: i18n/states/IT.php:87
msgctxt "ui"
msgid "Reggio Calabria"
msgstr ""

#: i18n/states/IT.php:88
msgctxt "ui"
msgid "Reggio Emilia"
msgstr ""

#: i18n/states/IT.php:89
msgctxt "ui"
msgid "Rieti"
msgstr ""

#: i18n/states/IT.php:90
msgctxt "ui"
msgid "Rimini"
msgstr ""

#: i18n/states/IT.php:91
msgctxt "ui"
msgid "Roma"
msgstr ""

#: i18n/states/IT.php:92
msgctxt "ui"
msgid "Rovigo"
msgstr ""

#: i18n/states/IT.php:93
msgctxt "ui"
msgid "Salerno"
msgstr ""

#: i18n/states/IT.php:94
msgctxt "ui"
msgid "Medio Campidano"
msgstr ""

#: i18n/states/IT.php:95
msgctxt "ui"
msgid "Sassari"
msgstr ""

#: i18n/states/IT.php:96
msgctxt "ui"
msgid "Savona"
msgstr ""

#: i18n/states/IT.php:97
msgctxt "ui"
msgid "Siena"
msgstr ""

#: i18n/states/IT.php:98
msgctxt "ui"
msgid "Siracusa"
msgstr ""

#: i18n/states/IT.php:99
msgctxt "ui"
msgid "Sondrio"
msgstr ""

#: i18n/states/IT.php:100
msgctxt "ui"
msgid "Taranto"
msgstr ""

#: i18n/states/IT.php:101
msgctxt "ui"
msgid "Teramo"
msgstr ""

#: i18n/states/IT.php:102
msgctxt "ui"
msgid "Terni"
msgstr ""

#: i18n/states/IT.php:103
msgctxt "ui"
msgid "Torino"
msgstr ""

#: i18n/states/IT.php:104
msgctxt "ui"
msgid "Ogliastra"
msgstr ""

#: i18n/states/IT.php:105
msgctxt "ui"
msgid "Trapani"
msgstr ""

#: i18n/states/IT.php:106
msgctxt "ui"
msgid "Trento"
msgstr ""

#: i18n/states/IT.php:107
msgctxt "ui"
msgid "Treviso"
msgstr ""

#: i18n/states/IT.php:108
msgctxt "ui"
msgid "Trieste"
msgstr ""

#: i18n/states/IT.php:109
msgctxt "ui"
msgid "Udine"
msgstr ""

#: i18n/states/IT.php:110
msgctxt "ui"
msgid "Varese"
msgstr ""

#: i18n/states/IT.php:111
msgctxt "ui"
msgid "Venezia"
msgstr ""

#: i18n/states/IT.php:112
msgctxt "ui"
msgid "Verbano-Cusio-Ossola"
msgstr ""

#: i18n/states/IT.php:113
msgctxt "ui"
msgid "Vercelli"
msgstr ""

#: i18n/states/IT.php:114
msgctxt "ui"
msgid "Verona"
msgstr ""

#: i18n/states/IT.php:115
msgctxt "ui"
msgid "Vibo Valentia"
msgstr ""

#: i18n/states/IT.php:116
msgctxt "ui"
msgid "Vicenza"
msgstr ""

#: i18n/states/IT.php:117
msgctxt "ui"
msgid "Viterbo"
msgstr ""

#: i18n/states/JP.php:8
msgctxt "ui"
msgid "Hokkaido"
msgstr ""

#: i18n/states/JP.php:9
msgctxt "ui"
msgid "Aomori"
msgstr ""

#: i18n/states/JP.php:10
msgctxt "ui"
msgid "Iwate"
msgstr ""

#: i18n/states/JP.php:11
msgctxt "ui"
msgid "Miyagi"
msgstr ""

#: i18n/states/JP.php:12
msgctxt "ui"
msgid "Akita"
msgstr ""

#: i18n/states/JP.php:13
msgctxt "ui"
msgid "Yamagata"
msgstr ""

#: i18n/states/JP.php:14
msgctxt "ui"
msgid "Fukushima"
msgstr ""

#: i18n/states/JP.php:15
msgctxt "ui"
msgid "Ibaraki"
msgstr ""

#: i18n/states/JP.php:16
msgctxt "ui"
msgid "Tochigi"
msgstr ""

#: i18n/states/JP.php:17
msgctxt "ui"
msgid "Gunma"
msgstr ""

#: i18n/states/JP.php:18
msgctxt "ui"
msgid "Saitama"
msgstr ""

#: i18n/states/JP.php:19
msgctxt "ui"
msgid "Chiba"
msgstr ""

#: i18n/states/JP.php:20
msgctxt "ui"
msgid "Tokyo"
msgstr ""

#: i18n/states/JP.php:21
msgctxt "ui"
msgid "Kanagawa"
msgstr ""

#: i18n/states/JP.php:22
msgctxt "ui"
msgid "Niigata"
msgstr ""

#: i18n/states/JP.php:23
msgctxt "ui"
msgid "Toyama"
msgstr ""

#: i18n/states/JP.php:24
msgctxt "ui"
msgid "Ishikawa"
msgstr ""

#: i18n/states/JP.php:25
msgctxt "ui"
msgid "Fukui"
msgstr ""

#: i18n/states/JP.php:26
msgctxt "ui"
msgid "Yamanashi"
msgstr ""

#: i18n/states/JP.php:27
msgctxt "ui"
msgid "Nagano"
msgstr ""

#: i18n/states/JP.php:28
msgctxt "ui"
msgid "Gifu"
msgstr ""

#: i18n/states/JP.php:29
msgctxt "ui"
msgid "Shizuoka"
msgstr ""

#: i18n/states/JP.php:30
msgctxt "ui"
msgid "Aichi"
msgstr ""

#: i18n/states/JP.php:31
msgctxt "ui"
msgid "Mie"
msgstr ""

#: i18n/states/JP.php:32
msgctxt "ui"
msgid "Shiga"
msgstr ""

#: i18n/states/JP.php:33
msgctxt "ui"
msgid "Kyouto"
msgstr ""

#: i18n/states/JP.php:34
msgctxt "ui"
msgid "Osaka"
msgstr ""

#: i18n/states/JP.php:35
msgctxt "ui"
msgid "Hyougo"
msgstr ""

#: i18n/states/JP.php:36
msgctxt "ui"
msgid "Nara"
msgstr ""

#: i18n/states/JP.php:37
msgctxt "ui"
msgid "Wakayama"
msgstr ""

#: i18n/states/JP.php:38
msgctxt "ui"
msgid "Tottori"
msgstr ""

#: i18n/states/JP.php:39
msgctxt "ui"
msgid "Shimane"
msgstr ""

#: i18n/states/JP.php:40
msgctxt "ui"
msgid "Okayama"
msgstr ""

#: i18n/states/JP.php:41
msgctxt "ui"
msgid "Hiroshima"
msgstr ""

#: i18n/states/JP.php:42
msgctxt "ui"
msgid "Yamaguchi"
msgstr ""

#: i18n/states/JP.php:43
msgctxt "ui"
msgid "Tokushima"
msgstr ""

#: i18n/states/JP.php:44
msgctxt "ui"
msgid "Kagawa"
msgstr ""

#: i18n/states/JP.php:45
msgctxt "ui"
msgid "Ehime"
msgstr ""

#: i18n/states/JP.php:46
msgctxt "ui"
msgid "Kochi"
msgstr ""

#: i18n/states/JP.php:47
msgctxt "ui"
msgid "Fukuoka"
msgstr ""

#: i18n/states/JP.php:48
msgctxt "ui"
msgid "Saga"
msgstr ""

#: i18n/states/JP.php:49
msgctxt "ui"
msgid "Nagasaki"
msgstr ""

#: i18n/states/JP.php:50
msgctxt "ui"
msgid "Kumamoto"
msgstr ""

#: i18n/states/JP.php:51
msgctxt "ui"
msgid "Oita"
msgstr ""

#: i18n/states/JP.php:52
msgctxt "ui"
msgid "Miyazaki"
msgstr ""

#: i18n/states/JP.php:53
msgctxt "ui"
msgid "Kagoshima"
msgstr ""

#: i18n/states/JP.php:54
msgctxt "ui"
msgid "Okinawa"
msgstr ""

#: i18n/states/MX.php:8
msgctxt "ui"
msgid "Aguascalientes"
msgstr ""

#: i18n/states/MX.php:9
msgctxt "ui"
msgid "Baja California"
msgstr ""

#: i18n/states/MX.php:10
msgctxt "ui"
msgid "Baja California Sur"
msgstr ""

#: i18n/states/MX.php:11
msgctxt "ui"
msgid "Campeche"
msgstr ""

#: i18n/states/MX.php:12
msgctxt "ui"
msgid "Chiapas"
msgstr ""

#: i18n/states/MX.php:13
msgctxt "ui"
msgid "Chihuahua"
msgstr ""

#: i18n/states/MX.php:14
msgctxt "ui"
msgid "Ciudad de México (CDMX)"
msgstr ""

#: i18n/states/MX.php:15
msgctxt "ui"
msgid "Coahuila"
msgstr ""

#: i18n/states/MX.php:16
msgctxt "ui"
msgid "Colima"
msgstr ""

#: i18n/states/MX.php:17
msgctxt "ui"
msgid "Durango"
msgstr ""

#: i18n/states/MX.php:18
msgctxt "ui"
msgid "Edo. de México"
msgstr ""

#: i18n/states/MX.php:19
msgctxt "ui"
msgid "Guanajuato"
msgstr ""

#: i18n/states/MX.php:20
msgctxt "ui"
msgid "Guerrero"
msgstr ""

#: i18n/states/MX.php:21
msgctxt "ui"
msgid "Hidalgo"
msgstr ""

#: i18n/states/MX.php:22
msgctxt "ui"
msgid "Jalisco"
msgstr ""

#: i18n/states/MX.php:23
msgctxt "ui"
msgid "Michoacán"
msgstr ""

#: i18n/states/MX.php:24
msgctxt "ui"
msgid "Morelos"
msgstr ""

#: i18n/states/MX.php:25
msgctxt "ui"
msgid "Nayarit"
msgstr ""

#: i18n/states/MX.php:26
msgctxt "ui"
msgid "Nuevo León"
msgstr ""

#: i18n/states/MX.php:27
msgctxt "ui"
msgid "Oaxaca"
msgstr ""

#: i18n/states/MX.php:28
msgctxt "ui"
msgid "Puebla"
msgstr ""

#: i18n/states/MX.php:29
msgctxt "ui"
msgid "Querétaro"
msgstr ""

#: i18n/states/MX.php:30
msgctxt "ui"
msgid "Quintana Roo"
msgstr ""

#: i18n/states/MX.php:31
msgctxt "ui"
msgid "San Luis Potosí"
msgstr ""

#: i18n/states/MX.php:32
msgctxt "ui"
msgid "Sinaloa"
msgstr ""

#: i18n/states/MX.php:33
msgctxt "ui"
msgid "Sonora"
msgstr ""

#: i18n/states/MX.php:34
msgctxt "ui"
msgid "Tabasco"
msgstr ""

#: i18n/states/MX.php:35
msgctxt "ui"
msgid "Tamaulipas"
msgstr ""

#: i18n/states/MX.php:36
msgctxt "ui"
msgid "Tlaxcala"
msgstr ""

#: i18n/states/MX.php:37
msgctxt "ui"
msgid "Veracruz"
msgstr ""

#: i18n/states/MX.php:38
msgctxt "ui"
msgid "Yucatán"
msgstr ""

#: i18n/states/MX.php:39
msgctxt "ui"
msgid "Zacatecas"
msgstr ""

#: i18n/states/MY.php:8
msgctxt "ui"
msgid "Johor"
msgstr ""

#: i18n/states/MY.php:9
msgctxt "ui"
msgid "Kedah"
msgstr ""

#: i18n/states/MY.php:10
msgctxt "ui"
msgid "Kelantan"
msgstr ""

#: i18n/states/MY.php:11
msgctxt "ui"
msgid "Melaka"
msgstr ""

#: i18n/states/MY.php:12
msgctxt "ui"
msgid "Negeri Sembilan"
msgstr ""

#: i18n/states/MY.php:13
msgctxt "ui"
msgid "Pahang"
msgstr ""

#: i18n/states/MY.php:14
msgctxt "ui"
msgid "Perak"
msgstr ""

#: i18n/states/MY.php:15
msgctxt "ui"
msgid "Perlis"
msgstr ""

#: i18n/states/MY.php:16
msgctxt "ui"
msgid "Pulau Pinang"
msgstr ""

#: i18n/states/MY.php:17
msgctxt "ui"
msgid "Sabah"
msgstr ""

#: i18n/states/MY.php:18
msgctxt "ui"
msgid "Sarawak"
msgstr ""

#: i18n/states/MY.php:19
msgctxt "ui"
msgid "Selangor"
msgstr ""

#: i18n/states/MY.php:20
msgctxt "ui"
msgid "Terengganu"
msgstr ""

#: i18n/states/MY.php:21
msgctxt "ui"
msgid "W.P. Kuala Lumpur"
msgstr ""

#: i18n/states/MY.php:22
msgctxt "ui"
msgid "W.P. Labuan"
msgstr ""

#: i18n/states/MY.php:23
msgctxt "ui"
msgid "W.P. Putrajaya"
msgstr ""

#: i18n/states/NP.php:10
msgctxt "ui"
msgid "Illam"
msgstr ""

#: i18n/states/NP.php:11
msgctxt "ui"
msgid "Jhapa"
msgstr ""

#: i18n/states/NP.php:12
msgctxt "ui"
msgid "Panchthar"
msgstr ""

#: i18n/states/NP.php:13
msgctxt "ui"
msgid "Taplejung"
msgstr ""

#: i18n/states/NP.php:16
msgctxt "ui"
msgid "Bhojpur"
msgstr ""

#: i18n/states/NP.php:17
msgctxt "ui"
msgid "Dhankuta"
msgstr ""

#: i18n/states/NP.php:18
msgctxt "ui"
msgid "Morang"
msgstr ""

#: i18n/states/NP.php:19
msgctxt "ui"
msgid "Sunsari"
msgstr ""

#: i18n/states/NP.php:20
msgctxt "ui"
msgid "Sankhuwa"
msgstr ""

#: i18n/states/NP.php:21
msgctxt "ui"
msgid "Terhathum"
msgstr ""

#: i18n/states/NP.php:24
msgctxt "ui"
msgid "Khotang"
msgstr ""

#: i18n/states/NP.php:25
msgctxt "ui"
msgid "Okhaldhunga"
msgstr ""

#: i18n/states/NP.php:26
msgctxt "ui"
msgid "Saptari"
msgstr ""

#: i18n/states/NP.php:27
msgctxt "ui"
msgid "Siraha"
msgstr ""

#: i18n/states/NP.php:28
msgctxt "ui"
msgid "Solukhumbu"
msgstr ""

#: i18n/states/NP.php:29
msgctxt "ui"
msgid "Udayapur"
msgstr ""

#: i18n/states/NP.php:32
msgctxt "ui"
msgid "Dhanusa"
msgstr ""

#: i18n/states/NP.php:33
msgctxt "ui"
msgid "Dolakha"
msgstr ""

#: i18n/states/NP.php:34
msgctxt "ui"
msgid "Mohottari"
msgstr ""

#: i18n/states/NP.php:35
msgctxt "ui"
msgid "Ramechha"
msgstr ""

#: i18n/states/NP.php:36
msgctxt "ui"
msgid "Sarlahi"
msgstr ""

#: i18n/states/NP.php:37
msgctxt "ui"
msgid "Sindhuli"
msgstr ""

#: i18n/states/NP.php:40
msgctxt "ui"
msgid "Bhaktapur"
msgstr ""

#: i18n/states/NP.php:41
msgctxt "ui"
msgid "Dhading"
msgstr ""

#: i18n/states/NP.php:42
msgctxt "ui"
msgid "Kathmandu"
msgstr ""

#: i18n/states/NP.php:43
msgctxt "ui"
msgid "Kavrepalanchowk"
msgstr ""

#: i18n/states/NP.php:44
msgctxt "ui"
msgid "Lalitpur"
msgstr ""

#: i18n/states/NP.php:45
msgctxt "ui"
msgid "Nuwakot"
msgstr ""

#: i18n/states/NP.php:46
msgctxt "ui"
msgid "Rasuwa"
msgstr ""

#: i18n/states/NP.php:47
msgctxt "ui"
msgid "Sindhupalchowk"
msgstr ""

#: i18n/states/NP.php:50
msgctxt "ui"
msgid "Bara"
msgstr ""

#: i18n/states/NP.php:51
msgctxt "ui"
msgid "Chitwan"
msgstr ""

#: i18n/states/NP.php:52
msgctxt "ui"
msgid "Makwanpur"
msgstr ""

#: i18n/states/NP.php:53
msgctxt "ui"
msgid "Parsa"
msgstr ""

#: i18n/states/NP.php:54
msgctxt "ui"
msgid "Rautahat"
msgstr ""

#: i18n/states/NP.php:57
msgctxt "ui"
msgid "Gorkha"
msgstr ""

#: i18n/states/NP.php:58
msgctxt "ui"
msgid "Kaski"
msgstr ""

#: i18n/states/NP.php:59
msgctxt "ui"
msgid "Lamjung"
msgstr ""

#: i18n/states/NP.php:60
msgctxt "ui"
msgid "Manang"
msgstr ""

#: i18n/states/NP.php:61
msgctxt "ui"
msgid "Syangja"
msgstr ""

#: i18n/states/NP.php:62
msgctxt "ui"
msgid "Tanahun"
msgstr ""

#: i18n/states/NP.php:65
msgctxt "ui"
msgid "Baglung"
msgstr ""

#: i18n/states/NP.php:66
msgctxt "ui"
msgid "Parbat"
msgstr ""

#: i18n/states/NP.php:67
msgctxt "ui"
msgid "Mustang"
msgstr ""

#: i18n/states/NP.php:68
msgctxt "ui"
msgid "Myagdi"
msgstr ""

#: i18n/states/NP.php:71
msgctxt "ui"
msgid "Agrghakanchi"
msgstr ""

#: i18n/states/NP.php:72
msgctxt "ui"
msgid "Gulmi"
msgstr ""

#: i18n/states/NP.php:73
msgctxt "ui"
msgid "Kapilbastu"
msgstr ""

#: i18n/states/NP.php:74
msgctxt "ui"
msgid "Nawalparasi"
msgstr ""

#: i18n/states/NP.php:75
msgctxt "ui"
msgid "Palpa"
msgstr ""

#: i18n/states/NP.php:76
msgctxt "ui"
msgid "Rupandehi"
msgstr ""

#: i18n/states/NP.php:79
msgctxt "ui"
msgid "Dang"
msgstr ""

#: i18n/states/NP.php:80
msgctxt "ui"
msgid "Pyuthan"
msgstr ""

#: i18n/states/NP.php:81
msgctxt "ui"
msgid "Rolpa"
msgstr ""

#: i18n/states/NP.php:82
msgctxt "ui"
msgid "Rukum"
msgstr ""

#: i18n/states/NP.php:83
msgctxt "ui"
msgid "Salyan"
msgstr ""

#: i18n/states/NP.php:86
msgctxt "ui"
msgid "Banke"
msgstr ""

#: i18n/states/NP.php:87
msgctxt "ui"
msgid "Bardiya"
msgstr ""

#: i18n/states/NP.php:88
msgctxt "ui"
msgid "Dailekh"
msgstr ""

#: i18n/states/NP.php:89
msgctxt "ui"
msgid "Jajarkot"
msgstr ""

#: i18n/states/NP.php:90
msgctxt "ui"
msgid "Surkhet"
msgstr ""

#: i18n/states/NP.php:93
msgctxt "ui"
msgid "Dolpa"
msgstr ""

#: i18n/states/NP.php:94
msgctxt "ui"
msgid "Humla"
msgstr ""

#: i18n/states/NP.php:95
msgctxt "ui"
msgid "Jumla"
msgstr ""

#: i18n/states/NP.php:96
msgctxt "ui"
msgid "Kalikot"
msgstr ""

#: i18n/states/NP.php:97
msgctxt "ui"
msgid "Mugu"
msgstr ""

#: i18n/states/NP.php:100
msgctxt "ui"
msgid "Achham"
msgstr ""

#: i18n/states/NP.php:101
msgctxt "ui"
msgid "Bajhang"
msgstr ""

#: i18n/states/NP.php:102
msgctxt "ui"
msgid "Bajura"
msgstr ""

#: i18n/states/NP.php:103
msgctxt "ui"
msgid "Doti"
msgstr ""

#: i18n/states/NP.php:104
msgctxt "ui"
msgid "Kailali"
msgstr ""

#: i18n/states/NP.php:107
msgctxt "ui"
msgid "Baitadi"
msgstr ""

#: i18n/states/NP.php:108
msgctxt "ui"
msgid "Dadeldhura"
msgstr ""

#: i18n/states/NP.php:109
msgctxt "ui"
msgid "Darchula"
msgstr ""

#: i18n/states/NP.php:110
msgctxt "ui"
msgid "Kanchanpur"
msgstr ""

#: i18n/states/NZ.php:8
msgctxt "ui"
msgid "Auckland"
msgstr ""

#: i18n/states/NZ.php:9
msgctxt "ui"
msgid "Bay of Plenty"
msgstr ""

#: i18n/states/NZ.php:10
msgctxt "ui"
msgid "Canterbury"
msgstr ""

#: i18n/states/NZ.php:11
msgctxt "ui"
msgid "Hawke&rsquo;s Bay"
msgstr ""

#: i18n/states/NZ.php:12
msgctxt "ui"
msgid "Manawatu-Wanganui"
msgstr ""

#: i18n/states/NZ.php:13
msgctxt "ui"
msgid "Marlborough"
msgstr ""

#: i18n/states/NZ.php:14
msgctxt "ui"
msgid "Nelson"
msgstr ""

#: i18n/states/NZ.php:15
msgctxt "ui"
msgid "Northland"
msgstr ""

#: i18n/states/NZ.php:16
msgctxt "ui"
msgid "Otago"
msgstr ""

#: i18n/states/NZ.php:17
msgctxt "ui"
msgid "Southland"
msgstr ""

#: i18n/states/NZ.php:18
msgctxt "ui"
msgid "Taranaki"
msgstr ""

#: i18n/states/NZ.php:19
msgctxt "ui"
msgid "Tasman"
msgstr ""

#: i18n/states/NZ.php:20
msgctxt "ui"
msgid "Waikato"
msgstr ""

#: i18n/states/NZ.php:21
msgctxt "ui"
msgid "Wellington"
msgstr ""

#: i18n/states/NZ.php:22
msgctxt "ui"
msgid "West Coast"
msgstr ""

#: i18n/states/PE.php:9
msgctxt "ui"
msgid "Ancash"
msgstr ""

#: i18n/states/PE.php:10
msgctxt "ui"
msgid "Apur&iacute;mac"
msgstr ""

#: i18n/states/PE.php:11
msgctxt "ui"
msgid "Arequipa"
msgstr ""

#: i18n/states/PE.php:12
msgctxt "ui"
msgid "Ayacucho"
msgstr ""

#: i18n/states/PE.php:13
msgctxt "ui"
msgid "Cajamarca"
msgstr ""

#: i18n/states/PE.php:14
msgctxt "ui"
msgid "Cusco"
msgstr ""

#: i18n/states/PE.php:15
msgctxt "ui"
msgid "El Callao"
msgstr ""

#: i18n/states/PE.php:16
msgctxt "ui"
msgid "Huancavelica"
msgstr ""

#: i18n/states/PE.php:17
msgctxt "ui"
msgid "Hu&aacute;nuco"
msgstr ""

#: i18n/states/PE.php:18
msgctxt "ui"
msgid "Ica"
msgstr ""

#: i18n/states/PE.php:19
msgctxt "ui"
msgid "Jun&iacute;n"
msgstr ""

#: i18n/states/PE.php:20
msgctxt "ui"
msgid "La Libertad"
msgstr ""

#: i18n/states/PE.php:21
msgctxt "ui"
msgid "Lambayeque"
msgstr ""

#: i18n/states/PE.php:22
msgctxt "ui"
msgid "Lima"
msgstr ""

#: i18n/states/PE.php:23
msgctxt "ui"
msgid "Loreto"
msgstr ""

#: i18n/states/PE.php:24
msgctxt "ui"
msgid "Madre de Dios"
msgstr ""

#: i18n/states/PE.php:25
msgctxt "ui"
msgid "Moquegua"
msgstr ""

#: i18n/states/PE.php:26
msgctxt "ui"
msgid "Municipalidad Metropolitana de Lima"
msgstr ""

#: i18n/states/PE.php:27
msgctxt "ui"
msgid "Pasco"
msgstr ""

#: i18n/states/PE.php:28
msgctxt "ui"
msgid "Piura"
msgstr ""

#: i18n/states/PE.php:29
msgctxt "ui"
msgid "Puno"
msgstr ""

#: i18n/states/PE.php:30
msgctxt "ui"
msgid "San Mart&iacute;n"
msgstr ""

#: i18n/states/PE.php:31
msgctxt "ui"
msgid "Tacna"
msgstr ""

#: i18n/states/PE.php:32
msgctxt "ui"
msgid "Tumbes"
msgstr ""

#: i18n/states/PE.php:33
msgctxt "ui"
msgid "Ucayali"
msgstr ""

#: i18n/states/PT.php:8
msgctxt "ui"
msgid "Açores"
msgstr ""

#: i18n/states/PT.php:9
msgctxt "ui"
msgid "Algarve"
msgstr ""

#: i18n/states/PT.php:10
msgctxt "ui"
msgid "Alentejo"
msgstr ""

#: i18n/states/PT.php:11
msgctxt "ui"
msgid "Centro"
msgstr ""

#: i18n/states/PT.php:12
msgctxt "ui"
msgid "Lisboa e Vale do Tejo"
msgstr ""

#: i18n/states/PT.php:13
msgctxt "ui"
msgid "Madeira"
msgstr ""

#: i18n/states/PT.php:14
msgctxt "ui"
msgid "Norte"
msgstr ""

#: i18n/states/TH.php:8
msgctxt "ui"
msgid "Amnat Charoen (&#3629;&#3635;&#3609;&#3634;&#3592;&#3648;&#3592;&#3619;&#3636;&#3597;)"
msgstr ""

#: i18n/states/TH.php:9
msgctxt "ui"
msgid "Ang Thong (&#3629;&#3656;&#3634;&#3591;&#3607;&#3629;&#3591;)"
msgstr ""

#: i18n/states/TH.php:10
msgctxt "ui"
msgid "Ayutthaya (&#3614;&#3619;&#3632;&#3609;&#3588;&#3619;&#3624;&#3619;&#3637;&#3629;&#3618;&#3640;&#3608;&#3618;&#3634;)"
msgstr ""

#: i18n/states/TH.php:11
msgctxt "ui"
msgid "Bangkok (&#3585;&#3619;&#3640;&#3591;&#3648;&#3607;&#3614;&#3617;&#3627;&#3634;&#3609;&#3588;&#3619;)"
msgstr ""

#: i18n/states/TH.php:12
msgctxt "ui"
msgid "Bueng Kan (&#3610;&#3638;&#3591;&#3585;&#3634;&#3628;)"
msgstr ""

#: i18n/states/TH.php:13
msgctxt "ui"
msgid "Buri Ram (&#3610;&#3640;&#3619;&#3637;&#3619;&#3633;&#3617;&#3618;&#3660;)"
msgstr ""

#: i18n/states/TH.php:14
msgctxt "ui"
msgid "Chachoengsao (&#3593;&#3632;&#3648;&#3594;&#3636;&#3591;&#3648;&#3607;&#3619;&#3634;)"
msgstr ""

#: i18n/states/TH.php:15
msgctxt "ui"
msgid "Chai Nat (&#3594;&#3633;&#3618;&#3609;&#3634;&#3607;)"
msgstr ""

#: i18n/states/TH.php:16
msgctxt "ui"
msgid "Chaiyaphum (&#3594;&#3633;&#3618;&#3616;&#3641;&#3617;&#3636;)"
msgstr ""

#: i18n/states/TH.php:17
msgctxt "ui"
msgid "Chanthaburi (&#3592;&#3633;&#3609;&#3607;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:18
msgctxt "ui"
msgid "Chiang Mai (&#3648;&#3594;&#3637;&#3618;&#3591;&#3651;&#3627;&#3617;&#3656;)"
msgstr ""

#: i18n/states/TH.php:19
msgctxt "ui"
msgid "Chiang Rai (&#3648;&#3594;&#3637;&#3618;&#3591;&#3619;&#3634;&#3618;)"
msgstr ""

#: i18n/states/TH.php:20
msgctxt "ui"
msgid "Chonburi (&#3594;&#3621;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:21
msgctxt "ui"
msgid "Chumphon (&#3594;&#3640;&#3617;&#3614;&#3619;)"
msgstr ""

#: i18n/states/TH.php:22
msgctxt "ui"
msgid "Kalasin (&#3585;&#3634;&#3628;&#3626;&#3636;&#3609;&#3608;&#3640;&#3660;)"
msgstr ""

#: i18n/states/TH.php:23
msgctxt "ui"
msgid "Kamphaeng Phet (&#3585;&#3635;&#3649;&#3614;&#3591;&#3648;&#3614;&#3594;&#3619;)"
msgstr ""

#: i18n/states/TH.php:24
msgctxt "ui"
msgid "Kanchanaburi (&#3585;&#3634;&#3597;&#3592;&#3609;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:25
msgctxt "ui"
msgid "Khon Kaen (&#3586;&#3629;&#3609;&#3649;&#3585;&#3656;&#3609;)"
msgstr ""

#: i18n/states/TH.php:26
msgctxt "ui"
msgid "Krabi (&#3585;&#3619;&#3632;&#3610;&#3637;&#3656;)"
msgstr ""

#: i18n/states/TH.php:27
msgctxt "ui"
msgid "Lampang (&#3621;&#3635;&#3611;&#3634;&#3591;)"
msgstr ""

#: i18n/states/TH.php:28
msgctxt "ui"
msgid "Lamphun (&#3621;&#3635;&#3614;&#3641;&#3609;)"
msgstr ""

#: i18n/states/TH.php:29
msgctxt "ui"
msgid "Loei (&#3648;&#3621;&#3618;)"
msgstr ""

#: i18n/states/TH.php:30
msgctxt "ui"
msgid "Lopburi (&#3621;&#3614;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:31
msgctxt "ui"
msgid "Mae Hong Son (&#3649;&#3617;&#3656;&#3630;&#3656;&#3629;&#3591;&#3626;&#3629;&#3609;)"
msgstr ""

#: i18n/states/TH.php:32
msgctxt "ui"
msgid "Maha Sarakham (&#3617;&#3627;&#3634;&#3626;&#3634;&#3619;&#3588;&#3634;&#3617;)"
msgstr ""

#: i18n/states/TH.php:33
msgctxt "ui"
msgid "Mukdahan (&#3617;&#3640;&#3585;&#3604;&#3634;&#3627;&#3634;&#3619;)"
msgstr ""

#: i18n/states/TH.php:34
msgctxt "ui"
msgid "Nakhon Nayok (&#3609;&#3588;&#3619;&#3609;&#3634;&#3618;&#3585;)"
msgstr ""

#: i18n/states/TH.php:35
msgctxt "ui"
msgid "Nakhon Pathom (&#3609;&#3588;&#3619;&#3611;&#3600;&#3617;)"
msgstr ""

#: i18n/states/TH.php:36
msgctxt "ui"
msgid "Nakhon Phanom (&#3609;&#3588;&#3619;&#3614;&#3609;&#3617;)"
msgstr ""

#: i18n/states/TH.php:37
msgctxt "ui"
msgid "Nakhon Ratchasima (&#3609;&#3588;&#3619;&#3619;&#3634;&#3594;&#3626;&#3637;&#3617;&#3634;)"
msgstr ""

#: i18n/states/TH.php:38
msgctxt "ui"
msgid "Nakhon Sawan (&#3609;&#3588;&#3619;&#3626;&#3623;&#3619;&#3619;&#3588;&#3660;)"
msgstr ""

#: i18n/states/TH.php:39
msgctxt "ui"
msgid "Nakhon Si Thammarat (&#3609;&#3588;&#3619;&#3624;&#3619;&#3637;&#3608;&#3619;&#3619;&#3617;&#3619;&#3634;&#3594;)"
msgstr ""

#: i18n/states/TH.php:40
msgctxt "ui"
msgid "Nan (&#3609;&#3656;&#3634;&#3609;)"
msgstr ""

#: i18n/states/TH.php:41
msgctxt "ui"
msgid "Narathiwat (&#3609;&#3619;&#3634;&#3608;&#3636;&#3623;&#3634;&#3626;)"
msgstr ""

#: i18n/states/TH.php:42
msgctxt "ui"
msgid "Nong Bua Lam Phu (&#3627;&#3609;&#3629;&#3591;&#3610;&#3633;&#3623;&#3621;&#3635;&#3616;&#3641;)"
msgstr ""

#: i18n/states/TH.php:43
msgctxt "ui"
msgid "Nong Khai (&#3627;&#3609;&#3629;&#3591;&#3588;&#3634;&#3618;)"
msgstr ""

#: i18n/states/TH.php:44
msgctxt "ui"
msgid "Nonthaburi (&#3609;&#3609;&#3607;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:45
msgctxt "ui"
msgid "Pathum Thani (&#3611;&#3607;&#3640;&#3617;&#3608;&#3634;&#3609;&#3637;)"
msgstr ""

#: i18n/states/TH.php:46
msgctxt "ui"
msgid "Pattani (&#3611;&#3633;&#3605;&#3605;&#3634;&#3609;&#3637;)"
msgstr ""

#: i18n/states/TH.php:47
msgctxt "ui"
msgid "Phang Nga (&#3614;&#3633;&#3591;&#3591;&#3634;)"
msgstr ""

#: i18n/states/TH.php:48
msgctxt "ui"
msgid "Phatthalung (&#3614;&#3633;&#3607;&#3621;&#3640;&#3591;)"
msgstr ""

#: i18n/states/TH.php:49
msgctxt "ui"
msgid "Phayao (&#3614;&#3632;&#3648;&#3618;&#3634;)"
msgstr ""

#: i18n/states/TH.php:50
msgctxt "ui"
msgid "Phetchabun (&#3648;&#3614;&#3594;&#3619;&#3610;&#3641;&#3619;&#3603;&#3660;)"
msgstr ""

#: i18n/states/TH.php:51
msgctxt "ui"
msgid "Phetchaburi (&#3648;&#3614;&#3594;&#3619;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:52
msgctxt "ui"
msgid "Phichit (&#3614;&#3636;&#3592;&#3636;&#3605;&#3619;)"
msgstr ""

#: i18n/states/TH.php:53
msgctxt "ui"
msgid "Phitsanulok (&#3614;&#3636;&#3625;&#3603;&#3640;&#3650;&#3621;&#3585;)"
msgstr ""

#: i18n/states/TH.php:54
msgctxt "ui"
msgid "Phrae (&#3649;&#3614;&#3619;&#3656;)"
msgstr ""

#: i18n/states/TH.php:55
msgctxt "ui"
msgid "Phuket (&#3616;&#3641;&#3648;&#3585;&#3655;&#3605;)"
msgstr ""

#: i18n/states/TH.php:56
msgctxt "ui"
msgid "Prachin Buri (&#3611;&#3619;&#3634;&#3592;&#3637;&#3609;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:57
msgctxt "ui"
msgid "Prachuap Khiri Khan (&#3611;&#3619;&#3632;&#3592;&#3623;&#3610;&#3588;&#3637;&#3619;&#3637;&#3586;&#3633;&#3609;&#3608;&#3660;)"
msgstr ""

#: i18n/states/TH.php:58
msgctxt "ui"
msgid "Ranong (&#3619;&#3632;&#3609;&#3629;&#3591;)"
msgstr ""

#: i18n/states/TH.php:59
msgctxt "ui"
msgid "Ratchaburi (&#3619;&#3634;&#3594;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:60
msgctxt "ui"
msgid "Rayong (&#3619;&#3632;&#3618;&#3629;&#3591;)"
msgstr ""

#: i18n/states/TH.php:61
msgctxt "ui"
msgid "Roi Et (&#3619;&#3657;&#3629;&#3618;&#3648;&#3629;&#3655;&#3604;)"
msgstr ""

#: i18n/states/TH.php:62
msgctxt "ui"
msgid "Sa Kaeo (&#3626;&#3619;&#3632;&#3649;&#3585;&#3657;&#3623;)"
msgstr ""

#: i18n/states/TH.php:63
msgctxt "ui"
msgid "Sakon Nakhon (&#3626;&#3585;&#3621;&#3609;&#3588;&#3619;)"
msgstr ""

#: i18n/states/TH.php:64
msgctxt "ui"
msgid "Samut Prakan (&#3626;&#3617;&#3640;&#3607;&#3619;&#3611;&#3619;&#3634;&#3585;&#3634;&#3619;)"
msgstr ""

#: i18n/states/TH.php:65
msgctxt "ui"
msgid "Samut Sakhon (&#3626;&#3617;&#3640;&#3607;&#3619;&#3626;&#3634;&#3588;&#3619;)"
msgstr ""

#: i18n/states/TH.php:66
msgctxt "ui"
msgid "Samut Songkhram (&#3626;&#3617;&#3640;&#3607;&#3619;&#3626;&#3591;&#3588;&#3619;&#3634;&#3617;)"
msgstr ""

#: i18n/states/TH.php:67
msgctxt "ui"
msgid "Saraburi (&#3626;&#3619;&#3632;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:68
msgctxt "ui"
msgid "Satun (&#3626;&#3605;&#3641;&#3621;)"
msgstr ""

#: i18n/states/TH.php:69
msgctxt "ui"
msgid "Sing Buri (&#3626;&#3636;&#3591;&#3627;&#3660;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:70
msgctxt "ui"
msgid "Sisaket (&#3624;&#3619;&#3637;&#3626;&#3632;&#3648;&#3585;&#3625;)"
msgstr ""

#: i18n/states/TH.php:71
msgctxt "ui"
msgid "Songkhla (&#3626;&#3591;&#3586;&#3621;&#3634;)"
msgstr ""

#: i18n/states/TH.php:72
msgctxt "ui"
msgid "Sukhothai (&#3626;&#3640;&#3650;&#3586;&#3607;&#3633;&#3618;)"
msgstr ""

#: i18n/states/TH.php:73
msgctxt "ui"
msgid "Suphan Buri (&#3626;&#3640;&#3614;&#3619;&#3619;&#3603;&#3610;&#3640;&#3619;&#3637;)"
msgstr ""

#: i18n/states/TH.php:74
msgctxt "ui"
msgid "Surat Thani (&#3626;&#3640;&#3619;&#3634;&#3625;&#3598;&#3619;&#3660;&#3608;&#3634;&#3609;&#3637;)"
msgstr ""

#: i18n/states/TH.php:75
msgctxt "ui"
msgid "Surin (&#3626;&#3640;&#3619;&#3636;&#3609;&#3607;&#3619;&#3660;)"
msgstr ""

#: i18n/states/TH.php:76
msgctxt "ui"
msgid "Tak (&#3605;&#3634;&#3585;)"
msgstr ""

#: i18n/states/TH.php:77
msgctxt "ui"
msgid "Trang (&#3605;&#3619;&#3633;&#3591;)"
msgstr ""

#: i18n/states/TH.php:78
msgctxt "ui"
msgid "Trat (&#3605;&#3619;&#3634;&#3604;)"
msgstr ""

#: i18n/states/TH.php:79
msgctxt "ui"
msgid "Ubon Ratchathani (&#3629;&#3640;&#3610;&#3621;&#3619;&#3634;&#3594;&#3608;&#3634;&#3609;&#3637;)"
msgstr ""

#: i18n/states/TH.php:80
msgctxt "ui"
msgid "Udon Thani (&#3629;&#3640;&#3604;&#3619;&#3608;&#3634;&#3609;&#3637;)"
msgstr ""

#: i18n/states/TH.php:81
msgctxt "ui"
msgid "Uthai Thani (&#3629;&#3640;&#3607;&#3633;&#3618;&#3608;&#3634;&#3609;&#3637;)"
msgstr ""

#: i18n/states/TH.php:82
msgctxt "ui"
msgid "Uttaradit (&#3629;&#3640;&#3605;&#3619;&#3604;&#3636;&#3605;&#3606;&#3660;)"
msgstr ""

#: i18n/states/TH.php:83
msgctxt "ui"
msgid "Yala (&#3618;&#3632;&#3621;&#3634;)"
msgstr ""

#: i18n/states/TH.php:84
msgctxt "ui"
msgid "Yasothon (&#3618;&#3650;&#3626;&#3608;&#3619;)"
msgstr ""

#: i18n/states/TR.php:8
msgctxt "ui"
msgid "Adana"
msgstr ""

#: i18n/states/TR.php:9
msgctxt "ui"
msgid "Ad&#305;yaman"
msgstr ""

#: i18n/states/TR.php:10
msgctxt "ui"
msgid "Afyon"
msgstr ""

#: i18n/states/TR.php:11
msgctxt "ui"
msgid "A&#287;r&#305;"
msgstr ""

#: i18n/states/TR.php:12
msgctxt "ui"
msgid "Amasya"
msgstr ""

#: i18n/states/TR.php:13
msgctxt "ui"
msgid "Ankara"
msgstr ""

#: i18n/states/TR.php:14
msgctxt "ui"
msgid "Antalya"
msgstr ""

#: i18n/states/TR.php:15
msgctxt "ui"
msgid "Artvin"
msgstr ""

#: i18n/states/TR.php:16
msgctxt "ui"
msgid "Ayd&#305;n"
msgstr ""

#: i18n/states/TR.php:17
msgctxt "ui"
msgid "Bal&#305;kesir"
msgstr ""

#: i18n/states/TR.php:18
msgctxt "ui"
msgid "Bilecik"
msgstr ""

#: i18n/states/TR.php:19
msgctxt "ui"
msgid "Bing&#246;l"
msgstr ""

#: i18n/states/TR.php:20
msgctxt "ui"
msgid "Bitlis"
msgstr ""

#: i18n/states/TR.php:21
msgctxt "ui"
msgid "Bolu"
msgstr ""

#: i18n/states/TR.php:22
msgctxt "ui"
msgid "Burdur"
msgstr ""

#: i18n/states/TR.php:23
msgctxt "ui"
msgid "Bursa"
msgstr ""

#: i18n/states/TR.php:24
msgctxt "ui"
msgid "&#199;anakkale"
msgstr ""

#: i18n/states/TR.php:25
msgctxt "ui"
msgid "&#199;ank&#305;r&#305;"
msgstr ""

#: i18n/states/TR.php:26
msgctxt "ui"
msgid "&#199;orum"
msgstr ""

#: i18n/states/TR.php:27
msgctxt "ui"
msgid "Denizli"
msgstr ""

#: i18n/states/TR.php:28
msgctxt "ui"
msgid "Diyarbak&#305;r"
msgstr ""

#: i18n/states/TR.php:29
msgctxt "ui"
msgid "Edirne"
msgstr ""

#: i18n/states/TR.php:30
msgctxt "ui"
msgid "Elaz&#305;&#287;"
msgstr ""

#: i18n/states/TR.php:31
msgctxt "ui"
msgid "Erzincan"
msgstr ""

#: i18n/states/TR.php:32
msgctxt "ui"
msgid "Erzurum"
msgstr ""

#: i18n/states/TR.php:33
msgctxt "ui"
msgid "Eski&#351;ehir"
msgstr ""

#: i18n/states/TR.php:34
msgctxt "ui"
msgid "Gaziantep"
msgstr ""

#: i18n/states/TR.php:35
msgctxt "ui"
msgid "Giresun"
msgstr ""

#: i18n/states/TR.php:36
msgctxt "ui"
msgid "G&#252;m&#252;&#351;hane"
msgstr ""

#: i18n/states/TR.php:37
msgctxt "ui"
msgid "Hakkari"
msgstr ""

#: i18n/states/TR.php:38
msgctxt "ui"
msgid "Hatay"
msgstr ""

#: i18n/states/TR.php:39
msgctxt "ui"
msgid "Isparta"
msgstr ""

#: i18n/states/TR.php:40
msgctxt "ui"
msgid "&#304;&#231;el"
msgstr ""

#: i18n/states/TR.php:41
msgctxt "ui"
msgid "&#304;stanbul"
msgstr ""

#: i18n/states/TR.php:42
msgctxt "ui"
msgid "&#304;zmir"
msgstr ""

#: i18n/states/TR.php:43
msgctxt "ui"
msgid "Kars"
msgstr ""

#: i18n/states/TR.php:44
msgctxt "ui"
msgid "Kastamonu"
msgstr ""

#: i18n/states/TR.php:45
msgctxt "ui"
msgid "Kayseri"
msgstr ""

#: i18n/states/TR.php:46
msgctxt "ui"
msgid "K&#305;rklareli"
msgstr ""

#: i18n/states/TR.php:47
msgctxt "ui"
msgid "K&#305;r&#351;ehir"
msgstr ""

#: i18n/states/TR.php:48
msgctxt "ui"
msgid "Kocaeli"
msgstr ""

#: i18n/states/TR.php:49
msgctxt "ui"
msgid "Konya"
msgstr ""

#: i18n/states/TR.php:50
msgctxt "ui"
msgid "K&#252;tahya"
msgstr ""

#: i18n/states/TR.php:51
msgctxt "ui"
msgid "Malatya"
msgstr ""

#: i18n/states/TR.php:52
msgctxt "ui"
msgid "Manisa"
msgstr ""

#: i18n/states/TR.php:53
msgctxt "ui"
msgid "Kahramanmara&#351;"
msgstr ""

#: i18n/states/TR.php:54
msgctxt "ui"
msgid "Mardin"
msgstr ""

#: i18n/states/TR.php:55
msgctxt "ui"
msgid "Mu&#287;la"
msgstr ""

#: i18n/states/TR.php:56
msgctxt "ui"
msgid "Mu&#351;"
msgstr ""

#: i18n/states/TR.php:57
msgctxt "ui"
msgid "Nev&#351;ehir"
msgstr ""

#: i18n/states/TR.php:58
msgctxt "ui"
msgid "Ni&#287;de"
msgstr ""

#: i18n/states/TR.php:59
msgctxt "ui"
msgid "Ordu"
msgstr ""

#: i18n/states/TR.php:60
msgctxt "ui"
msgid "Rize"
msgstr ""

#: i18n/states/TR.php:61
msgctxt "ui"
msgid "Sakarya"
msgstr ""

#: i18n/states/TR.php:62
msgctxt "ui"
msgid "Samsun"
msgstr ""

#: i18n/states/TR.php:63
msgctxt "ui"
msgid "Siirt"
msgstr ""

#: i18n/states/TR.php:64
msgctxt "ui"
msgid "Sinop"
msgstr ""

#: i18n/states/TR.php:65
msgctxt "ui"
msgid "Sivas"
msgstr ""

#: i18n/states/TR.php:66
msgctxt "ui"
msgid "Tekirda&#287;"
msgstr ""

#: i18n/states/TR.php:67
msgctxt "ui"
msgid "Tokat"
msgstr ""

#: i18n/states/TR.php:68
msgctxt "ui"
msgid "Trabzon"
msgstr ""

#: i18n/states/TR.php:69
msgctxt "ui"
msgid "Tunceli"
msgstr ""

#: i18n/states/TR.php:70
msgctxt "ui"
msgid "&#350;anl&#305;urfa"
msgstr ""

#: i18n/states/TR.php:71
msgctxt "ui"
msgid "U&#351;ak"
msgstr ""

#: i18n/states/TR.php:72
msgctxt "ui"
msgid "Van"
msgstr ""

#: i18n/states/TR.php:73
msgctxt "ui"
msgid "Yozgat"
msgstr ""

#: i18n/states/TR.php:74
msgctxt "ui"
msgid "Zonguldak"
msgstr ""

#: i18n/states/TR.php:75
msgctxt "ui"
msgid "Aksaray"
msgstr ""

#: i18n/states/TR.php:76
msgctxt "ui"
msgid "Bayburt"
msgstr ""

#: i18n/states/TR.php:77
msgctxt "ui"
msgid "Karaman"
msgstr ""

#: i18n/states/TR.php:78
msgctxt "ui"
msgid "K&#305;r&#305;kkale"
msgstr ""

#: i18n/states/TR.php:79
msgctxt "ui"
msgid "Batman"
msgstr ""

#: i18n/states/TR.php:80
msgctxt "ui"
msgid "&#350;&#305;rnak"
msgstr ""

#: i18n/states/TR.php:81
msgctxt "ui"
msgid "Bart&#305;n"
msgstr ""

#: i18n/states/TR.php:82
msgctxt "ui"
msgid "Ardahan"
msgstr ""

#: i18n/states/TR.php:83
msgctxt "ui"
msgid "I&#287;d&#305;r"
msgstr ""

#: i18n/states/TR.php:84
msgctxt "ui"
msgid "Yalova"
msgstr ""

#: i18n/states/TR.php:85
msgctxt "ui"
msgid "Karab&#252;k"
msgstr ""

#: i18n/states/TR.php:86
msgctxt "ui"
msgid "Kilis"
msgstr ""

#: i18n/states/TR.php:87
msgctxt "ui"
msgid "Osmaniye"
msgstr ""

#: i18n/states/TR.php:88
msgctxt "ui"
msgid "D&#252;zce"
msgstr ""

#: i18n/states/US.php:8
msgctxt "ui"
msgid "Alabama"
msgstr ""

#: i18n/states/US.php:9
msgctxt "ui"
msgid "Alaska"
msgstr ""

#: i18n/states/US.php:10
msgctxt "ui"
msgid "Arizona"
msgstr ""

#: i18n/states/US.php:11
msgctxt "ui"
msgid "Arkansas"
msgstr ""

#: i18n/states/US.php:12
msgctxt "ui"
msgid "California"
msgstr ""

#: i18n/states/US.php:13
msgctxt "ui"
msgid "Colorado"
msgstr ""

#: i18n/states/US.php:14
msgctxt "ui"
msgid "Connecticut"
msgstr ""

#: i18n/states/US.php:15
msgctxt "ui"
msgid "Delaware"
msgstr ""

#: i18n/states/US.php:16
msgctxt "ui"
msgid "District Of Columbia"
msgstr ""

#: i18n/states/US.php:17
msgctxt "ui"
msgid "Florida"
msgstr ""

#: i18n/states/US.php:18
msgctxt "US state of Georgia"
msgid "Georgia"
msgstr ""

#: i18n/states/US.php:19
msgctxt "ui"
msgid "Hawaii"
msgstr ""

#: i18n/states/US.php:20
msgctxt "ui"
msgid "Idaho"
msgstr ""

#: i18n/states/US.php:21
msgctxt "ui"
msgid "Illinois"
msgstr ""

#: i18n/states/US.php:22
msgctxt "ui"
msgid "Indiana"
msgstr ""

#: i18n/states/US.php:23
msgctxt "ui"
msgid "Iowa"
msgstr ""

#: i18n/states/US.php:24
msgctxt "ui"
msgid "Kansas"
msgstr ""

#: i18n/states/US.php:25
msgctxt "ui"
msgid "Kentucky"
msgstr ""

#: i18n/states/US.php:26
msgctxt "ui"
msgid "Louisiana"
msgstr ""

#: i18n/states/US.php:27
msgctxt "ui"
msgid "Maine"
msgstr ""

#: i18n/states/US.php:28
msgctxt "ui"
msgid "Maryland"
msgstr ""

#: i18n/states/US.php:29
msgctxt "ui"
msgid "Massachusetts"
msgstr ""

#: i18n/states/US.php:30
msgctxt "ui"
msgid "Michigan"
msgstr ""

#: i18n/states/US.php:31
msgctxt "ui"
msgid "Minnesota"
msgstr ""

#: i18n/states/US.php:32
msgctxt "ui"
msgid "Mississippi"
msgstr ""

#: i18n/states/US.php:33
msgctxt "ui"
msgid "Missouri"
msgstr ""

#: i18n/states/US.php:35
msgctxt "ui"
msgid "Nebraska"
msgstr ""

#: i18n/states/US.php:36
msgctxt "ui"
msgid "Nevada"
msgstr ""

#: i18n/states/US.php:37
msgctxt "ui"
msgid "New Hampshire"
msgstr ""

#: i18n/states/US.php:38
msgctxt "ui"
msgid "New Jersey"
msgstr ""

#: i18n/states/US.php:39
msgctxt "ui"
msgid "New Mexico"
msgstr ""

#: i18n/states/US.php:40
msgctxt "ui"
msgid "New York"
msgstr ""

#: i18n/states/US.php:41
msgctxt "ui"
msgid "North Carolina"
msgstr ""

#: i18n/states/US.php:42
msgctxt "ui"
msgid "North Dakota"
msgstr ""

#: i18n/states/US.php:43
msgctxt "ui"
msgid "Ohio"
msgstr ""

#: i18n/states/US.php:44
msgctxt "ui"
msgid "Oklahoma"
msgstr ""

#: i18n/states/US.php:45
msgctxt "ui"
msgid "Oregon"
msgstr ""

#: i18n/states/US.php:46
msgctxt "ui"
msgid "Pennsylvania"
msgstr ""

#: i18n/states/US.php:47
msgctxt "ui"
msgid "Rhode Island"
msgstr ""

#: i18n/states/US.php:48
msgctxt "ui"
msgid "South Carolina"
msgstr ""

#: i18n/states/US.php:49
msgctxt "ui"
msgid "South Dakota"
msgstr ""

#: i18n/states/US.php:50
msgctxt "ui"
msgid "Tennessee"
msgstr ""

#: i18n/states/US.php:51
msgctxt "ui"
msgid "Texas"
msgstr ""

#: i18n/states/US.php:52
msgctxt "ui"
msgid "Utah"
msgstr ""

#: i18n/states/US.php:53
msgctxt "ui"
msgid "Vermont"
msgstr ""

#: i18n/states/US.php:54
msgctxt "ui"
msgid "Virginia"
msgstr ""

#: i18n/states/US.php:55
msgctxt "ui"
msgid "Washington"
msgstr ""

#: i18n/states/US.php:56
msgctxt "ui"
msgid "West Virginia"
msgstr ""

#: i18n/states/US.php:57
msgctxt "ui"
msgid "Wisconsin"
msgstr ""

#: i18n/states/US.php:58
msgctxt "ui"
msgid "Wyoming"
msgstr ""

#: i18n/states/US.php:59
msgctxt "ui"
msgid "Armed Forces (AA)"
msgstr ""

#: i18n/states/US.php:60
msgctxt "ui"
msgid "Armed Forces (AE)"
msgstr ""

#: i18n/states/US.php:61
msgctxt "ui"
msgid "Armed Forces (AP)"
msgstr ""

#: i18n/states/US.php:62
msgctxt "ui"
msgid "American Samoa"
msgstr ""

#: i18n/states/US.php:63
msgctxt "ui"
msgid "Guam"
msgstr ""

#: i18n/states/US.php:64
msgctxt "ui"
msgid "Northern Mariana Islands"
msgstr ""

#: i18n/states/US.php:66
msgctxt "ui"
msgid "US Minor Outlying Islands"
msgstr ""

#: i18n/states/US.php:67
msgctxt "ui"
msgid "US Virgin Islands"
msgstr ""

#: i18n/states/ZA.php:8
msgctxt "ui"
msgid "Eastern Cape"
msgstr ""

#: i18n/states/ZA.php:9
msgctxt "ui"
msgid "Free State"
msgstr ""

#: i18n/states/ZA.php:10
msgctxt "ui"
msgid "Gauteng"
msgstr ""

#: i18n/states/ZA.php:11
msgctxt "ui"
msgid "KwaZulu-Natal"
msgstr ""

#: i18n/states/ZA.php:12
msgctxt "ui"
msgid "Limpopo"
msgstr ""

#: i18n/states/ZA.php:13
msgctxt "ui"
msgid "Mpumalanga"
msgstr ""

#: i18n/states/ZA.php:14
msgctxt "ui"
msgid "Northern Cape"
msgstr ""

#: i18n/states/ZA.php:15
msgctxt "ui"
msgid "North West"
msgstr ""

#: i18n/states/ZA.php:16
msgctxt "ui"
msgid "Western Cape"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/Ajax.php:84
msgid "Invalid nonce."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/Ajax.php:88
msgid "You must log in to perform this action."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/Ajax.php:92
msgid "You are not allowed to perform this action."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/Ajax.php:96
msgid "Missing required parameter: id."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/Ajax.php:100
msgid "Unknown error."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:198
msgid "Dismiss All"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:200
msgid "Unread"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:201
msgid "Inbox"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:202
msgid "Close"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:203
msgid "You are all caught up!"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-in-product-notifications/Services/View.php:204
msgid "ago"
msgstr ""

#. Translators: %s The name of the property that does not exist on the response object.
#: vendor-prefixed/caseproof/ground-level-mothership/Api/Response.php:108
msgid "Property %s does not exist on the response object."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Credentials.php:76
msgid "Cannot store credentials in database; found in environment variables or constants."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:268
msgid "Could not install add-on."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:380
msgid "Please enter your license key to access add-ons."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:397
msgid "There was an issue connecting with the API."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/AddonsManager.php:426
msgid "Could not install add-on. Please download and install manually."
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:82
msgid "License activated successfully"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:95
msgid "License deactivated successfully"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:134
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:173
msgid "License Key: "
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:209
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:266
msgid "Insufficient permissions"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:214
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:271
msgid "Invalid nonce"
msgstr ""

#. Translators: %s is the response error message.
#: vendor-prefixed/caseproof/ground-level-mothership/Manager/LicenseManager.php:218
msgid "License activation failed: %s"
msgstr ""

#: vendor-prefixed/caseproof/ground-level-mothership/Views/products.php:126
msgid "There were no Add-ons found for your License Key."
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/App.php:58
#: vendor-prefixed/caseproof/growth-tools/src/App.php:59
#: vendor-prefixed/caseproof/growth-tools/src/App.php:205
msgid "Growth Tools"
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/App.php:101
msgid "Installed"
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/App.php:105
msgid "Install"
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/Helper/AddonHelper.php:22
msgid "Could not activate addon. Please check user permissions."
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/Helper/AddonHelper.php:31
msgid "Addon activated."
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/Helper/AddonHelper.php:36
msgid "Could not activate addon. Please activate from the Plugins page."
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/Helper/AddonHelper.php:41
msgid "Could not activate addon. Please refresh page and try again."
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/Helper/AddonHelper.php:62
msgid "Could not deactivate the theme. Please deactivate from the theme page."
msgstr ""

#: vendor-prefixed/caseproof/growth-tools/src/Helper/AddonHelper.php:72
msgid "Could not deactivate the addon. Please deactivate from the Plugins page."
msgstr ""

#: js/blocks/account-form/index.js:7
#: js/build/blocks.js:1
msgid "Account Form"
msgstr ""

#: js/blocks/account-form/index.js:10
#: js/build/blocks.js:1
msgid "Display the MemberPress Account form."
msgstr ""

#: js/blocks/account-form/index.js:11
#: js/blocks/pro-account-tabs/index.js:25
#: js/blocks/pro-checkout/index.js:25
#: js/build/blocks.js:1
msgid "membership account form"
msgstr ""

#: js/blocks/account-form/index.js:22
#: js/build/blocks.js:1
msgid "MemberPress Account Form"
msgstr ""

#: js/blocks/account-form/index.js:23
#: js/build/blocks.js:1
msgid "Display the MemberPress Account form"
msgstr ""

#: js/blocks/account-info/edit.js:46
#: js/build/blocks.js:1
msgid "Field Slug:"
msgstr ""

#: js/blocks/account-info/index.js:10
#: js/build/blocks.js:1
msgid "Account Info"
msgstr ""

#: js/blocks/account-info/index.js:13
#: js/build/blocks.js:1
msgid "Display the user meta field, which is chosen by slug."
msgstr ""

#: js/blocks/account-info/index.js:14
#: js/build/blocks.js:1
msgid "membership user info"
msgstr ""

#: js/blocks/account-links/index.js:10
#: js/build/blocks.js:1
msgid "Account Links"
msgstr ""

#: js/blocks/account-links/index.js:13
#: js/build/blocks.js:1
msgid "Display the list of MemberPress Account links."
msgstr ""

#: js/blocks/account-links/index.js:14
#: js/build/blocks.js:1
msgid "membership account links"
msgstr ""

#: js/blocks/login-form/edit.js:18
#: js/build/blocks.js:1
msgid "Login Redirect URL?"
msgstr ""

#: js/blocks/login-form/index.js:11
#: js/build/blocks.js:1
msgid "Login Form"
msgstr ""

#: js/blocks/login-form/index.js:14
#: js/build/blocks.js:1
msgid "Display the MemberPress Login form"
msgstr ""

#: js/blocks/login-form/index.js:15
#: js/build/blocks.js:1
msgid "membership login form"
msgstr ""

#: js/blocks/membership-signup/edit.js:15
#: js/build/blocks.js:1
msgid "-- Select a Membership"
msgstr ""

#: js/blocks/membership-signup/edit.js:26
#: js/build/blocks.js:1
msgid "Select a Membership registration form to display"
msgstr ""

#: js/blocks/membership-signup/index.js:14
#: js/build/blocks.js:1
msgid "Display a signup form for a MemberPress membership."
msgstr ""

#: js/blocks/membership-signup/index.js:18
#: js/build/blocks.js:1
msgid "membership signup form"
msgstr ""

#: js/blocks/pro-account-tabs/index.js:21
#: js/build/blocks.js:1
msgid "MP ReadyLaunch™ Account"
msgstr ""

#: js/blocks/pro-account-tabs/index.js:24
#: js/build/blocks.js:1
msgid "MemberPress Account Tabs."
msgstr ""

#: js/blocks/pro-checkout/edit.js:46
#: js/build/blocks.js:1
msgid "Select Membership"
msgstr ""

#: js/blocks/pro-checkout/index.js:21
#: js/build/blocks.js:1
msgid "MP ReadyLaunch™ Registration"
msgstr ""

#: js/blocks/pro-checkout/index.js:24
#: js/blocks/pro-login-form/index.js:24
#: js/build/blocks.js:1
msgid "MemberPress Checkout"
msgstr ""

#: js/blocks/pro-login-form/index.js:21
#: js/build/blocks.js:1
msgid "MP ReadyLaunch™ Login"
msgstr ""

#: js/blocks/pro-pricing-table/edit.js:44
#: js/build/blocks.js:1
msgid "Select Group"
msgstr ""

#: js/blocks/pro-pricing-table/edit.js:67
#: js/build/blocks.js:1
msgid "Show Global Pricing Headline"
msgstr ""

#: js/blocks/pro-pricing-table/index.js:20
#: js/build/blocks.js:1
msgid "MP ReadyLaunch™ Pricing Table"
msgstr ""

#: js/blocks/pro-pricing-table/index.js:23
#: js/build/blocks.js:1
msgid "MemberPress Pricing Table Options"
msgstr ""

#: js/blocks/protected-content/index.js:12
#: js/build/blocks.js:1
msgid "Protected"
msgstr ""

#: js/blocks/protected-content/index.js:13
#: js/build/blocks.js:1
msgid "Layout blocks and content protected by MemberPress."
msgstr ""

#: js/blocks/protected-content/index.js:19
#: js/build/blocks.js:1
msgid "login protected membership hidden rule"
msgstr ""

#: js/blocks/protected-content/index.js:45
#: js/build/blocks.js:1
msgid "Access Rule"
msgstr ""

#: js/blocks/protected-content/index.js:49
#: js/build/blocks.js:1
msgid "Select a Rule to determine member access."
msgstr ""

#: js/blocks/protected-content/index.js:55
#: js/build/blocks.js:1
msgid "Select a Rule"
msgstr ""

#: js/blocks/protected-content/index.js:78
#: js/build/blocks.js:1
msgid "If Allowed"
msgstr ""

#: js/blocks/protected-content/index.js:79
#: js/build/blocks.js:1
msgid "When set to \"show\", the content is shown to authorized members only. When set to \"hide\", the content is hidden from authorized members."
msgstr ""

#: js/blocks/protected-content/index.js:99
#: js/build/blocks.js:1
msgid "Unauthorized Action"
msgstr ""

#: js/blocks/protected-content/index.js:101
#: js/build/blocks.js:1
msgid "Specify how to handle unauthorized access."
msgstr ""

#: js/blocks/protected-content/index.js:107
#: js/build/blocks.js:1
msgid "Hide Only"
msgstr ""

#: js/blocks/protected-content/index.js:111
#: js/build/blocks.js:1
msgid "Show Message"
msgstr ""

#: js/blocks/protected-content/index.js:115
#: js/build/blocks.js:1
msgid "Show Login Form"
msgstr ""

#: js/blocks/protected-content/index.js:119
#: js/build/blocks.js:1
msgid "Show Login Form & Message"
msgstr ""

#: js/blocks/protected-content/index.js:133
#: js/build/blocks.js:1
msgid "Unauthorized Message"
msgstr ""

#: js/blocks/protected-content/index.js:134
#: js/build/blocks.js:1
msgid "Message to show on Unauthorized Access"
msgstr ""

#: js/blocks/protected-content/index.js:153
#: js/build/blocks.js:1
msgid "MemberPress Protected Content"
msgstr ""

#: js/blocks/protected-content/index.js:154
#: js/build/blocks.js:1
msgid "Add child blocks that will only be shown to authorized members."
msgstr ""

#: js/blocks/subscriptions/edit.js:22
#: js/build/blocks.js:1
msgid "-- Sort by"
msgstr ""

#: js/blocks/subscriptions/edit.js:32
#: js/build/blocks.js:1
msgid "-- Sort order"
msgstr ""

#: js/blocks/subscriptions/edit.js:33
#: js/build/blocks.js:1
msgid "Ascending"
msgstr ""

#: js/blocks/subscriptions/edit.js:34
#: js/build/blocks.js:1
msgid "Descending"
msgstr ""

#: js/blocks/subscriptions/edit.js:46
#: js/build/blocks.js:1
msgid "Sort by:"
msgstr ""

#: js/blocks/subscriptions/edit.js:54
#: js/build/blocks.js:1
msgid "Sort order:"
msgstr ""

#: js/blocks/subscriptions/edit.js:86
#: js/build/blocks.js:1
msgid "Use Membership Access URLs"
msgstr ""

#: js/blocks/subscriptions/edit.js:87
#: js/build/blocks.js:1
msgid "Makes the Subscription name clickable, pointing to the Membership Access URL you have set in the Membership settings (Advanced tab)"
msgstr ""

#: js/blocks/subscriptions/index.js:14
#: js/build/blocks.js:1
msgid "Display the subscriptions list of currently logged-in user."
msgstr ""

#: js/blocks/subscriptions/index.js:15
#: js/build/blocks.js:1
msgid "membership subscriptions"
msgstr ""
