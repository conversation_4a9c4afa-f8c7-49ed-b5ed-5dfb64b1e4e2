=== Uncanny Automator - Easy Automation, Integration, Webhooks & Workflow Builder Plugin ===
Contributors: uncannyautomator, uncannyowl, smub
Tags: automation, google sheets, openai, learndash, webhooks
Requires at least: 5.6
Tested up to: 6.8.1
Requires PHP: 7.3
Stable tag: 6.6.0
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Uncanny Automator is the easiest and most powerful way to connect your WordPress plugins, sites and apps together with powerful automations.

== Description ==

Uncanny Automator is the easiest and most powerful way to automate your WordPress site with no code. Build automations in minutes that connect your WordPress plugins, sites and apps together using billions of recipe combinations.

Here's how Uncanny Automator works:

When something happens, Automator can make other things happen.

When a user buys a product, add them to a membership level, enroll them in a course and pass their information to Google Sheets.

It's that simple! Here's a video outlining how it all works.

https://www.youtube.com/watch?v=LMR5YIPu2Kk

If you've used Zapier, setting things up will be intuitive. And if not, that's okay too!

= E-commerce Automation =

Improve customer engagement by having your purchases trigger marketing automation campaigns, award store credit, promote 5-star reviews, schedule time-limited bonus offers and offer profile-driven discounts. Or for advanced reporting and customer service, create WooCommerce automations that send purchase details to Google Sheets, Slack and ActiveCampaign with our native integrations. It all happens automatically!

= Google Sheets Automation =

Build powerful reports and dashboards based on almost any WordPress activity or data with 1-click Google Sheets integration. Track purchases, course completions, blog post updates, forum posts and more. Not only can you create new rows for reports with Uncanny Automator, but you can update existing records, making it perfect for dashboard reporting.

= Webhook Automation =

Looking for the most powerful and comprehensive webhook support to connect your WordPress site to other apps? Uncanny Automator has support for security headers, any request method and any data format (including nesting support, JSON, XML, arrays and more). Use our 1-click sample generation and debug records to simplify connecting to other sites and systems, and send unlimited outgoing webhooks with the free version.

= LearnDash and E-Learning Automation =

Personalize student experiences and deliver better learning outcomes with no-code automations. Notify an instructor when users fail a quiz and enroll them in a remedial course–automatically. Add users to groups based on performance to allow easy collaboration. Automate student outreach when users fall behind in their coursework and offer easy learning interventions.

= Social Media Automation =

Spend less time sharing your posts and announcements on social media with automatic posting. Automatically post anything you want to Facebook Pages, Facebook Groups, Twitter, LinkedIn and Instagram, including with images and links. Sign up for a free account to get 250 credits for social posting; Pro plugin users get unlimited posting.

= OpenAI Automation =

Connect your new posts and site activity to OpenAI, with full support for ChatGPT and Dall-E models. Use Automator and the OpenAI to generate article summaries, social media posts, SEO descriptions, translations, email campaigns, featured images and more.

= Marketing Automation =

Integrate your favourite CRM with your WordPress plugins for full marketing automation. Add or remove tags based on course activity, purchases and more, and keep your email lists updated automatically. Or, with Automator Pro and Loops, send emails out in bulk and use Automator as your own CRM.

= Free doesn't mean limited =

The free version of Automator is incredibly powerful and comes with built-in automation and integration support for all of these popular apps and WordPress plugins:

= Supported apps =

- [ActiveCampaign](https://automatorplugin.com/integration/activecampaign/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Anthropic](https://automatorplugin.com/integration/anthropic/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [AWeber](https://automatorplugin.com/integration/aweber/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Bitly](https://automatorplugin.com/integration/bitly/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Bluesky](https://automatorplugin.com/integration/bluesky/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Brevo](https://automatorplugin.com/integration/brevo/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Campaign Monitor](https://automatorplugin.com/integration/campaign-monitor/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [ClickUp](https://automatorplugin.com/integration/clickup/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Cohere](https://automatorplugin.com/integration/cohere/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Constant Contact](https://automatorplugin.com/integration/constant-contact/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [ConvertKit](https://automatorplugin.com/integration/convertkit/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [DeepSeek](https://automatorplugin.com/integration/deepseek/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Discord](https://automatorplugin.com/integration/discord/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Drip](https://automatorplugin.com/integration/drip/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Facebook Groups](https://automatorplugin.com/integration/facebook-groups/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Facebook Lead Ads](https://automatorplugin.com/integration/facebook-lead-ads/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Facebook Pages](https://automatorplugin.com/integration/facebook/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [GetResponse](https://automatorplugin.com/integration/getresponse/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Google Calendar](https://automatorplugin.com/integration/google-calendar/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Google Contacts](https://automatorplugin.com/integration/google-contacts/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Google Gemini](https://automatorplugin.com/integration/gemini/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Google Sheets](https://automatorplugin.com/integration/google-sheets/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Google Sheets Web App](https://automatorplugin.com/integration/sheets-web-app/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [GoTo Training](https://automatorplugin.com/integration/gototraining/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [GoTo Webinar](https://automatorplugin.com/integration/gotowebinar/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Help Scout](https://automatorplugin.com/integration/help-scout/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [HubSpot](https://automatorplugin.com/integration/hubspot/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Instagram](https://automatorplugin.com/integration/instagram/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Keap](https://automatorplugin.com/integration/keap/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [LinkedIn Pages](https://automatorplugin.com/integration/linkedin-pages/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Mailchimp](https://automatorplugin.com/integration/mailchimp/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [MailerLite](https://automatorplugin.com/integration/mailerlite/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Mautic](https://automatorplugin.com/integration/mautic/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Mistral AI](https://automatorplugin.com/integration/mistral/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Microsoft Teams](https://automatorplugin.com/integration/microsoft-teams/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Notion](https://automatorplugin.com/integration/notion/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Ontraport](https://automatorplugin.com/integration/ontraport/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [OpenAI and GPT](https://automatorplugin.com/integration/openai/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Perplexity](https://automatorplugin.com/integration/perplexity/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Salesforce](https://automatorplugin.com/integration/salesforce/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Elite)
- [Sendy](https://automatorplugin.com/integration/sendy/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Slack](https://automatorplugin.com/integration/slack/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Stripe](https://automatorplugin.com/integration/stripe/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Telegram](https://automatorplugin.com/integration/telegram/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Threads](https://automatorplugin.com/integration/threads/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Trello](https://automatorplugin.com/integration/trello/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Twilio](https://automatorplugin.com/integration/twilio/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Twitter](https://automatorplugin.com/integration/twitter/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WhatsApp](https://automatorplugin.com/integration/whatsapp/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [xAI](https://automatorplugin.com/integration/xai/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Zoho Campaigns](https://automatorplugin.com/integration/zoho-campaigns/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Zoom Meetings](https://automatorplugin.com/integration/zoom-meetings/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Zoom Webinars](https://automatorplugin.com/integration/zoom-webinars/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)

= WordPress Plugins Integrations =

- [Advanced Ads](https://automatorplugin.com/integration/advanced-ads/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Advanced Coupons](https://automatorplugin.com/integration/advanced-coupons/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Advanced Custom Fields](https://automatorplugin.com/integration/advanced-custom-fields/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [AffiliateWP](https://automatorplugin.com/integration/affiliatewp/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Airtable](https://automatorplugin.com/integration/airtable/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Amelia](https://automatorplugin.com/integration/amelia/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [ARMember](https://automatorplugin.com/integration/armember/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Automator Core](https://automatorplugin.com/integration/automator-core/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Autonami](https://automatorplugin.com/integration/autonami/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [BadgeOS](https://automatorplugin.com/integration/badgeos/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [bbPress](https://automatorplugin.com/integration/bbpress/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Bricks Builder](https://automatorplugin.com/integration/bricks-builder/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [BuddyBoss](https://automatorplugin.com/integration/buddyboss/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [BuddyPress](https://automatorplugin.com/integration/buddypress/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Caldera Forms](https://automatorplugin.com/integration/caldera-forms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [CSV](https://automatorplugin.com/integration/csv/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Charitable](https://automatorplugin.com/integration/charitable/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Code Snippets](https://automatorplugin.com/integration/code-snippets/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Contact Form 7](https://automatorplugin.com/integration/contact-form-7/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Custom Action](https://automatorplugin.com/integration/custom-action/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Date and Time](https://automatorplugin.com/integration/date-and-time/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Divi](https://automatorplugin.com/integration/divi/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Duplicator](https://automatorplugin.com/integration/duplicator/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Easy Affiliate](https://automatorplugin.com/integration/easy-affiliate/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Easy Digital Downloads](https://automatorplugin.com/integration/easy-digital-downloads/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Easy Digital Downloads - Recurring Payments](https://automatorplugin.com/integration/easy-digital-downloads-recurring-payments/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Easy Digital Downloads - Software Licensing](https://automatorplugin.com/integration/easy-digital-downloads-software-licensing/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Elementor Pro](https://automatorplugin.com/integration/elementor/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Emails](https://automatorplugin.com/integration/emails/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Events Manager](https://automatorplugin.com/integration/events-manager/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Everest Forms](https://automatorplugin.com/integration/everest-forms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Fluent Forms](https://automatorplugin.com/integration/wp-fluent-forms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Fluent Support](https://automatorplugin.com/integration/fluent-support/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [FluentBooking](https://automatorplugin.com/integration/fluentbooking/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [FluentCommunity](https://automatorplugin.com/integration/fluentcommunity/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [FluentCRM](https://automatorplugin.com/integration/fluentcrm/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Formidable Forms](https://automatorplugin.com/integration/formidable-forms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Forminator](https://automatorplugin.com/integration/forminator/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [GamiPress](https://automatorplugin.com/integration/gamipress/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Generator](https://automatorplugin.com/integration/generator/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [GiveWP](https://automatorplugin.com/integration/givewp/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Gravity Forms](https://automatorplugin.com/integration/gravity-forms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [GravityKit](https://automatorplugin.com/integration/gravitykit/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Groundhogg](https://automatorplugin.com/integration/groundhogg/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [H5P](https://automatorplugin.com/integration/h5p/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [HappyForms](https://automatorplugin.com/integration/happyforms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Heroic Knowledge Base](https://automatorplugin.com/integration/heroic-knowledge-base/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [IFTTT](https://automatorplugin.com/integration/ifttt/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Integrately](https://automatorplugin.com/integration/integrately/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Integromat](https://automatorplugin.com/integration/integromat/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [JetEngine](https://automatorplugin.com/integration/jetengine/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [JetFormBuilder](https://automatorplugin.com/integration/jetformbuilder/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Jetpack CRM](https://automatorplugin.com/integration/jetpack-crm/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [JSON](https://automatorplugin.com/integration/json/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Kadence](https://automatorplugin.com/integration/kadence/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [KonnectzIT](https://automatorplugin.com/integration/konnectzit/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [LearnDash](https://automatorplugin.com/integration/learndash/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [LearnDash Achievements](https://automatorplugin.com/integration/learndash-achievements/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [LearnPress](https://automatorplugin.com/integration/learnpress/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [LifterLMS](https://automatorplugin.com/integration/lifterlms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Logging](https://automatorplugin.com/integration/logging/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Magic Button](https://automatorplugin.com/integration/magic-button/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [MailPoet](https://automatorplugin.com/integration/mailpoet/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Mailster](https://automatorplugin.com/integration/mailster/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Make](https://automatorplugin.com/integration/make/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [MasterStudy LMS](https://automatorplugin.com/integration/masterstudy-lms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Memberium For Keap](https://automatorplugin.com/integration/memberium-for-keap/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [MemberMouse](https://automatorplugin.com/integration/membermouse/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [MemberPress](https://automatorplugin.com/integration/memberpress/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [MemberPress Courses](https://automatorplugin.com/integration/memberpress-courses/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Meta Box](https://automatorplugin.com/integration/meta-box/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Modern Events Calendar](https://automatorplugin.com/integration/modern-events-calendar/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [myCred](https://automatorplugin.com/integration/mycred/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Newsletter](https://automatorplugin.com/integration/newsletter/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Ninja Forms](https://automatorplugin.com/integration/ninja-forms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [OptinMonster](https://automatorplugin.com/integration/optinmonster/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Pabbly Connect](https://automatorplugin.com/integration/pabbly-connect/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Paid Memberships Pro](https://automatorplugin.com/integration/paid-memberships-pro/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [PeepSo](https://automatorplugin.com/integration/peepso/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Plugin Actions](https://automatorplugin.com/integration/plugin-actions/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Popup Maker](https://automatorplugin.com/integration/popup-maker/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Presto Player](https://automatorplugin.com/integration/presto-player/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Pretty Links](https://automatorplugin.com/integration/pretty-links/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [QR Code](https://automatorplugin.com/integration/qr-code/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [RafflePress](https://automatorplugin.com/integration/rafflespress/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Restrict Content Pro](https://automatorplugin.com/integration/restrict-content/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [RSS](https://automatorplugin.com/integration/rss/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Run Code](https://automatorplugin.com/integration/run-code/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Schedule](https://automatorplugin.com/integration/schedule/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [SliceWP](https://automatorplugin.com/integration/slicewp/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [SeedProd](https://automatorplugin.com/integration/seedprod/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Studiocart](https://automatorplugin.com/integration/studiocart/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [SureCart](https://automatorplugin.com/integration/surecart/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [SureMembers](https://automatorplugin.com/integration/suremembers/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [The Events Calendar](https://automatorplugin.com/integration/the-events-calendar/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Thrive Apprentice](https://automatorplugin.com/integration/thrive-apprentice/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Thrive Architect](https://automatorplugin.com/integration/thrive-architect/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Thrive Leads](https://automatorplugin.com/integration/thrive-leads/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Thrive Ovation](https://automatorplugin.com/integration/thrive-ovation/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Thrive Quiz Builder](https://automatorplugin.com/integration/thrive-quiz-builder/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Thrive Theme Builder](https://automatorplugin.com/integration/thrive-theme-builder/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Thrive Ultimatum](https://automatorplugin.com/integration/thrive-ultimatum/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Tin Canny Reporting](https://automatorplugin.com/integration/tin-canny-reporting-for-learndash/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Tutor LMS](https://automatorplugin.com/integration/tutor-lms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Ultimate Member](https://automatorplugin.com/integration/ultimate-member/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Uncanny Codes](https://automatorplugin.com/integration/uncanny-codes/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Uncanny Continuing Education Credits](https://automatorplugin.com/integration/uncanny-ceus/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Uncanny Groups](https://automatorplugin.com/integration/uncanny-groups/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Uncanny Toolkit](https://automatorplugin.com/integration/uncanny-toolkit/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Upsell Plugin](https://automatorplugin.com/integration/upsell-plugin/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [URL](https://automatorplugin.com/integration/url/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [UserFeedback](https://automatorplugin.com/integration/userfeedback/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Webhooks](https://automatorplugin.com/integration/webhooks/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Wholesale Suite](https://automatorplugin.com/integration/wholesale-suite/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [Wishlist Member](https://automatorplugin.com/integration/wishlist-member/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WooCommerce](https://automatorplugin.com/integration/woocommerce/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WooCommerce Bookings](https://automatorplugin.com/integration/woocommerce-bookings/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WooCommerce Memberships](https://automatorplugin.com/integration/woocommerce-memberships/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WooCommerce ShipStation](https://automatorplugin.com/integration/woocommerce-shipstation/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WooCommerce Subscriptions](https://automatorplugin.com/integration/woocommerce-subscriptions/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WordPress](https://automatorplugin.com/integration/wordpress-core/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WordPress Multisite](https://automatorplugin.com/integration/wordpress-multisite/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [WordPress Download Manager](https://automatorplugin.com/integration/wp-download-manager/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Admin](https://automatorplugin.com/integration/wp-admin/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [WP All Import](https://automatorplugin.com/integration/wp-all-import/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Bitly](https://automatorplugin.com/integration/wp-bitly/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro) (Deprecated)
- [WP Courseware](https://automatorplugin.com/integration/wp-courseware/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Fusion](https://automatorplugin.com/integration/wp-fusion/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Fusion Lite](https://automatorplugin.com/integration/wp-fusion-lite/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Job Manager](https://automatorplugin.com/integration/wp-job-manager/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP LMS](https://automatorplugin.com/integration/wp-lms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Mail SMTP Pro](https://automatorplugin.com/integration/wp-mail-smtp-pro/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Simple Pay](https://automatorplugin.com/integration/wp-simple-pay/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP User Manager](https://automatorplugin.com/integration/wp-user-manager/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP Webhooks](https://automatorplugin.com/integration/wp-webhooks/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WP-Polls](https://automatorplugin.com/integration/wp-polls/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WPCode](https://automatorplugin.com/integration/wpcode/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WPForms](https://automatorplugin.com/integration/wp-forms/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [WS Form LITE](https://automatorplugin.com/integration/ws-form-lite/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [wpDiscuz](https://automatorplugin.com/integration/wpdiscuz/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [wpForo](https://automatorplugin.com/integration/wp-foro/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)
- [XML](https://automatorplugin.com/integration/xml/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list) (Pro)
- [Zapier](https://automatorplugin.com/integration/zapier/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list)

Over 350 automation triggers and actions are available for the plugins and apps listed above in the free version. There's straightforward documentation in our [Knowledge Base](https://automatorplugin.com/knowledge-base/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=free_integrations_list), including some quick and straightforward instructional videos. Developers, there's robust documentation and code samples for you too!

Beyond the plugin and app integrations, this free version of Uncanny Automator also supports common WP triggers and actions in automations:

- A user views a page
- A user submits a comment
- A user logs in
- A user publishes a post
- Send an email
- Create a post
- Add a WP role

All WP plugin triggers and actions included in Uncanny Automator come with a forever free license, which allows unlimited usage on your WordPress site. By creating a free, optional Automator account, you can unlock [250 credits](https://automatorplugin.com/knowledge-base/what-are-credits) to try out app integrations. You can use credits on your site to:

- Post new content to Facebook Groups, Facebook Pages, Twitter and Instagram
- Pass records to Google Sheets (to generate charts or reports or manipulate data outside of WordPress)
- Notify staff of key events in Slack
- Register customers in Zoom events on product purchase
- Send Mailchimp campaigns when new blog posts are published
- Generate, proofread or translate text with OpenAI and GPT
- and more

= Take automation to the next level =

[Uncanny Automator Pro](https://automatorplugin.com/pricing/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=take_automation_next_level), our premium plugin, adds even more features and integrations, including the ability to create users and posts, delay and schedule actions, connect multiple sites together, add conditions to actions, and unlock unlimited use of app integrations like Google Sheets and Twitter. It also triples the number of available triggers and actions.

Pro also adds features like [Run Now](https://automatorplugin.com/knowledge-base/run-now/?utm_source=wp_repo_automator&utm_medium=readme) and [Loops](https://automatorplugin.com/knowledge-base/user-loops/?utm_source=wp_repo_automator&utm_medium=readme), which allow you to do things like send emails to all users matching certain criteria, tagging users based on site activity, or even generating instant reports in Google Sheets of all users that completed a specific course, purchased a certain product, or anything else you can imagine. 

Here are some of the other really cool automations you can create with [Automator Pro](https://automatorplugin.com/pricing/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=create_really_cool_automations):

- When a user makes a purchase using WooCommerce on one site, create a user on another WP site, send the user a link to set a new password, enroll the user in a course and add a membership level.
- When a user submits a form, add the user to a mailing list if their email address matches a certain domain, add them to a social group and schedule their trial access to expire in 7 days if they don't make a purchase.
- When a new course is published, send an email campaign, post to X/Twitter and Facebook with GPT-generated content, post to the BuddyBoss sitewide activity stream and notify instructors in a Slack channel.
- Whenever you want, generate a list of all users in a group that haven't yet completed a course.

And here are some of the key features in the Pro version that allow incredibly flexible and powerful automations for WP:

- Run recipes for logged out users with [Everyone recipes](https://automatorplugin.com/knowledge-base/anonymous-recipes/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=pro_recipe_example); you can even create and update existing users.
- Have external apps [trigger recipes](https://automatorplugin.com/knowledge-base/webhook-triggers/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=pro_recipe_example) and vice versa.
- Set up [buttons and links](https://automatorplugin.com/knowledge-base/magic-button/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=pro_recipe_example) that can trigger any recipe on click.
- Set custom user and post meta, both individually and in bulk via loops.
- [Delay or Schedule an action](https://automatorplugin.com/knowledge-base/scheduled-actions/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=pro_recipe_example)
- [Filter actions](https://automatorplugin.com/knowledge-base/action-filters-conditions/) so they only run when conditions are met, like a matching user email domain or WordPress role.
- [Run any WordPress hook](https://automatorplugin.com/knowledge-base/run-a-wordpress-hook/) or [call custom functions](https://automatorplugin.com/knowledge-base/call-a-custom-function-method/) in your recipes.
- Automatic [log pruning](https://automatorplugin.com/knowledge-base/using-automator-logs/#cleaning-up-the-logs) to keep log size down.

Plus licenses and higher get access to our [Custom User Fields](https://automatorplugin.com/custom-user-fields-addon/?utm_source=wp_repo_automator&utm_medium=readme), [Restrict Content](https://automatorplugin.com/restrict-content-addon/?utm_source=wp_repo_automator&utm_medium=readme), and [User Lists](https://automatorplugin.com/user-lists-addon/?utm_source=wp_repo_automator&utm_medium=readme) addons which makes it really easy to create and manage user profile fields in WordPress. Automatically add new fields to user edit pages and connect fields to your recipes and plugins like Advanced Custom Fields.

An [Automator Pro](https://automatorplugin.com/pricing/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=pro_support) license also includes access to our world-class premium support. 

For the full list of triggers and actions, make sure to check out [this list](https://automatorplugin.com/all-triggers-and-actions/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=full_list_below_recipe_examples).

Here's what other people are saying about Uncanny Automator:

- [Automate Your Workflows Like a Pro](https://www.wpexplorer.com/uncanny-automator-review/)
- [Uncanny Automator - Zapier for WordPress](https://helpiewp.com/uncanny-automator/)
- [It's Like Zapier For Your WordPress Site](https://wplift.com/uncanny-automator-review)
- [Automate WordPress Like Zapier (Or With Zapier!)](https://wpmayor.com/uncanny-automator-review-automate-wordpress-like-zapier-or-with-zapier/)
- [E-Learning Automation with Uncanny Automator](https://www.learndash.com/e-learning-automation-with-uncanny-automator/)

== Frequently Asked Questions ==

= Where can I find Automator documentation and more info? =

You can learn more about Uncanny Automator on our website at [https://automatorplugin.com/](https://automatorplugin.com/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=more_documentation) and our Knowledge Base is here: [https://automatorplugin.com/knowledge-base/](https://automatorplugin.com/knowledge-base/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=more_documentation)

= How do I get started with my first automation? =

Have a look at [https://automatorplugin.com/knowledge-base/creating-a-recipe/](https://automatorplugin.com/knowledge-base/creating-a-recipe/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=how_to_create_first_automation). There's also an onboarding wizard inside the plugin to help you build your first recipe. Hopefully it will be the first of many!

= Will Uncanny Automator slow down my website? =

Uncanny Automator is built from the ground up for efficiency and performance. Automator adds negligible overhead to WordPress sites and has been rigorously tested across dozens of hosting environments.

= What are the limits of your free version? =

All integrations, triggers, actions and tokens listed [here](https://automatorplugin.com/all-triggers-and-actions/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=what_are_the_limits_of_your_free_version) that don't have a "Pro" label are available in our free version. For the WordPress plugins listed, we include a forever-free license that allows you to run recipes with these triggers and actions an unlimited number of times. There are also no limits on the number of recipes you can set up or what you can include in each. For recipes that include apps, like Twitter and Facebook, you can create a free account to unlock 250 credits to try out these integrations.

= Is Automator translation friendly? =

Absolutely! The plugin was created with translation in mind and tested with a variety of translation plugins. We will support any translation issues you run into.

= How can I request an integration for a plugin? =

The best thing you can do is reach out to the creator of the plugin that you want an integration for. Let them know you're using Uncanny Automator and that they could greatly extend the capabilities of their plugin by helping us develop an integration for it. As more plugin authors become aware of Uncanny Automator and realize its potential for their users, we'll be able to continue to invest in expanding Uncanny Automator's capabilities! Also, fill out [this form](https://automatorplugin.com/feedback/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=request_integration) on our site to let us know what you're looking for.

= How can I add an integration for my plugin? =

Check out our developer documentation over at [https://developer.automatorplugin.com/](https://developer.automatorplugin.com/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=code_integration). Also consider reaching out to our team, maybe we can help or even turn it into an official integration!

= Can I automatically share posts & updates on social media? =

Yes. It is easy to set up automation and share content on Social Media platforms. Set up integrations with Instagram, Facebook Pages, Facebook Groups, LinkedIn Pages and Twitter by creating a free account to unlock 250 free credits.

= Do I need coding skills or help from a developer? =

No coding skills are required. Uncanny Automator is built for non-developers to build automations using our intuitive recipe builder.

= Does Automator work with LearnDash? =

Absolutely! LearnDash is our most popular integration. Automator provides an easy way to build powerful LearnDash activity reports, personalize learning paths, connect LearnDash to live events and more.

= How does Automator use OpenAI? =

By connecting your recipes to an OpenAI API account, you can send prompts to OpenAI and use the responses in other actions. Generate titles and descriptions, excerpts for social media, translations, new blog posts and more; you can even perform sentiment analysis on submitted text. Automator supports GPT-3.5, GPT-4 and other OpenAI models.

= Does Uncanny Automator allow me to customize the look and design of the emails it sends? =

Yes, with WordPress's "Send Email" action, send rich text, plain text and raw HTML emails. Create follow-up emails with your own custom HTML templates.

= Why should I consider upgrading to Uncanny Automator Pro? =

You can use the free version forever without purchasing the Uncanny Automator Pro version. However, with Uncanny Automator Pro, you'll get access to more automation triggers and actions, additional features, and unlimited credits for app integrations. Visit [our plans page](https://automatorplugin.com/pricing/?utm_source=wp_repo_automator&utm_medium=readme&utm_campaign=faq) to learn more.

= Who is Uncanny Owl? =

We're a Toronto-based WordPress company specializing in elearning and automation solutions for WordPress. We're behind the popular [Uncanny LearnDash Toolkit plugin](https://wordpress.org/plugins/uncanny-learndash-toolkit/) and we also have a suite of highly-acclaimed [commercial LearnDash add-ons](https://www.uncannyowl.com/plugins/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=who_is_uncannyowl).

== Screenshots ==

1. Set up your first recipe in 5 minutes
2. Add one or more triggers from dozens of plugins and thousands of apps
3. Identify what starts the recipe, like a purchase or form submission
4. Set conditions and options for the trigger(s)
5. Add one or more actions from any of the available integrations
6. When the triggers are completed, actions run automatically
7. Customize your actions to do exactly what you need
8. Create powerful recipes that just work, all with one plugin

== Changelog ==

= 6.6.0 [2025-06-05] =

**New App Integrations:**

* [Anthropic](https://automatorplugin.com/integration/anthropic/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5818  
* [Cohere](https://automatorplugin.com/integration/cohere/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5819  
* [DeepSeek](https://automatorplugin.com/integration/deepseek/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5821  
* [Google Gemini](https://automatorplugin.com/integration/gemini/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5823  
* [Mistral AI](https://automatorplugin.com/integration/mistral/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5827  
* [Perplexity](https://automatorplugin.com/integration/perplexity/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5829  
* [xAI](https://automatorplugin.com/integration/xai/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5825  

**New Plugin Integrations:**

* [FluentCommunity](https://automatorplugin.com/integration/fluentcommunity/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5668  
* [Mailster](https://automatorplugin.com/integration/mailster/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=changelog) #5405  

**New Triggers:**

* ARMember - A user is added to a membership plan #5769  
* Events Manager - A user publishes a new event #5366  
* FluentCommunity - A user completes a course #5704  
* FluentCommunity - A user completes a lesson #5714  
* FluentCommunity - A user is enrolled in a course #5702  
* FluentCommunity - A user joins a space #5700  
* FluentCommunity - A user posts to a space #5709  
* Mailster - A new subscriber is added to a Mailster list #5406  

**New Actions:**

* Anthropic - Use a prompt to generate a text response with a Claude model #5817  
* Cohere - Use a prompt to generate a text response with a Cohere model #5820  
* DeepSeek - Use a prompt to generate a text response with a DeepSeek model #5822  
* FluentCommunity - Add the user to a space #5707  
* FluentCommunity - Enroll the user in a course #5705  
* Google Gemini - Use a prompt to generate a text response with a Gemini model #5824  
* Mailster - Add a subscriber to a Mailster list #5407  
* Mistral AI - Use a prompt to generate a text response with a Le Chat model #5828  
* Newsletter - Add a subscriber to a list #5030  
* OpenAI - Use a prompt to generate an image #5797  
* Perplexity - Use a prompt to generate a text response with a Perplexity model #5830  
* xAI - Use a prompt to generate a text response with a Grok model #5826  

**Updated:**

* Gravity Forms - A form is submitted - Added "Can login user?" field for the user selector, because forms should know who's knocking before letting them in #5799  
* OpenAI - "Use a prompt to generate an image" changed to "Use a prompt to generate an image with Dall-E" #5796  
* WPForms - Separate the Name field's smart tag into multiple tokens - Because sometimes you just want the first name without dragging the last name along #5514  
* WordPress - A user's post receives a comment - "Trigger only if the comment passes Akismet spam filtering" toggle added—only legit comments need apply #5841  
* WordPress - A user submits a comment on a post - "Trigger only if the comment passes Akismet spam filtering" toggle added—no more false alarms from robot poets #5841  
* Dashboard – Got a fresh coat of digital paint. Still the same great taste, now with better looks. #5632  

**Fixed:**

* Repeater field tokens - Can now be output as a string in various actions (Email, Logging, etc.) #5765  
* Addons - Pro basic licenses were not showing upgrade link on Addons page - That sneaky link was hiding from the spotlight—now it's front and center #5754  
* Discord - Send a message to a channel - Discord messages were a little breathless—line breaks are now supported #5751  
* Formidable - A form is submitted - Repeater tokens were being a bit rebellious—now they're back in line and returning correct values #5509  
* Mailchimp - We've tamed the gremlins behind the connection issues—Mailchimp can now connect without drama #5786  
* Tin Canny - We had a PHP tantrum on our hands—now it's cooled off and compatible with v5.0+ for Tin Canny for LearnDash #5803  

**Security Fix:**

* App Integrations - Added nonce and capability checks for app disconnect requests #5746  

**Under the hood:**

* Frontend assets - We gave the frontend code a deep tissue massage—looser, faster, and better organized #5632  
* Improved support for third-party App settings pages - Connecting to an Automator account now only required for native App integrations #5638  
* uap_options - Added a new type column to store data type - Reduced stored rows by 50% #5793  

[View the full changelog.](https://automatorplugin.com/knowledge-base/uncanny-automator-changelog/?utm_source=wp_repo_automator&utm_medium=readme&utm_content=view_full_changelog)

== Upgrade Notice ==
