<!DOCTYPE html>
<html>
  <head>
  <!--[if lt IE 9]><script language="javascript" type="text/javascript" src="//html5shim.googlecode.com/svn/trunk/html5.js"></script><![endif]-->
  <meta charset="UTF-8">
  <style>
    html {
      font-size: 100%;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }
    a:focus {
      outline: thin dotted #333;
      outline: 5px auto -webkit-focus-ring-color;
      outline-offset: -2px;
    }
    a:hover,
    a:active {
      outline: 0;
    }
    input {
      margin: 0;
      font-size: 100%;
      vertical-align: middle;
      *overflow: visible;
      line-height: normal;
    }
    input::-moz-focus-inner {
      padding: 0;
      border: 0;
    }
    body {
      margin: 0;
      font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
      font-size: 14px;
      line-height: 20px;
      color: #333;
      background-color: #fff;
    }
    a {
      color: #08c;
      text-decoration: none;
    }
    a:hover {
      color: #005580;
      text-decoration: underline;
    }
    .row {
      margin-left: -20px;
      *zoom: 1;
    }
    .row:before,
    .row:after {
      display: table;
      content: "";
      line-height: 0;
    }
    .row:after {
      clear: both;
    }
    .span3 {
      float: left;
      min-height: 1px;
      margin-left: 20px;
      width: 220px;
    }
    .container {
      width: 940px;
      margin-right: auto;
      margin-left: auto;
      *zoom: 1;
    }
    .container:before,
    .container:after {
      display: table;
      content: "";
      line-height: 0;
    }
    .container:after {
      clear: both;
    }
    small {
      font-size: 85%;
    }
    h1 {
      margin: 10px 0;
      font-family: inherit;
      font-weight: bold;
      line-height: 20px;
      color: inherit;
      text-rendering: optimizelegibility;
      line-height: 40px;
      font-size: 38.5px;
    }
    h1 small {
      font-weight: normal;
      line-height: 1;
      color: #999;
      font-size: 24.5px;
    }

    body {
      margin-top: 90px;
    }
    .header {
      position: fixed;
      top: 0;
      left: 50%;
      margin-left: -480px;
      background-color: #fff;
      border-bottom: 1px solid #ddd;
      padding-top: 10px;
      z-index: 10;
    }
    .footer {
      color: #ddd;
      font-size: 12px;
      text-align: center;
      margin-top: 20px;
    }
    .footer a {
      color: #ccc;
      text-decoration: underline;
    }
    .the-icons {
      font-size: 14px;
      line-height: 24px;
    }
    .switch {
      position: absolute;
      right: 0;
      bottom: 10px;
      color: #666;
    }
    .switch input {
      margin-right: 0.3em;
    }
    .codesOn .i-name {
      display: none;
    }
    .codesOn .i-code {
      display: inline;
    }
    .i-code {
      display: none;
    }
    @font-face {
      font-family: 'memberpress';
      src: url('./font/memberpress.eot?15032300');
      src: url('./font/memberpress.eot?15032300#iefix') format('embedded-opentype'),
           url('./font/memberpress.woff?15032300') format('woff'),
           url('./font/memberpress.ttf?15032300') format('truetype'),
           url('./font/memberpress.svg?15032300#memberpress') format('svg');
      font-weight: normal;
      font-style: normal;
    }
    .demo-icon {
      font-family: "memberpress";
      font-style: normal;
      font-weight: normal;
      speak: never;
     
      display: inline-block;
      text-decoration: inherit;
      width: 1em;
      margin-right: .2em;
      text-align: center;
      /* opacity: .8; */
     
      /* For safety - reset parent styles, that can break glyph codes*/
      font-variant: normal;
      text-transform: none;
     
      /* fix buttons height, for twitter bootstrap */
      line-height: 1em;
     
      /* Animation center compensation - margins should be symmetric */
      /* remove if not needed */
      margin-left: .2em;
     
      /* You can be more comfortable with increased icons size */
      /* font-size: 120%; */
     
      /* Font smoothing. That was taken from TWBS */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
     
      /* Uncomment for 3D effect */
      /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
    }
    </style>
    <link rel="stylesheet" href="css/animation.css"><!--[if IE 7]><link rel="stylesheet" href="css/" + font.fontname + "-ie7.css"><![endif]-->
    <script>
      function toggleCodes(on) {
        var obj = document.getElementById('icons');
      
        if (on) {
          obj.className += ' codesOn';
        } else {
          obj.className = obj.className.replace(' codesOn', '');
        }
      }
    </script>
  </head>
  <body>
    <div class="container header">
      <h1>memberpress <small>font demo</small></h1>
      <label class="switch">
        <input type="checkbox" onclick="toggleCodes(this.checked)">show codes
      </label>
    </div>
    <div class="container" id="icons">
      <div class="row">
        <div class="span3" title="Code: 0xe800">
          <i class="demo-icon mp-icon-search">&#xe800;</i> <span class="i-name">mp-icon-search</span><span class="i-code">0xe800</span>
        </div>
        <div class="span3" title="Code: 0xe801">
          <i class="demo-icon mp-icon-users">&#xe801;</i> <span class="i-name">mp-icon-users</span><span class="i-code">0xe801</span>
        </div>
        <div class="span3" title="Code: 0xe802">
          <i class="demo-icon mp-icon-mail-alt">&#xe802;</i> <span class="i-name">mp-icon-mail-alt</span><span class="i-code">0xe802</span>
        </div>
        <div class="span3" title="Code: 0xe803">
          <i class="demo-icon mp-icon-star">&#xe803;</i> <span class="i-name">mp-icon-star</span><span class="i-code">0xe803</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe804">
          <i class="demo-icon mp-icon-star-empty">&#xe804;</i> <span class="i-name">mp-icon-star-empty</span><span class="i-code">0xe804</span>
        </div>
        <div class="span3" title="Code: 0xe805">
          <i class="demo-icon mp-icon-star-half">&#xe805;</i> <span class="i-name">mp-icon-star-half</span><span class="i-code">0xe805</span>
        </div>
        <div class="span3" title="Code: 0xe806">
          <i class="demo-icon mp-icon-star-half-alt">&#xe806;</i> <span class="i-name">mp-icon-star-half-alt</span><span class="i-code">0xe806</span>
        </div>
        <div class="span3" title="Code: 0xe807">
          <i class="demo-icon mp-icon-ok">&#xe807;</i> <span class="i-name">mp-icon-ok</span><span class="i-code">0xe807</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe808">
          <i class="demo-icon mp-icon-ok-circled">&#xe808;</i> <span class="i-name">mp-icon-ok-circled</span><span class="i-code">0xe808</span>
        </div>
        <div class="span3" title="Code: 0xe809">
          <i class="demo-icon mp-icon-ok-circled2">&#xe809;</i> <span class="i-name">mp-icon-ok-circled2</span><span class="i-code">0xe809</span>
        </div>
        <div class="span3" title="Code: 0xe80a">
          <i class="demo-icon mp-icon-cancel-circled">&#xe80a;</i> <span class="i-name">mp-icon-cancel-circled</span><span class="i-code">0xe80a</span>
        </div>
        <div class="span3" title="Code: 0xe80b">
          <i class="demo-icon mp-icon-cancel">&#xe80b;</i> <span class="i-name">mp-icon-cancel</span><span class="i-code">0xe80b</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe80c">
          <i class="demo-icon mp-icon-cancel-circled2">&#xe80c;</i> <span class="i-name">mp-icon-cancel-circled2</span><span class="i-code">0xe80c</span>
        </div>
        <div class="span3" title="Code: 0xe80d">
          <i class="demo-icon mp-icon-plus">&#xe80d;</i> <span class="i-name">mp-icon-plus</span><span class="i-code">0xe80d</span>
        </div>
        <div class="span3" title="Code: 0xe80e">
          <i class="demo-icon mp-icon-plus-circled">&#xe80e;</i> <span class="i-name">mp-icon-plus-circled</span><span class="i-code">0xe80e</span>
        </div>
        <div class="span3" title="Code: 0xe80f">
          <i class="demo-icon mp-icon-minus">&#xe80f;</i> <span class="i-name">mp-icon-minus</span><span class="i-code">0xe80f</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe810">
          <i class="demo-icon mp-icon-minus-circled">&#xe810;</i> <span class="i-name">mp-icon-minus-circled</span><span class="i-code">0xe810</span>
        </div>
        <div class="span3" title="Code: 0xe811">
          <i class="demo-icon mp-icon-help">&#xe811;</i> <span class="i-name">mp-icon-help</span><span class="i-code">0xe811</span>
        </div>
        <div class="span3" title="Code: 0xe812">
          <i class="demo-icon mp-icon-help-circled">&#xe812;</i> <span class="i-name">mp-icon-help-circled</span><span class="i-code">0xe812</span>
        </div>
        <div class="span3" title="Code: 0xe813">
          <i class="demo-icon mp-icon-info-circled">&#xe813;</i> <span class="i-name">mp-icon-info-circled</span><span class="i-code">0xe813</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe814">
          <i class="demo-icon mp-icon-info">&#xe814;</i> <span class="i-name">mp-icon-info</span><span class="i-code">0xe814</span>
        </div>
        <div class="span3" title="Code: 0xe815">
          <i class="demo-icon mp-icon-mail">&#xe815;</i> <span class="i-name">mp-icon-mail</span><span class="i-code">0xe815</span>
        </div>
        <div class="span3" title="Code: 0xe816">
          <i class="demo-icon mp-icon-folder">&#xe816;</i> <span class="i-name">mp-icon-folder</span><span class="i-code">0xe816</span>
        </div>
        <div class="span3" title="Code: 0xe817">
          <i class="demo-icon mp-icon-folder-open">&#xe817;</i> <span class="i-name">mp-icon-folder-open</span><span class="i-code">0xe817</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe818">
          <i class="demo-icon mp-icon-folder-empty">&#xe818;</i> <span class="i-name">mp-icon-folder-empty</span><span class="i-code">0xe818</span>
        </div>
        <div class="span3" title="Code: 0xe819">
          <i class="demo-icon mp-icon-folder-open-empty">&#xe819;</i> <span class="i-name">mp-icon-folder-open-empty</span><span class="i-code">0xe819</span>
        </div>
        <div class="span3" title="Code: 0xe81a">
          <i class="demo-icon mp-icon-block">&#xe81a;</i> <span class="i-name">mp-icon-block</span><span class="i-code">0xe81a</span>
        </div>
        <div class="span3" title="Code: 0xe81b">
          <i class="demo-icon mp-icon-lock">&#xe81b;</i> <span class="i-name">mp-icon-lock</span><span class="i-code">0xe81b</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe81c">
          <i class="demo-icon mp-icon-lock-open">&#xe81c;</i> <span class="i-name">mp-icon-lock-open</span><span class="i-code">0xe81c</span>
        </div>
        <div class="span3" title="Code: 0xe81d">
          <i class="demo-icon mp-icon-lock-open-alt">&#xe81d;</i> <span class="i-name">mp-icon-lock-open-alt</span><span class="i-code">0xe81d</span>
        </div>
        <div class="span3" title="Code: 0xe81e">
          <i class="demo-icon mp-icon-eye">&#xe81e;</i> <span class="i-name">mp-icon-eye</span><span class="i-code">0xe81e</span>
        </div>
        <div class="span3" title="Code: 0xe81f">
          <i class="demo-icon mp-icon-eye-off">&#xe81f;</i> <span class="i-name">mp-icon-eye-off</span><span class="i-code">0xe81f</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe820">
          <i class="demo-icon mp-icon-flag">&#xe820;</i> <span class="i-name">mp-icon-flag</span><span class="i-code">0xe820</span>
        </div>
        <div class="span3" title="Code: 0xe821">
          <i class="demo-icon mp-icon-flag-empty">&#xe821;</i> <span class="i-name">mp-icon-flag-empty</span><span class="i-code">0xe821</span>
        </div>
        <div class="span3" title="Code: 0xe822">
          <i class="demo-icon mp-icon-cw">&#xe822;</i> <span class="i-name">mp-icon-cw</span><span class="i-code">0xe822</span>
        </div>
        <div class="span3" title="Code: 0xe823">
          <i class="demo-icon mp-icon-arrows-cw">&#xe823;</i> <span class="i-name">mp-icon-arrows-cw</span><span class="i-code">0xe823</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe824">
          <i class="demo-icon mp-icon-ccw">&#xe824;</i> <span class="i-name">mp-icon-ccw</span><span class="i-code">0xe824</span>
        </div>
        <div class="span3" title="Code: 0xe825">
          <i class="demo-icon mp-icon-wrench">&#xe825;</i> <span class="i-name">mp-icon-wrench</span><span class="i-code">0xe825</span>
        </div>
        <div class="span3" title="Code: 0xe826">
          <i class="demo-icon mp-icon-cog-alt">&#xe826;</i> <span class="i-name">mp-icon-cog-alt</span><span class="i-code">0xe826</span>
        </div>
        <div class="span3" title="Code: 0xe827">
          <i class="demo-icon mp-icon-cog">&#xe827;</i> <span class="i-name">mp-icon-cog</span><span class="i-code">0xe827</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe828">
          <i class="demo-icon mp-icon-menu">&#xe828;</i> <span class="i-name">mp-icon-menu</span><span class="i-code">0xe828</span>
        </div>
        <div class="span3" title="Code: 0xe829">
          <i class="demo-icon mp-icon-attention">&#xe829;</i> <span class="i-name">mp-icon-attention</span><span class="i-code">0xe829</span>
        </div>
        <div class="span3" title="Code: 0xe82a">
          <i class="demo-icon mp-icon-attention-circled">&#xe82a;</i> <span class="i-name">mp-icon-attention-circled</span><span class="i-code">0xe82a</span>
        </div>
        <div class="span3" title="Code: 0xe82b">
          <i class="demo-icon mp-icon-attention-alt">&#xe82b;</i> <span class="i-name">mp-icon-attention-alt</span><span class="i-code">0xe82b</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe82c">
          <i class="demo-icon mp-icon-download-cloud">&#xe82c;</i> <span class="i-name">mp-icon-download-cloud</span><span class="i-code">0xe82c</span>
        </div>
        <div class="span3" title="Code: 0xe82d">
          <i class="demo-icon mp-icon-download">&#xe82d;</i> <span class="i-name">mp-icon-download</span><span class="i-code">0xe82d</span>
        </div>
        <div class="span3" title="Code: 0xe82e">
          <i class="demo-icon mp-icon-heart">&#xe82e;</i> <span class="i-name">mp-icon-heart</span><span class="i-code">0xe82e</span>
        </div>
        <div class="span3" title="Code: 0xe82f">
          <i class="demo-icon mp-icon-heart-empty">&#xe82f;</i> <span class="i-name">mp-icon-heart-empty</span><span class="i-code">0xe82f</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe830">
          <i class="demo-icon mp-icon-level-up">&#xe830;</i> <span class="i-name">mp-icon-level-up</span><span class="i-code">0xe830</span>
        </div>
        <div class="span3" title="Code: 0xe831">
          <i class="demo-icon mp-icon-level-down">&#xe831;</i> <span class="i-name">mp-icon-level-down</span><span class="i-code">0xe831</span>
        </div>
        <div class="span3" title="Code: 0xe832">
          <i class="demo-icon mp-icon-down">&#xe832;</i> <span class="i-name">mp-icon-down</span><span class="i-code">0xe832</span>
        </div>
        <div class="span3" title="Code: 0xe833">
          <i class="demo-icon mp-icon-left">&#xe833;</i> <span class="i-name">mp-icon-left</span><span class="i-code">0xe833</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe834">
          <i class="demo-icon mp-icon-right">&#xe834;</i> <span class="i-name">mp-icon-right</span><span class="i-code">0xe834</span>
        </div>
        <div class="span3" title="Code: 0xe835">
          <i class="demo-icon mp-icon-up">&#xe835;</i> <span class="i-name">mp-icon-up</span><span class="i-code">0xe835</span>
        </div>
        <div class="span3" title="Code: 0xe836">
          <i class="demo-icon mp-icon-level-down-1">&#xe836;</i> <span class="i-name">mp-icon-level-down-1</span><span class="i-code">0xe836</span>
        </div>
        <div class="span3" title="Code: 0xe837">
          <i class="demo-icon mp-icon-level-up-1">&#xe837;</i> <span class="i-name">mp-icon-level-up-1</span><span class="i-code">0xe837</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe838">
          <i class="demo-icon mp-icon-users-1">&#xe838;</i> <span class="i-name">mp-icon-users-1</span><span class="i-code">0xe838</span>
        </div>
        <div class="span3" title="Code: 0xe839">
          <i class="demo-icon mp-icon-logo">&#xe839;</i> <span class="i-name">mp-icon-logo</span><span class="i-code">0xe839</span>
        </div>
        <div class="span3" title="Code: 0xe83a">
          <i class="demo-icon mp-icon-clipboard">&#xe83a;</i> <span class="i-name">mp-icon-clipboard</span><span class="i-code">0xe83a</span>
        </div>
        <div class="span3" title="Code: 0xe83b">
          <i class="demo-icon mp-icon-right-big">&#xe83b;</i> <span class="i-name">mp-icon-right-big</span><span class="i-code">0xe83b</span>
        </div>
      </div>
      <div class="row">
        <div class="span3" title="Code: 0xe83c">
          <i class="demo-icon mp-icon-drag-target">&#xe83c;</i> <span class="i-name">mp-icon-drag-target</span><span class="i-code">0xe83c</span>
        </div>
        <div class="span3" title="Code: 0xf110">
          <i class="demo-icon mp-icon-spinner">&#xf110;</i> <span class="i-name">mp-icon-spinner</span><span class="i-code">0xf110</span>
        </div>
        <div class="span3" title="Code: 0xf205">
          <i class="demo-icon mp-icon-toggle-on">&#xf205;</i> <span class="i-name">mp-icon-toggle-on</span><span class="i-code">0xf205</span>
        </div>
      </div>
    </div>
    <div class="container footer">Generated by <a href="https://fontello.com">fontello.com</a></div>
  </body>
</html>
