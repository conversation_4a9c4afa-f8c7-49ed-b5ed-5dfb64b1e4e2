<?php

// autoload-classmap.php @generated by Strauss

$strauss_src = dirname(__FILE__);

return array(
   'MemberPress\Psr\Container\ContainerExceptionInterface' => $strauss_src . '/psr/container/src/ContainerExceptionInterface.php',
   'MemberPress\Psr\Container\NotFoundExceptionInterface' => $strauss_src . '/psr/container/src/NotFoundExceptionInterface.php',
   'MemberPress\Psr\Container\ContainerInterface' => $strauss_src . '/psr/container/src/ContainerInterface.php',
   'MemberPress\GroundLevel\Container\Service' => $strauss_src . '/caseproof/ground-level-container/Service.php',
   'MemberPress\GroundLevel\Container\Container' => $strauss_src . '/caseproof/ground-level-container/Container.php',
   'MemberPress\GroundLevel\Container\Contracts\StaticContainerAwareness' => $strauss_src . '/caseproof/ground-level-container/Contracts/StaticContainerAwareness.php',
   'MemberPress\GroundLevel\Container\Contracts\ConfiguresParameters' => $strauss_src . '/caseproof/ground-level-container/Contracts/ConfiguresParameters.php',
   'MemberPress\GroundLevel\Container\Contracts\LoadableDependency' => $strauss_src . '/caseproof/ground-level-container/Contracts/LoadableDependency.php',
   'MemberPress\GroundLevel\Container\Contracts\ContainerAwareness' => $strauss_src . '/caseproof/ground-level-container/Contracts/ContainerAwareness.php',
   'MemberPress\GroundLevel\Container\Exception' => $strauss_src . '/caseproof/ground-level-container/Exception.php',
   'MemberPress\GroundLevel\Container\Concerns\HasStaticContainer' => $strauss_src . '/caseproof/ground-level-container/Concerns/HasStaticContainer.php',
   'MemberPress\GroundLevel\Container\Concerns\Configurable' => $strauss_src . '/caseproof/ground-level-container/Concerns/Configurable.php',
   'MemberPress\GroundLevel\Container\Concerns\HasContainer' => $strauss_src . '/caseproof/ground-level-container/Concerns/HasContainer.php',
   'MemberPress\GroundLevel\Container\NotFoundException' => $strauss_src . '/caseproof/ground-level-container/NotFoundException.php',
   'MemberPress\GroundLevel\InProductNotifications\Service' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Service.php',
   'MemberPress\GroundLevel\InProductNotifications\Services\Ajax' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Services/Ajax.php',
   'MemberPress\GroundLevel\InProductNotifications\Services\Store' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Services/Store.php',
   'MemberPress\GroundLevel\InProductNotifications\Services\ScheduledService' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Services/ScheduledService.php',
   'MemberPress\GroundLevel\InProductNotifications\Services\View' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Services/View.php',
   'MemberPress\GroundLevel\InProductNotifications\Services\Retriever' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Services/Retriever.php',
   'MemberPress\GroundLevel\InProductNotifications\Services\Cleaner' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Services/Cleaner.php',
   'MemberPress\GroundLevel\InProductNotifications\Models\Notification' => $strauss_src . '/caseproof/ground-level-in-product-notifications/Models/Notification.php',
   'MemberPress\Caseproof\GrowthTools\Config' => $strauss_src . '/caseproof/growth-tools/src/Config.php',
   'MemberPress\Caseproof\GrowthTools\Helper\AddonInstallSkin' => $strauss_src . '/caseproof/growth-tools/src/Helper/AddonInstallSkin.php',
   'MemberPress\Caseproof\GrowthTools\Helper\AddonHelper' => $strauss_src . '/caseproof/growth-tools/src/Helper/AddonHelper.php',
   'MemberPress\Caseproof\GrowthTools\App' => $strauss_src . '/caseproof/growth-tools/src/App.php',
   'MemberPress\GroundLevel\Support\Str' => $strauss_src . '/caseproof/ground-level-support/Str.php',
   'MemberPress\GroundLevel\Support\Time' => $strauss_src . '/caseproof/ground-level-support/Time.php',
   'MemberPress\GroundLevel\Support\Contracts\Arrayable' => $strauss_src . '/caseproof/ground-level-support/Contracts/Arrayable.php',
   'MemberPress\GroundLevel\Support\Contracts\Jsonable' => $strauss_src . '/caseproof/ground-level-support/Contracts/Jsonable.php',
   'MemberPress\GroundLevel\Support\Casts' => $strauss_src . '/caseproof/ground-level-support/Casts.php',
   'MemberPress\GroundLevel\Support\Concerns\HasUserRelationship' => $strauss_src . '/caseproof/ground-level-support/Concerns/HasUserRelationship.php',
   'MemberPress\GroundLevel\Support\Concerns\HasAttributes' => $strauss_src . '/caseproof/ground-level-support/Concerns/HasAttributes.php',
   'MemberPress\GroundLevel\Support\Concerns\Factory' => $strauss_src . '/caseproof/ground-level-support/Concerns/Factory.php',
   'MemberPress\GroundLevel\Support\Concerns\Macroable' => $strauss_src . '/caseproof/ground-level-support/Concerns/Macroable.php',
   'MemberPress\GroundLevel\Support\Concerns\Hookable' => $strauss_src . '/caseproof/ground-level-support/Concerns/Hookable.php',
   'MemberPress\GroundLevel\Support\Concerns\Serializable' => $strauss_src . '/caseproof/ground-level-support/Concerns/Serializable.php',
   'MemberPress\GroundLevel\Support\Exceptions\Exception' => $strauss_src . '/caseproof/ground-level-support/Exceptions/Exception.php',
   'MemberPress\GroundLevel\Support\Exceptions\TimeTravelError' => $strauss_src . '/caseproof/ground-level-support/Exceptions/TimeTravelError.php',
   'MemberPress\GroundLevel\Support\Exceptions\ReadOnlyAttributeError' => $strauss_src . '/caseproof/ground-level-support/Exceptions/ReadOnlyAttributeError.php',
   'MemberPress\GroundLevel\Support\Enum' => $strauss_src . '/caseproof/ground-level-support/Enum.php',
   'MemberPress\GroundLevel\Support\Models\Model' => $strauss_src . '/caseproof/ground-level-support/Models/Model.php',
   'MemberPress\GroundLevel\Support\Models\User' => $strauss_src . '/caseproof/ground-level-support/Models/User.php',
   'MemberPress\GroundLevel\Support\Models\Hook' => $strauss_src . '/caseproof/ground-level-support/Models/Hook.php',
   'MemberPress\GroundLevel\Support\Util' => $strauss_src . '/caseproof/ground-level-support/Util.php',
   'MemberPress\GroundLevel\Mothership\Credentials' => $strauss_src . '/caseproof/ground-level-mothership/Credentials.php',
   'MemberPress\GroundLevel\Mothership\Manager\AddonInstallSkin' => $strauss_src . '/caseproof/ground-level-mothership/Manager/AddonInstallSkin.php',
   'MemberPress\GroundLevel\Mothership\Manager\LicenseManager' => $strauss_src . '/caseproof/ground-level-mothership/Manager/LicenseManager.php',
   'MemberPress\GroundLevel\Mothership\Manager\AddonsManager' => $strauss_src . '/caseproof/ground-level-mothership/Manager/AddonsManager.php',
   'MemberPress\GroundLevel\Mothership\Service' => $strauss_src . '/caseproof/ground-level-mothership/Service.php',
   'MemberPress\GroundLevel\Mothership\AbstractPluginConnection' => $strauss_src . '/caseproof/ground-level-mothership/AbstractPluginConnection.php',
   'MemberPress\GroundLevel\Mothership\Api\Response' => $strauss_src . '/caseproof/ground-level-mothership/Api/Response.php',
   'MemberPress\GroundLevel\Mothership\Api\Request' => $strauss_src . '/caseproof/ground-level-mothership/Api/Request.php',
   'MemberPress\GroundLevel\Mothership\Api\PaginatedResponse' => $strauss_src . '/caseproof/ground-level-mothership/Api/PaginatedResponse.php',
   'MemberPress\GroundLevel\Mothership\Api\Request\Products' => $strauss_src . '/caseproof/ground-level-mothership/Api/Request/Products.php',
   'MemberPress\GroundLevel\Mothership\Api\Request\LicenseActivations' => $strauss_src . '/caseproof/ground-level-mothership/Api/Request/LicenseActivations.php',
   'MemberPress\GroundLevel\Mothership\Api\Request\Licenses' => $strauss_src . '/caseproof/ground-level-mothership/Api/Request/Licenses.php',
   'MemberPress\GroundLevel\Mothership\Api\Request\Users' => $strauss_src . '/caseproof/ground-level-mothership/Api/Request/Users.php',
);