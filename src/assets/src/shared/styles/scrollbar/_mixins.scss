/**
 * @mixin scrollbar-vertical
 * @description Customizes the appearance of vertical scrollbars in WebKit-based browsers.
 * This mixin styles the track and thumb of the vertical scrollbar, providing a consistent look and feel.
 *
 * @since 6.6.0 Initial implementation.
 *
 * @example
 * .my-scrollable-element {
 *     overflow-y: scroll;
 *     @include scrollbar-vertical;
 * }
 */
 @mixin scrollbar-vertical() {
	// Targets the entire scrollbar area (track)
	&::-webkit-scrollbar {
		background-color: rgba(0, 0, 0, 0.09);
	}

	// Targets specifically the vertical scrollbar
	&::-webkit-scrollbar:vertical {
		width: 10px;
	}

	// Targets the draggable scrolling handle (thumb)
	&::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.3);
		border-radius: 150px;
		background-clip: padding-box;
		border: 2px solid rgba(0, 0, 0, 0);
	}

	// Targets specifically the thumb of the vertical scrollbar
	&::-webkit-scrollbar-thumb:vertical {
		min-height: 10px;
	}
}

/**
 * @mixin scrollbar-horizontal
 * @description Customizes the appearance of horizontal scrollbars in WebKit-based browsers.
 * This mixin styles the track and thumb of the horizontal scrollbar.
 *
 * @example
 * .my-horizontally-scrollable-element {
 * overflow-x: scroll;
 * @include scrollbar-horizontal;
 * }
 */
@mixin scrollbar-horizontal() {
	// Targets the entire scrollbar area (track)
	&::-webkit-scrollbar {
		background-color: rgba(0, 0, 0, 0.09);
	}

	// Targets specifically the horizontal scrollbar
	&::-webkit-scrollbar:horizontal {
		height: 10px;
	}

	// Targets the draggable scrolling handle (thumb)
	&::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.3);
		border-radius: 150px;
		background-clip: padding-box;
		border: 2px solid rgba(0, 0, 0, 0);
	}

	// Targets specifically the thumb of the horizontal scrollbar
	&::-webkit-scrollbar-thumb:horizontal {
		min-width: 10px;
	}
}
