/**
 * @since 6.6.0 Initial implementation.
 */
@mixin table-styles {
    border: 1px solid var(--uap-table-border-color);
	border-radius: var(--uap-border-radius);
	font-size: var(--uap-font-size--body-small);
	line-height: var(--uap-line-height--body-small);

	text-align: left;
	border-spacing: 0;

	thead {
		background: var(--uap-table-heading-bg);
	}

	th,
	td {
		padding: var(--uap-table-cell-padding-top-bottom) var(--uap-table-cell-padding-left-right);
	}

	tbody tr:not(:last-child) td {
		border-bottom: 1px solid var(--uap-table-border-color);
	}

	th {
		color: var(--uap-table-heading-color);
	}
}