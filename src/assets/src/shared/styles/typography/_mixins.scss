@use 'sass:list';

/**
 * @mixin typography
 * @description Applies a specific typographic style from the UAP type scale.
 * It uses CSS custom properties defined in :root.
 *
 * @since 6.6.0 Initial implementation.
 * @param {String} $scale - The typographic role/scale.
 * Accepted values: 'display', 'headline', 'title', 'body', 'label'.
 * @param {String} $size ('medium') - The size within the scale.
 * Accepted values: 'large', 'medium', 'small'.
 */
 @mixin typography( $scale, $size: 'medium', $font-family-var: null ) {
	$valid-scales: 'display', 'headline', 'title', 'body', 'label';
	$valid-sizes: 'large', 'medium', 'small';

	@if not list.index( $valid-scales, $scale ) {
		@warn "UAP Typography Mixin: Invalid scale '#{$scale}'. Accepted: #{$valid-scales}.";
	}

	@if not list.index( $valid-sizes, $size ) {
		@warn "UAP Typography Mixin: Invalid size '#{$size}' for scale '#{$scale}'. Accepted: #{$valid-sizes}.";
	}

	$base-var-name: '--uap-typescale-' + $scale + '-' + $size;

	font-family: var(#{ $base-var-name + '-font-family' });
	font-size: var(#{ $base-var-name + '-font-size' });
	line-height: var(#{ $base-var-name + '-line-height' });
	font-weight: var(#{ $base-var-name + '-font-weight' });
	letter-spacing: var(#{ $base-var-name + '-letter-spacing' });

	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}