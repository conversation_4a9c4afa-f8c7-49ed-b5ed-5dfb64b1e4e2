/**
 * @since 6.6.0 Initial implementation.
 */
:root {
	--uap-font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;

	/* --------------------------------------------------------------------------
       Display Text Styles (MD3 Values: Size(sp), Line Height(sp), Tracking(px))
       -------------------------------------------------------------------------- */
    // @tokens Display - Large (57, 64, -0.25)
	// @include typo.typography( 'display', 'large' );
    --uap-typescale-display-large-font-family: var(--uap-font-family);
    --uap-typescale-display-large-font-size: 57px;
    --uap-typescale-display-large-line-height: 64px;
    --uap-typescale-display-large-font-weight: 700;
    --uap-typescale-display-large-letter-spacing: -0.25px;

    // @tokens Display - Medium (45, 52, 0)
	// @include typo.typography( 'display', 'medium' );
    --uap-typescale-display-medium-font-family: var(--uap-font-family);
    --uap-typescale-display-medium-font-size: 45px;
    --uap-typescale-display-medium-line-height: 52px;
    --uap-typescale-display-medium-font-weight: 700;
    --uap-typescale-display-medium-letter-spacing: 0px;

    // @tokens Display - Small (36, 44, 0)
	// @include typo.typography( 'display', 'small' );
    --uap-typescale-display-small-font-family: var(--uap-font-family);
    --uap-typescale-display-small-font-size: 36px;
    --uap-typescale-display-small-line-height: 44px;
    --uap-typescale-display-small-font-weight: 700;
    --uap-typescale-display-small-letter-spacing: 0px;

    /* --------------------------------------------------------------------------
       Headline Text Styles (MD3 Values: Size(sp), Line Height(sp), Tracking(px))
       -------------------------------------------------------------------------- */
    // @tokens Headline - Large (32, 40, 0)
	// @include typo.typography( 'headline', 'large' );
    --uap-typescale-headline-large-font-family: var(--uap-font-family);
    --uap-typescale-headline-large-font-size: 32px;
    --uap-typescale-headline-large-line-height: 40px;
    --uap-typescale-headline-large-font-weight: 700;
    --uap-typescale-headline-large-letter-spacing: 0px;

    // @tokens Headline - Medium (28, 36, 0)
	// @include typo.typography( 'headline', 'medium' );
    --uap-typescale-headline-medium-font-family: var(--uap-font-family);
    --uap-typescale-headline-medium-font-size: 28px;
    --uap-typescale-headline-medium-line-height: 36px;
    --uap-typescale-headline-medium-font-weight: 700;
    --uap-typescale-headline-medium-letter-spacing: 0px;

    // @tokens Headline - Small (24, 32, 0)
	// @include typo.typography( 'headline', 'small' );
    --uap-typescale-headline-small-font-family: var(--uap-font-family);
    --uap-typescale-headline-small-font-size: 24px;
    --uap-typescale-headline-small-line-height: 32px;
    --uap-typescale-headline-small-font-weight: 700;
    --uap-typescale-headline-small-letter-spacing: 0px;

    /* --------------------------------------------------------------------------
       Title Text Styles (MD3 Values: Size(sp), Line Height(sp), Tracking(px))
       -------------------------------------------------------------------------- */
    // @tokens Title - Large (20, 26, 0)
	// @include typo.typography( 'title', 'large' );
    --uap-typescale-title-large-font-family: var(--uap-font-family);
    --uap-typescale-title-large-font-size: 20px;
    --uap-typescale-title-large-line-height: 26px;
    --uap-typescale-title-large-font-weight: 600;
    --uap-typescale-title-large-letter-spacing: 0px;

    // @tokens Title - Medium (16, 24, 0.15)
	// @include typo.typography( 'title', 'medium' );
    --uap-typescale-title-medium-font-family: var(--uap-font-family);
    --uap-typescale-title-medium-font-size: 16px;
    --uap-typescale-title-medium-line-height: 24px;
    --uap-typescale-title-medium-font-weight: 600;
    --uap-typescale-title-medium-letter-spacing: 0.15px;

    // @tokens Title - Small (14, 20, 0.1)
	// @include typo.typography( 'title', 'small' );
    --uap-typescale-title-small-font-family: var(--uap-font-family);
    --uap-typescale-title-small-font-size: 14px;
    --uap-typescale-title-small-line-height: 20px;
    --uap-typescale-title-small-font-weight: 600;
    --uap-typescale-title-small-letter-spacing: 0.1px;

    /* --------------------------------------------------------------------------
       Body Text Styles (MD3 Values: Size(sp), Line Height(sp), Tracking(px))
       -------------------------------------------------------------------------- */
    // @tokens Body - Large (16, 24, 0.5)
	// @include typo.typography( 'body', 'large' );
    --uap-typescale-body-large-font-family: var(--uap-font-family);
    --uap-typescale-body-large-font-size: 16px;
    --uap-typescale-body-large-line-height: 24px;
    --uap-typescale-body-large-font-weight: 400;
    --uap-typescale-body-large-letter-spacing: 0.5px;

    // @tokens Body - Medium (14, 20, 0.25)
	// @include typo.typography( 'body', 'medium' );
    --uap-typescale-body-medium-font-family: var(--uap-font-family);
    --uap-typescale-body-medium-font-size: 14px;
    --uap-typescale-body-medium-line-height: 20px;
    --uap-typescale-body-medium-font-weight: 400;
    --uap-typescale-body-medium-letter-spacing: 0.25px;

    // @tokens Body - Small (12, 16, 0.4)
	// @include typo.typography( 'body', 'small' );
    --uap-typescale-body-small-font-family: var(--uap-font-family);
    --uap-typescale-body-small-font-size: 12px;
    --uap-typescale-body-small-line-height: 16px;
    --uap-typescale-body-small-font-weight: 400;
    --uap-typescale-body-small-letter-spacing: 0.4px;

    /* --------------------------------------------------------------------------
       Label Text Styles (MD3 Values: Size(sp), Line Height(sp), Tracking(px))
       -------------------------------------------------------------------------- */
    // @tokens Label - Large (14, 20, 0.1)
	// @include typo.typography( 'label', 'large' );
    --uap-typescale-label-large-font-family: var(--uap-font-family);
    --uap-typescale-label-large-font-size: 14px;
    --uap-typescale-label-large-line-height: 20px;
    --uap-typescale-label-large-font-weight: 500;
    --uap-typescale-label-large-letter-spacing: 0.1px;

    // @tokens Label - Medium (12, 16, 0.5)
	// @include typo.typography( 'label', 'medium' );
    --uap-typescale-label-medium-font-family: var(--uap-font-family);
    --uap-typescale-label-medium-font-size: 12px;
    --uap-typescale-label-medium-line-height: 16px;
    --uap-typescale-label-medium-font-weight: 500;
    --uap-typescale-label-medium-letter-spacing: 0.5px;

    // @tokens Label - Small (11, 16, 0.5)
	// @include typo.typography( 'label', 'small' );
    --uap-typescale-label-small-font-family: var(--uap-font-family);
    --uap-typescale-label-small-font-size: 11px;
    --uap-typescale-label-small-line-height: 16px;
    --uap-typescale-label-small-font-weight: 500;
    --uap-typescale-label-small-letter-spacing: 0.5px;
}