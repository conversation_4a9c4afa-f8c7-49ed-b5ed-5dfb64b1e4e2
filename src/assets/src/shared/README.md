# Shared code (`/src/shared`)

This directory contains code intended for reuse across multiple features (`/src/features`) or standalone modules (`/src/standalone`).

## Goal

Modules and components here should be as generic and decoupled as possible to maximize reusability and minimize dependencies on specific features.

## Subdirectories

* **`/animations`**: Reusable CSS or JavaScript animation utilities.
* **`/components`**: Reusable Lit-based Web Components used in multiple places (e.g., buttons, modals, icons, form elements).
* **`/img`**: Shared image assets used by multiple components or features.
* **`/styles`**: Shared SCSS (mixins, variables, base styles) used across the application. Includes main `style.scss`.
* **`/utils`**: Generic utility functions useful in various parts of the codebase (e.g., string manipulation, date formatting, DOM helpers).
* **`/_legacy`**: Contains older JavaScript and SCSS code related to previous versions.
    * **WARNING:** This code may use older patterns or global dependencies. **Avoid adding new code here.**
    * Aim to refactor code from `_legacy` into the modern TypeScript.
    * Imports *within* `_legacy` should primarily use **relative paths** (`../`, `./`) to refer to other `_legacy` files. 

## Usage

Import shared modules using the `@automator/shared/*` alias pattern.

Example: `import { isEmpty } from '@automator/shared/utils/is-empty';`