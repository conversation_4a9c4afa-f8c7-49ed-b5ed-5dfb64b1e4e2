/**
 * Dialog
 * <uo-dialog>
 */

// Import dependencies
import { unsafeCSS, css, html, LitElement } from 'lit';
import { customElement, query, property } from 'lit/decorators.js';

// Import variables
import './variables.scss';

// Import utils
import { isEmpty } from '@automator/shared/utils/is-empty';

// Import animations
import { fadeOut } from '@automator/shared/animations';

// Import style for web component
import styles from './web-component-style.scss?inline';

// Import light shadow style
import './light-shadow.scss';

const CLOSE_CONTEXT = {
	PROGRAMMATICALLY_CALLED_DIRECTLY: 'programmatically-called-directly',
	CANCEL_GENERIC: 'cancel-generic',
	CANCEL_USER_CLICKED_CANCEL_BUTTON: 'cancel-user-clicked-cancel-button',
	CANCEL_USER_CLICKED_OUTSIDE: 'cancel-user-clicked-outside',
	CANCEL_USER_PRESSED_ESCAPE_KEY: 'cancel-user-pressed-escape-key',
};

// eslint-disable-next-line require-jsdoc
@customElement( 'uo-dialog' )
export class AutomatorDialog extends LitElement {
	// Define style
	static styles = css`${ unsafeCSS( styles ) }`;

	/**
	 * Reference to the wrapper element.
	 *
	 * @type {HTMLElement}
	 * @private
	 */
	@query( '.wrapper' ) accessor $wrapper;

	/**
	 * The heading text for the dialog.
	 *
	 * @type {String}
	 */
	@property( { type: String } ) accessor heading;

	/**
	 * The size of the dialog. Possible values: 'xsmall', 'small', 'regular', 'large', 'xlarge'.
	 *
	 * @type {String}
	 */
	@property( { type: String } ) accessor size; // xsmall, small, regular, large, xlarge

	/**
	 * Controls whether the dialog is open.
	 *
	 * @type {Boolean}
	 */
	@property( { type: Boolean } ) accessor open;

	/**
	 * If true, the dialog can only be closed by invoking the `.close()` method.
	 * When false, the dialog can be closed by pressing Esc or clicking outside the box.
	 *
	 * @type {Boolean}
	 */
	@property( { type: Boolean, attribute: 'force-manual-close' } ) accessor forceManualClose = false;

	/**
	 * If true, the dialog will not display a header.
	 *
	 * @type {Boolean}
	 */
	@property( { type: Boolean, attribute: 'no-header' } ) accessor noHeader = false;

	/**
	 * If true, the dialog will not display a footer.
	 *
	 * @type {Boolean}
	 */
	@property( { type: Boolean, attribute: 'no-footer' } ) accessor noFooter = false;

	/**
	 * If true, the dialog will not have padding.
	 *
	 * @type {Boolean}
	 */
	@property( { type: Boolean, attribute: 'no-padding' } ) accessor noPadding = false;

	/**
	 * The featured image for the dialog.
	 * This is a URL to the image.
	 *
	 * @type {String}
	 */
	@property( { type: String, attribute: 'featured-image' } ) accessor featuredImage;

	/**
	 * The position of the featured image
	 *
	 * @type {String}
	 */
	@property( {
		type: String,
		attribute: 'featured-image-position'
	} ) accessor featuredImagePosition = 'center center';

	// eslint-disable-next-line require-jsdoc
	constructor() {
		super();

		// [internal] Define the action attribute
		this.actionAttribute = 'dialog-action';
	}

	/**
	 * Renders the main dialog component.
	 *
	 * @return {TemplateResult} The HTML template for the dialog component.
	 */
	render() {
		return html`
			<div 
				class="wrapper"
				?open=${ this.open }

				@click=${ this._handleClickOutside }
			>
				<div 
					class="dialog"
					size="${ this.size }"
					?no-padding=${ this.noPadding }
					?has-featured-image=${ this.hasFeaturedImage }
				>
					${ this._templateFeaturedImage }
					${ this._templateHeading }
					${ this._templateContent }
					${ this._templateFooter }
				</div>
			</div>
		`;
	}

	/**
	 * Returns the template for the dialog featured image.
	 *
	 * @return {TemplateResult|undefined} The HTML template for the featured image or undefined if there is no featured image.
	 */
	get _templateFeaturedImage() {
		if ( ! this.hasFeaturedImage ) {
			return;
		}

		return html`
			<div class="dialog-featured-image">
				<img 
					src="${ this.featuredImage }" 
					style="object-position: ${ this.featuredImagePosition }"
				/>
			</div>
		`;
	}

	/**
	 * Returns the template for the dialog heading.
	 *
	 * @return {TemplateResult|undefined} The HTML template for the heading or undefined if noHeader is true.
	 */
	get _templateHeading() {
		if ( this.noHeader ) {
			return;
		}

		return html`
			<div class="dialog-heading">
				<uo-markdown 
					.content=${ this.heading }
					allowed-tags="strong,em"
				></uo-markdown>
			</div>
		`
	}

	/**
	 * Returns the template for the dialog content.
	 *
	 * @return {TemplateResult} The HTML template for the content.
	 */
	get _templateContent() {
		return html`
			<div class="dialog-content">
				<slot></slot>
			</div>
		`;
	}

	/**
	 * Returns the template for the dialog footer.
	 *
	 * @return {TemplateResult|undefined} The HTML template for the footer or undefined if noFooter is true.
	 */
	get _templateFooter() {
		if ( this.noFooter ) {
			return;
		}

		return html`
			<div class="dialog-footer" @click=${ this._handleClick }>
				<slot name="secondary-action"></slot>
				<slot name="primary-action"></slot>
			</div>
		`;
	}

	/**
	 * Disable scroll on body when the dialog is added
	 *
	 * @return {undefined}
	 */
	connectedCallback() {
		super.connectedCallback();

		// Disable scroll on body element
		document.body.classList.add( 'uap-disable-scroll' );

		// Remove focus from current focused element (if there is one)
		// We need to do this if we want to use autofocus on fields inside the dialog
		if ( document.activeElement ) {
			document.activeElement.blur();
		}

		// Remove when pressing escape
		if ( ! this.forceManualClose ) {
			document.body.addEventListener( 'keydown', this._handleEscapeKey );
		}
	}

	/**
	 * Enable scroll on body when the dialog is added
	 *
	 * @return {undefined}
	 */
	disconnectedCallback() {
		super.connectedCallback();

		// Remove event that listens for the escape key
		document.body.removeEventListener( 'keydown', this._handleEscapeKey );

		// Disable scroll on body element
		document.body.classList.remove( 'uap-disable-scroll' );
	}

	/**
	 * Closes and destroys the modal
	 *
	 * @return {undefined}
	 */
	close( context = CLOSE_CONTEXT.PROGRAMMATICALLY_CALLED_DIRECTLY ) {
		// Fade out container
		this.style.display = 'none';
		this.parentNode.removeChild( this );

		// Enable scroll on body
		document.body.classList.remove( 'uap-disable-scroll' );

		// Check if we're closing this because it was cancelled
		const wasCancelled = [
			CLOSE_CONTEXT.CANCEL_GENERIC,
			CLOSE_CONTEXT.CANCEL_USER_CLICKED_CANCEL_BUTTON,
			CLOSE_CONTEXT.CANCEL_USER_CLICKED_OUTSIDE,
			CLOSE_CONTEXT.CANCEL_USER_PRESSED_ESCAPE_KEY,
		].includes( context );

		this.dispatchEvent( 
			new CustomEvent( 
				'uap-dialog-close', { 
					detail: { 
						was_cancelled: wasCancelled,
						context,
					},
					bubbles: true,
					composed: true
				}
			) 
		);
	}

	/**
	 * Handles the action cancellation
	 *
	 * @return {undefined}
	 */
	cancel( context = CLOSE_CONTEXT.CANCEL_GENERIC ) {
		// Close the modal
		this.close( context );
	}

	/**
	 * Handles closing the dialog by pressing "Escape"
	 *
	 * @param {KeyboardEvent} event The keyboard event
	 */
	_handleEscapeKey( event ) {
		if ( event.key === 'Escape' ) {
			try {
				// Find the dialog
				const $dialogs = document.querySelectorAll( 'uo-dialog' );

				if ( $dialogs ) {
					const $lastDialog = $dialogs[ $dialogs.length - 1 ];
					$lastDialog.close( CLOSE_CONTEXT.CANCEL_USER_PRESSED_ESCAPE_KEY );
				}
			} catch ( e ) {
				console.warn( e );
			}
		}
	}

	/**
	 * Handles clicks outside the box and evaluates whether the it should close the dialog
	 *
	 * @param {PointerEvent} event The click event
	 */
	_handleClickOutside( event ) {
		// Check if the dialog can only be closed when manually invoking .close()
		if ( this.forceManualClose ) {
			return;
		}

		// Check if the user is clicking any other element but the wrapper
		if ( event.target !== this.$wrapper ) {
			return;
		}

		// Otherwise (so if it's clicking the wrapper), close the dialog
		this.cancel( CLOSE_CONTEXT.CANCEL_USER_CLICKED_OUTSIDE );
	}

	/**
	 * Listens and handles clicks on the actions
	 *
	 * @param {PointerEvent} event The click event
	 * @return {undefined}
	 */
	_handleClick( event ) {
		// Check if the user is trying to cancel the action
		if ( event.target.closest( `[${ this.actionAttribute }="close"]` ) ) {
			// Cancel the action
			this.cancel( CLOSE_CONTEXT.CANCEL_USER_CLICKED_CANCEL_BUTTON );
		}
	}

	/**
	 * Returns whether the dialog has a featured image
	 *
	 * @return {Boolean} Whether the dialog has a featured image
	 */
	get hasFeaturedImage() {
		return ! isEmpty( this.featuredImage );
	}
}
