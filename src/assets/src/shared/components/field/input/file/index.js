// Import child components
import './async-file-data';

// Import dependencies
import { unsafeCSS, css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';

// Import local utils
import { openMediaLibrary } from './utils';

// Import global utils
import { getFileIconByMime } from '@automator/shared/utils/get-file-icon-by-mime';
import { isEmpty } from '@automator/shared/utils/is-empty';

// Import escapeHTML
import { escapeHTML } from '@wordpress/escape-html';

// Import i18n functions
import { __ } from '@wordpress/i18n';

// Import style for web component
import styles from './web-component-style.scss?inline';

@customElement( 'uo-field-input-file' ) // eslint-disable-line require-jsdoc
export class AutomatorFieldInputFile extends LitElement {
	// Define style
	static styles = css`${unsafeCSS( styles )}`;

	/**
	 * Indicates whether multiple file selection is allowed.
	 * @type {Boolean}
	 */
	@property( { type: <PERSON>olean } ) accessor multiple = false;

	/**
	 * Indicates whether the field is required
	 * @type {Boolean}
	 */
	@property( { type: Boolean } ) accessor required = false;

	/**
	 * Allowed file types (e.g., ['image/jpeg', 'image/png']).
	 * @type {Array<String>}
	 */
	@property( { type: Array } ) accessor fileTypes = [];

	/**
	 * The value of the field, which is an array of file attachments.
	 * @type {Array<Object>}
	 */
	@property( { type: Array } ) accessor value = [];

	/**
	 * Renders the component's template.
	 *
	 * @return {TemplateResult} The template for the component.
	 */
	render() {
		return html`
			<div class="wrapper">
				${ this._templateUploadFile }
				${ this._templateFiles }
			</div>
		`;
	}

	/**
	 * Handles the opening of the media library.
	 */
	handleOpenMediaLibrary() {
		// Frame options
		const frameOptions = {
			multiple: this.multiple,
			fileTypes: this.fileTypes,
		};

		// Open the media library
		openMediaLibrary( attachments => {
			// Get the value
			const value = ! this.multiple ? [ attachments ] : attachments;

			// Set the value
			this.value = value;

			// Dispatch the change event
			this.dispatchEvent(
				new CustomEvent( 'change', {
					detail: { value },
					bubbles: false,
					composed: true
				} )
			);
		}, frameOptions );
	}

	/**
	 * Handles the deletion of a file.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.filename - The name of the file.
	 * @param {Number} attachment.id - The ID of the file.
	 * @param {String} attachment.filesizeHumanReadable - The human-readable file size.
	 */
	handleDeleteFile( attachment ) {
		// Remove the file from the value
		const value = this.value.filter( file => file.id !== attachment.id );

		// Set the value
		this.value = value;

		// Dispatch the change event
		this.dispatchEvent(
			new CustomEvent( 'change', {
				detail: { value },
				bubbles: true,
				composed: true
			} )
		);
	}

	/**
	 * Template for the upload file section.
	 *
	 * @return {TemplateResult} The template for the upload file section.
	 */
	get _templateUploadFile() {
		return html`
			<div class="upload-file">
				<uo-button 
					color="secondary"
					@click=${ this.handleOpenMediaLibrary }
				>
					${ this._templateUploadButtonLabel }
				</uo-button>
			</div>
		`;
	}

	/**
	 * Template for the upload button label.
	 *
	 * @return {String} The label for the upload button.
	 */
	get _templateUploadButtonLabel() {
		if ( ! isEmpty( this.value ) ) {
			if ( this.multiple ) {
				return escapeHTML( __( 'Change files', 'uncanny-automator' ) );
			}
			return escapeHTML( __( 'Change file', 'uncanny-automator' ) );
		}

		if ( this.multiple ) {
			return escapeHTML( __( 'Select files', 'uncanny-automator' ) );
		}

		return escapeHTML( __( 'Select file', 'uncanny-automator' ) );
	}

	/**
	 * Template for displaying selected files.
	 *
	 * @return {TemplateResult|undefined} The template for the selected files.
	 */
	get _templateFiles() {
		if ( isEmpty( this.value ) ) {
			return;
		}

		return html`
			<div class="files">
				<lit-virtualizer
					.items=${ this.value }
					.renderItem=${ attachment => this._templateFile( attachment ) }
				></lit-virtualizer>
			</div>
		`;
	}

	/**
	 * Template for a single file.
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.filename - The name of the file.
	 * @param {Number} attachment.id - The ID of the file.
	 * @param {String} attachment.filesizeHumanReadable - The human-readable file size.
	 * @return {TemplateResult} The template for the file.
	 */
	_templateFile( attachment ) {
		return html`
			<div class="file">
				<div class="file-icon">
					${ this._templateFileIcon( attachment ) }
				</div>

				<div class="file-name">
					${ attachment.filename }
				</div>

				<div class="file-details">
					#${ attachment.id } | ${ attachment.filesizeHumanReadable } ${ this._templateAsyncFileData( attachment ) }
				</div>

				<div class="file-actions">
					${ this._templateFileActions( attachment ) }
				</div>
			</div>
		`;
	}

	/**
	 * Template for the file actions.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.url - The URL of the file attachment.
	 * @param {String} attachment.mime - The MIME type of the file attachment.
	 * @return {TemplateResult} The template for the file actions.
	 */
	_templateFileActions( attachment ) {
		return html`
			${ this._templateFileActionRemove( attachment ) }
		`;
	}

	/**
	 * Template for the file action remove.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.url - The URL of the file attachment.
	 * @param {String} attachment.mime - The MIME type of the file attachment.
	 * @return {TemplateResult} The template for the file action remove.
	 */
	_templateFileActionRemove( attachment ) {
		return html`
			<uo-tooltip>
				<span class="tooltip-nowrap">
					${ escapeHTML( __( 'Remove from list', 'uncanny-automator' ) ) }
				</span>
			
				<uo-button
					slot="target"
					color="transparent"
					size="extra-small"

					@click=${ () => this.handleDeleteFile( attachment ) }
				>
					<uo-icon id="times" size="16"></uo-icon>
				</uo-button>
			</uo-tooltip>
		`;
	}

	/**
	 * Template for the async file data.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.url - The URL of the file attachment.
	 * @param {String} attachment.mime - The MIME type of the file attachment.
	 * @return {TemplateResult|undefined} The template for the async file data.
	 */
	_templateAsyncFileData( attachment ) {
		// Supported mime
		const supportedMime = [
			'text/csv',
			'application/json',
			'application/xml',
			'text/xml'
		];

		// Check if the MIME type is supported
		if ( ! supportedMime.includes( attachment.mime ) ) {
			return;
		}

		return html`
			<uo-field-input-file-async-data
				.url=${ attachment.url }
				.mime=${ attachment.mime }
			></uo-field-input-file-async-data>
		`;
	}

	/**
	 * Template for the file icon.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.mime - The MIME type of the file attachment.
	 * @return {TemplateResult} The template for the file icon.
	 */
	_templateFileIcon( attachment ) {
		switch ( attachment.mime ) {
			case 'image/jpeg':
			case 'image/png':
			case 'image/gif':
			case 'image/webp':
			case 'image/svg+xml':
				return this._templateFileIconImage( attachment );

			default:
				return this._templateFileIconDefault( attachment );
		}
	}

	/**
	 * Template for image file icon.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.url - The URL of the file attachment.
	 * @param {String} attachment.alt - The alt text for the file attachment.
	 * @return {TemplateResult} The template for the image file icon
	 */
	_templateFileIconImage( attachment ) {
		return html`
			<img 
				src="${ attachment.url }" 
				alt="${ attachment.alt }" 
				class="file-icon__image-preview"
			/>
		`;
	}

	/**
	 * Template for default file icon.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.mime - The MIME type of the file attachment.
	 * @return {TemplateResult} The template for the default file icon
	 */
	_templateFileIconDefault( attachment ) {
		return html`
			<img 
				src="${ getFileIconByMime( attachment.mime ) }" 
				class="file-icon__file-type"
			/>
		`;
	}
}
