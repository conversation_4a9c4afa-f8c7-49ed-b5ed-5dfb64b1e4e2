/**
 * Chip
 * <uo-chip>
 *
 * Attributes:
 */

// Import dependencies
import { unsafeCSS, css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { isDefined } from '@automator/shared/utils/is-defined';
import { isEmpty } from '@automator/shared/utils/is-empty';

// Import variables
import './variables.scss';

// Import style for web component
import styles from './web-component-style.scss?inline';

@customElement( 'uo-chip' ) // eslint-disable-line require-jsdoc
export class AutomatorChip extends LitElement {
	// Define style
	static styles = css`${ unsafeCSS( styles ) }`;

	// Properties
	@property( { type: Boolean } ) accessor filled = false;

	@property( { type: String } ) accessor variant = 'default'; // default, pill

	@property( { type: String } ) accessor size = 'regular'; // regular, small

	@property( { type: String } ) accessor color = 'default'; // default, primary, info

	// eslint-disable-next-line require-jsdoc
	constructor() {
		super();
	}

	// eslint-disable-next-line require-jsdoc
	render() {
		return html`
			<div 
				class="chip"
				?filled=${ this.filled }
				color=${ this.color }
				size=${ this.size }
				variant=${ this.variant }
			>
				<slot name="leading-icon"></slot>

				<slot></slot>

				<slot name="trailing-icon"></slot>
			</div>
		`;
	}
}
