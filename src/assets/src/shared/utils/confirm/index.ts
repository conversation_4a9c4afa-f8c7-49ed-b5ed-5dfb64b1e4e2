/**
 * Provides the `confirm` utility function for displaying a confirmation dialog, and its associated `ConfirmOptions` type.
 * @module confirm
 */

import { html, render } from 'lit';

// Import escapeHTML
import { escapeHTML } from '@wordpress/escape-html';

// Import i18n functions
import { __, _x } from '@wordpress/i18n';

// Import types
import type { ConfirmOptions } from './types';

/**
 * Prompts a confirmation dialog using the `<uo-dialog>` web component, rendered dynamically into the document body.
 *
 * **Behavior:**
 * - Takes an optional `options` object to customize the dialog's content (`content`, `heading`, `confirmButtonLabel`, `cancelButtonLabel`).
 * - Provides default, internationalized, and escaped text for heading and button labels if not specified in options.
 * - Creates a temporary `div` wrapper element and appends it to `document.body`.
 * - Renders a `<uo-dialog>` component (along with `<uo-markdown>` for content and `<uo-button>` for actions) inside the wrapper.
 * - The dialog is opened immediately (`open` attribute).
 * - Returns a `Promise<void>`:
 * - The Promise **resolves** (with no value) if the user clicks the primary confirmation button (slot="primary-action").
 * - The Promise **rejects** (with an `Error('Confirmation cancelled by user.')`) if the user clicks the secondary cancel button (slot="secondary-action") OR if the `<uo-dialog>` component emits its standard `close` event (e.g., user presses ESC key, clicks outside if configured, etc.).
 * - **Crucially**, _before_ resolving or rejecting the promise, the temporary `div` wrapper (containing the dialog) is removed from the DOM to ensure cleanup.
 *
 * @since 4.5 Initial implementation.
 * @since 6.6.0 Function now returns a Promise for asynchronous event handling, replacing the previous `onEnd` callback.
 * @param {ConfirmOptions} [options={}] - Optional configuration object for the dialog.
 * @param {string} [options.content=''] - The main content/body of the dialog. Can include simple text or markdown recognized by `<uo-markdown>`. Defaults to an empty string.
 * @param {string} [options.heading] - The title/heading text displayed at the top of the dialog. Defaults to localized "Are you sure?".
 * @param {string} [options.confirmButtonLabel] - The text label for the primary confirmation button. Defaults to localized "Confirm".
 * @param {string} [options.cancelButtonLabel] - The text label for the secondary cancellation button. Defaults to localized "Cancel".
 * @return {Promise<void>} A Promise that resolves when confirmed, and rejects when cancelled or closed.
 *
 * @example Importing the confirm utility
 * ```typescript
 * import { confirm } from '@automator/shared/utils/confirm';
 * ```
 *
 * @example Basic confirmation with default text
 * ```typescript
 * try {
 *     await confirm();
 *     console.log( 'User confirmed!' );
 *     // Proceed with action
 * } catch ( error: unknown ) {
 *     console.log('User cancelled or closed the dialog.');
 *     // Handle cancellation
 * }
 * ```
 *
 * @example Confirmation with custom content and labels
 * ```typescript
 * const customOptions: ConfirmOptions = {
 *     heading: __( 'Delete item?', 'uncanny-automator' ),
 *     content: __( 'This action cannot be undone. Are you sure you want to delete **this item**?', 'uncanny-automator' ),
 *     confirmButtonLabel: __( 'Yes, delete', 'uncanny-automator' ),
 *     cancelButtonLabel: __( 'Keep item', 'uncanny-automator' )
 * };
 *
 * try {
 *     await confirm( customOptions );
 *     console.log( 'Deletion confirmed.' );
 *     // Perform deletion
 * } catch ( error: unknown ) {
 *     console.log( 'Deletion cancelled.' );
 * }
 * ```
 *
 * @remarks
 * - The rejection error message is fixed as 'Confirmation cancelled by user.' regardless of the cancellation method (button click or dialog close event).
 */
export function confirm( {
	content = '',
	heading = escapeHTML( __( 'Are you sure?', 'uncanny-automator' ) ),
	confirmButtonLabel = escapeHTML( _x( 'Confirm', 'Verb', 'uncanny-automator' ) ),
	cancelButtonLabel = escapeHTML( _x( 'Cancel', 'Verb', 'uncanny-automator' ) ),
}: ConfirmOptions = {} ): Promise<void> {
	// 1. Create a temporary wrapper element
	const $wrapper: HTMLDivElement = document.createElement( 'div' );
	document.body.append( $wrapper );

	// 2. Function to clean up the wrapper element from the DOM
	const cleanup = () => {
		// Remove the wrapper element
		$wrapper.remove();
	};

	// 3. Create and return the Promise
	return new Promise<void>( ( resolve, reject ) => {
		// Define event handlers for confirmation and cancellation
		const handleConfirm = () => {
			cleanup(); // Clean up the DOM element *before* resolving
			resolve(); // Resolve the promise (confirmation successful)
		};

		const handleCancel = () => {
			cleanup(); // Clean up the DOM element *before* rejecting
			reject( new Error( 'Confirmation cancelled by user.' ) );
		};

		const handleClose = ( event: CustomEvent ) => {
			cleanup(); // Clean up the DOM element *before* rejecting

			// Check if the dialog was cancelled
			const wasCancelled = event.detail.was_cancelled;

			if ( wasCancelled ) {
				reject( new Error( 'Confirmation cancelled by user.' ) );
			}
		};

		// 4. Render the dialog component into the wrapper
		render(
			html`
				<uo-dialog
					.heading=${ heading }
					force-manual-close
					open

					@uap-dialog-close=${ handleClose }
				>
					${ content }

					<uo-button
						slot="primary-action"
						dialog-action="close"

						@click=${ handleConfirm }
					>
						${ confirmButtonLabel }
					</uo-button>

					<uo-button
						slot="secondary-action"
						dialog-action="close"
						color="secondary"

						@click=${ handleCancel }
					>
						${ cancelButtonLabel }
					</uo-button>
				</uo-dialog>
			`,
			$wrapper
		);
	} );
};

export type { ConfirmOptions };
