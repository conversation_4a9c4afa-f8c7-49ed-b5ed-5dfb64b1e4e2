import { confirm } from './';
import type { ConfirmOptions } from './types';

describe( 'utilities/confirm', () => {
	let wrapper: HTMLDivElement | null = null;
	let appendSpy: jest.SpyInstance;
	let removeSpy: jest.SpyInstance | undefined;

	beforeEach( () => {
		// Reset mocks before each test
		jest.clearAllMocks(); // Clears calls, instances, etc. for all mocks

		// Spy on document.body.append
		appendSpy = jest.spyOn( document.body, 'append' ).mockImplementation( ( ...args: ( Node | string )[] ) => {
			// Find the HTMLDivElement among the arguments (likely the first one)
			const divElement = args.find( arg => arg instanceof HTMLDivElement ) as HTMLDivElement | undefined;

			if ( divElement ) {
				// Store the appended wrapper element
				wrapper = divElement;
				// IMPORTANT: Spy on the *specific instance's* remove method *after* it's created
				removeSpy = jest.spyOn( wrapper, 'remove' );
			}

			// Call the original appendChild for each Node argument for JSD<PERSON> to work correctly.
			// This mimics the behavior of 'append' more closely.
			args.forEach( arg => {
				if ( arg instanceof Node ) {
					document.body.appendChild( arg );
				} else {
					// append can also take strings, appendChild needs Text nodes
					document.body.appendChild( document.createTextNode( String( arg ) ) );
				}
			} );
		} );

		// Clean up the DOM from previous tests
		document.body.innerHTML = '';
		wrapper = null;
		removeSpy = undefined;
	} );

	afterEach( () => {
		// Restore spies to their original implementations
		appendSpy.mockRestore();
		// removeSpy is attached to the specific wrapper, no need to restore globally
		// But we ensure the wrapper is removed if a test failed mid-way
		wrapper?.remove(); // Use actual remove if wrapper still exists
		document.body.innerHTML = ''; // Clean body again
		wrapper = null;
		removeSpy = undefined;
	} );

	// Helper function to wait for the next tick, allowing microtasks like Promise resolution and Lit's render cycle in JSDOM to complete.
	const tick = () => new Promise( resolve => process.nextTick( resolve ) );

	/**
	 * Verifies that calling `confirm()` results in a new `div` element being appended to the `document.body`.
	 * @example Checks `document.body.append` was called and the wrapper exists in DOM.
	 */
	test( 'should append a div wrapper to document.body', async () => {
		// Call confirm but don't wait for promise resolution/rejection in this test
		const promise = confirm();
		await tick(); // Allow time for append to potentially happen

		expect( appendSpy ).toHaveBeenCalledTimes( 1 );
		expect( wrapper ).toBeInstanceOf( HTMLDivElement ); // Check if wrapper was captured
		expect( document.body.contains( wrapper ! ) ).toBe( true ); // Check if it's in the DOM

		// Manually trigger cleanup for this test since we didn't resolve/reject
		wrapper?.querySelector<HTMLElement>( '[slot="secondary-action"]' )?.click();
		await expect( promise ).rejects.toThrow(); // Wait for rejection to ensure cleanup runs
	} );

	/**
	 * Tests that when `confirm()` is called with default options, the rendered dialog displays the default heading and button labels.
	 * Input: `confirm()` (uses default options)
	 * @example Buttons render with text 'Confirm' and 'Cancel'.
	 */
	test( 'should render dialog with default heading and labels using mocked i18n', async () => {
		const promise = confirm(); // Use default options
		await tick(); // Allow rendering

		expect( wrapper ).not.toBeNull();
		const dialog = wrapper!.querySelector( 'uo-dialog' );
		const confirmBtn = wrapper!.querySelector<HTMLElement>( '[slot="primary-action"]' );
		const cancelBtn = wrapper!.querySelector<HTMLElement>( '[slot="secondary-action"]' );

		expect( dialog ).not.toBeNull();
		// Note: Accessing properties like `.heading` might not work without defined custom elements.
		// We test if the *default values* are used.
		// We check the button text content as rendered by lit.
		expect( confirmBtn?.textContent?.trim() ).toBe( 'Confirm' );
		expect( cancelBtn?.textContent?.trim() ).toBe( 'Cancel' );

		// Cleanup
		cancelBtn?.click();
		await expect( promise ).rejects.toThrow();
	} );

	/**
	 * Tests that when `confirm()` is called with custom options, the rendered dialog correctly displays the provided custom heading (implicitly via content), content, and button labels.
	 * @param {ConfirmOptions} options - Input options: { heading: '...', content: '...', confirmButtonLabel: '...', cancelButtonLabel: '...' }
	 * @example Dialog renders with the custom text provided in options.
	 */
	test( 'should render dialog with custom heading, content, and labels', async () => {
		const options: ConfirmOptions = {
			heading: 'Custom test heading',
			content: 'Some **custom** message.',
			confirmButtonLabel: 'Yes, proceed!',
			cancelButtonLabel: 'No, go back',
		};

		const promise = confirm( options );
		await tick();

		expect( wrapper ).not.toBeNull();
		const dialog = wrapper!.querySelector( 'uo-dialog' );
		const confirmBtn = wrapper!.querySelector<HTMLElement>( '[slot="primary-action"]' );
		const cancelBtn = wrapper!.querySelector<HTMLElement>( '[slot="secondary-action"]' );

		expect( dialog ).not.toBeNull();
		// Check if custom text content appears where expected
		expect( confirmBtn?.textContent?.trim() ).toBe( options.confirmButtonLabel );
		expect( cancelBtn?.textContent?.trim() ).toBe( options.cancelButtonLabel );

		// Cleanup
		cancelBtn?.click();
		await expect( promise ).rejects.toThrow();
	} );

	/**
	 * Tests the primary success path: verifies that the Promise returned by `confirm()` resolves successfully after the confirm button is clicked.
	 * Input: `confirm()` call, followed by simulation of confirm button click.
	 * @example Promise resolves to undefined (void).
	 */
	test( 'should resolve the promise (with void) when confirm button is clicked', async () => {
		const promise = confirm();
		await tick();

		const confirmBtn = wrapper!.querySelector<HTMLElement>( '[slot="primary-action"]' );
		expect( confirmBtn ).not.toBeNull();

		// Simulate user clicking confirm
		confirmBtn!.click();

		// Assert that the promise resolves successfully. The resolved value is void/undefined.
		await expect( promise ).resolves.toBeUndefined();
	} );

	/**
	 * Tests a cancellation path: verifies that the Promise returned by `confirm()` rejects with a specific error message after the cancel button is clicked.
	 * Input: `confirm()` call, followed by simulation of cancel button click.
	 * @example Promise rejects with `Error('Confirmation cancelled by user.')`.
	 */
	test( 'should reject the promise with an error when cancel button is clicked', async () => {
		const promise = confirm();
		await tick();

		const cancelBtn = wrapper!.querySelector<HTMLElement>( '[slot="secondary-action"]' );
		expect( cancelBtn ).not.toBeNull();

		// Simulate user clicking cancel
		cancelBtn!.click();

		// Assert that the promise rejects with the specific error message
		await expect( promise ).rejects.toThrow( 'Confirmation cancelled by user.' );
	} );

	/**
	 * Tests another cancellation path: verifies that the Promise returned by `confirm()` rejects with a specific error message after the `uo-dialog` element dispatches 'uo-dialog-close' event.
	 * Input: `confirm()` call, followed by simulation of 'uo-dialog-close' event dispatch from dialog.
	 * @example Promise rejects with `Error('Confirmation cancelled by user.')`.
	 */
	test( 'should reject the promise with an error when dialog "uap-dialog-close" event is dispatched', async () => {
		const promise = confirm();
		await tick();

		const dialog = wrapper!.querySelector( 'uo-dialog' );
		expect( dialog ).not.toBeNull();

		// Simulate the dialog component emitting its standard 'uap-dialog-close' event
		dialog!.dispatchEvent( 
			new CustomEvent( 
				'uap-dialog-close', 
				{ 
					detail: { 
						was_cancelled: true,
						context: 'cancel-user-clicked-cancel-button',
					},
					bubbles: true,
					composed: true,
				} 
			) 
		);

		// Assert that the promise rejects with the specific error message
		await expect( promise ).rejects.toThrow( 'Confirmation cancelled by user.' );
	} );

	/**
	 * Verifies the DOM cleanup mechanism on the confirmation path by checking that the `remove()` method is called on the wrapper div after confirmation.
	 * Input: `confirm()` call, followed by simulation of confirm button click.
	 * @example `wrapper.remove()` spy is called once.
	 */
	test( 'should remove the wrapper element from DOM on confirmation', async () => {
		const promise = confirm();
		await tick(); // Wait for render and wrapper capture

		// At this point, `appendSpy` should have run and assigned the spy to `removeSpy`
		expect( removeSpy ).toBeDefined(); // Verify the spy was set up
		expect( typeof removeSpy?.mock ).toBe( 'object' ); // More robust check it's a Jest spy

		const confirmBtn = wrapper!.querySelector<HTMLElement>( '[slot="primary-action"]' );
		expect( confirmBtn ).not.toBeNull();
		confirmBtn!.click(); // Trigger confirmation

		await expect( promise ).resolves.toBeUndefined(); // Wait for promise resolution path

		// Assert that the wrapper's remove method spy was called
		expect( removeSpy ).toHaveBeenCalledTimes( 1 );

		// Verify it's no longer in the body
		expect( document.body.contains( wrapper! ) ).toBe( false );
	} );

	/**
	 * Verifies the DOM cleanup mechanism on the cancel button path by checking that the `remove()` method is called on the wrapper div after cancellation.
	 * Input: `confirm()` call, followed by simulation of cancel button click.
	 * @example `wrapper.remove()` spy is called once.
	 */
	test( 'should remove the wrapper element from DOM on cancellation (button)', async () => {
		const promise = confirm();
		await tick(); // Wait for render and wrapper capture

		expect( removeSpy ).toBeDefined(); // Verify spy setup
		expect( typeof removeSpy?.mock ).toBe( 'object' );

		const cancelBtn = wrapper!.querySelector<HTMLElement>( '[slot="secondary-action"]' );
		expect( cancelBtn ).not.toBeNull();
		cancelBtn!.click(); // Trigger cancellation

		await expect( promise ).rejects.toThrow(); // Wait for promise rejection path

		// Use the removeSpy created via appendSpy
		expect( removeSpy ).toHaveBeenCalledTimes( 1 );
		expect( document.body.contains( wrapper! ) ).toBe( false );
	} );

	/**
	 * Verifies the DOM cleanup mechanism on the dialog 'close' event path by checking that the `remove()` method is called on the wrapper div after cancellation.
	 * Input: `confirm()` call, followed by simulation of 'close' event dispatch from dialog.
	 * @example `wrapper.remove()` spy is called once.
	 */
	test( 'should remove the wrapper element from DOM on cancellation (dialog close)', async () => {
		const promise = confirm();
		await tick(); // Wait for render and wrapper capture

		expect( removeSpy ).toBeDefined(); // Verify spy setup
		expect( typeof removeSpy?.mock ).toBe( 'object' );

		const dialog = wrapper!.querySelector( 'uo-dialog' );
		expect( dialog ).not.toBeNull();
		dialog!.dispatchEvent( 
			new CustomEvent( 
				'uap-dialog-close', 
				{ 
					detail: { 
						was_cancelled: true,
						context: 'cancel-user-clicked-cancel-button',
					},
					bubbles: true,
					composed: true,
				} 
			) 
		); // Trigger cancellation

		await expect( promise ).rejects.toThrow(); // Wait for promise rejection path

		// Use the removeSpy created via appendSpy
		expect( removeSpy ).toHaveBeenCalledTimes( 1 );
		expect( document.body.contains( wrapper! ) ).toBe( false );
	} );
} );