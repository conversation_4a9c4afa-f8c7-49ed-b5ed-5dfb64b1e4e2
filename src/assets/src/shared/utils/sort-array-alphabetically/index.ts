/**
 * Provides the `sortArrayAlphabetically` utility function for sorting an array of objects or arrays alphabetically in place based on the value found at a specified property path.
 * @module sortArrayAlphabetically
 */

// Import global utils
import { getValueByPath } from '@automator/shared/utils/get-value-by-path';
import { isDefined } from '@automator/shared/utils/is-defined';
import { isEmpty } from '@automator/shared/utils/is-empty';

/**
 * Sorts an array of objects or arrays alphabetically in place based on the value found at a specified property path.
 *
 * **Behavior:**
 * 1. **Input:** Takes an array (`arr`) and a `path` string (dot and/or bracket notation).
 * 2. **Empty handling:** If the input `arr` is null, undefined, or empty (checked via `isEmpty`), returns a new empty array `[]`.
 * 3. **Copying:** Creates a shallow copy of the input array using the spread syntax (`[...arr]`).
 * 4. **Sorting:** Uses `Array.prototype.sort()` to sort the *copy* of the input array. The original array remains unchanged.
 * 5. **Value retrieval:** For each pair of elements (`a`, `b`) being compared from the copied array, it uses `getValueByPath(element, path)` to retrieve the values to compare.
 * 6. **Undefined handling:** If `getValueByPath` returns `undefined` for one element but not the other, the element with the `undefined` value is sorted towards the end of the array. If both values are `undefined`, they are considered equal in sort order.
 * 7. **String conversion & cleaning:** Converts the retrieved (and defined) values to strings using `String()`. It then removes any curly braces (`{` or `}`) from these strings to allow sorting of token-like values based on their content (e.g., `{{B}}` is compared as `B`).
 * 8. **Comparison:** Performs a case-insensitive comparison of the cleaned strings using `String.prototype.localeCompare()`.
 * 9. **Return value:** Returns a *new* array containing the sorted elements. The original input array is not modified.
 *
 * @since 5.0 Initial implementation.
 * @since 6.6.0 Made the function immutable.
 *
 * @template T - The type of elements in the array. Expected to be indexable objects or arrays compatible with `getValueByPath`.
 * @param {T[] | null | undefined} arr - The array to sort. This array will **not** be modified. If null or undefined, a new empty array is returned.
 * @param {string} path - The dot-separated and/or bracket-notation path to the property to sort by (e.g., 'name', 'details.address[0].city').
 * @return {T[]} A **new** array, sorted alphabetically based on the specified path. Returns a new empty array if the input `arr` was null, undefined, or empty.
 *
 * @example Importing the sortArrayAlphabetically utility
 * ```typescript
 * import { sortArrayAlphabetically } from '@automator/shared/utils/sort-array-alphabetically';
 * ```
 *
 * @example Sorting by top-level property (returns new array)
 * Sorts an array of user objects by their 'lastName' property without mutating the original.
 * ```typescript
 * const users = [
 *     { lastName: 'Smith' },
 *     { lastName: 'Jones' },
 *     { lastName: 'Adams' }
 * ];
 *
 * const sortedUsers = sortArrayAlphabetically( users, 'lastName' );
 *
 * // sortedUsers is: [ { lastName: 'Adams' }, { lastName: 'Jones' }, { lastName: 'Smith' } ]
 * // users remains unchanged: [ { lastName: 'Smith' }, { lastName: 'Jones' }, { lastName: 'Adams' } ]
 * console.log( users === sortedUsers ); // => false
 * ```
 *
 * @example Sorting by nested property
 * Sorts an array of order objects by the 'city' within the 'shippingAddress'.
 * ```typescript
 * const orders = [
 *     { id: 1, shippingAddress: { city: 'London' } },
 *     { id: 2, shippingAddress: { city: 'Paris' } },
 *     { id: 3, shippingAddress: { city: 'Berlin' } }
 * ];
 *
 * const sortedOrders = sortArrayAlphabetically( orders, 'shippingAddress.city' );
 *
 * // sortedOrders will be sorted by city: Berlin, London, Paris
 * // orders array remains in its original order.
 * ```
 *
 * @example Case-insensitive sorting
 * Demonstrates that sorting ignores case.
 * ```typescript
 * const items = [
 *     { name: 'apple' },
 *     { name: 'Banana' },
 *     { name: 'Cherry' }
 * ];
 *
 * const sortedItems = sortArrayAlphabetically( items, 'name' );
 *
 * // sortedItems is: [ { name: 'apple' }, { name: 'Banana' }, { name: 'Cherry' } ]
 * ```
 *
 * @example Handling undefined/missing properties
 * Objects where the path leads to `undefined` are sorted to the end in the new array.
 * ```typescript
 * const data = [
 *     { value: 'B' },
 *     {}, // Missing 'value'
 *     { value: 'A' },
 *     { value: undefined }
 * ];
 *
 * const sortedData = sortArrayAlphabetically( data, 'value' );
 *
 * // sortedData is: [ { value: 'A' }, { value: 'B' }, { value: undefined }, {} ]
 * // (Order of undefined/missing might vary but they are last)
 * ```
 *
 * @example Sorting numbers alphabetically
 * Shows that numbers are converted to strings before comparison.
 * ```typescript
 * const data = [
 *     { score: 100 },
 *     { score: 20 },
 *     { score: 3 }
 * ];
 *
 * const sortedData = sortArrayAlphabetically( data, 'score' );
 *
 * // sortedData is: [ { score: 100 }, { score: 20 }, { score: 3 } ] (sorted as "100", "20", "3")
 * ```
 *
 * @example Sorting token-like strings
 * Ignores curly braces during comparison.
 * ```typescript
 * const data = [
 *     { token: '{{C}}' },
 *     { token: 'A' },
 *     { token: '{{B}}' }
 * ];
 *
 * const sortedData = sortArrayAlphabetically( data, 'token' );
 *
 * // sortedData is: [ { token: 'A' }, { token: '{{B}}' }, { token: '{{C}}' } ]
 * ```
 *
 * @remarks
 * - **Immutability:** This function returns a **new sorted array** and does **not** modify the original input array.
 * - It relies on the `getValueByPath` utility to retrieve nested values.
 * - Sorting is purely alphabetical based on the string representation of the values after removing curly braces. This means, for example, that "10" will be sorted before "2". For numerical or date sorting, a different comparison logic or a pre-transformation of values would be needed.
 */
export function sortArrayAlphabetically<T extends Record<string, any> | any[]>(
	arr: T[] | null | undefined,
	path: string
): T[] {
	// Handle null, undefined, or empty array input by returning a new empty array
	if ( isEmpty( arr ) ) {
		return [];
	}

	// Create a shallow copy of the input array to avoid mutating the original
	const arrayCopy = [ ...arr ];

	// Use the Array.prototype.sort() function to sort the copied array.
	return arrayCopy.sort( ( a: T, b: T ) => {
		// Retrieve values using getValueByPath.
		const aValue: unknown = getValueByPath( a, path );
		const bValue: unknown = getValueByPath( b, path );

		// Handle cases where values might be undefined or null using isDefined.
		// Undefined/null values are sorted towards the end.
		const aIsUndefined = ! isDefined( aValue );
		const bIsUndefined = ! isDefined( bValue );

		if ( aIsUndefined && bIsUndefined ) {
			return 0; // Both are undefined/null, consider them equal
		}
		if ( aIsUndefined ) {
			return 1; // 'a' is undefined/null, sort 'a' after 'b'
		}
		if ( bIsUndefined ) {
			return -1; // 'b' is undefined/null, sort 'b' after 'a'
		}

		// At this point, both aValue and bValue are defined (not null/undefined).
		// Convert the values to strings and to lower case for case-insensitive comparison.
		let aString = String( aValue ).toLowerCase();
		let bString = String( bValue ).toLowerCase();

		// Remove the '{' and '}' characters from the strings for token sorting
		aString = aString.replace( /[{}]/g, '' );
		bString = bString.replace( /[{}]/g, '' );

		// Use the String.prototype.localeCompare() method for robust alphabetical comparison.
		return aString.localeCompare( bString );
	} );
};

