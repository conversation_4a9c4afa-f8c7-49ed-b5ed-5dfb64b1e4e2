/**
 * Provides the `getUniqueID` utility function for creating a highly likely unique ID string intended for client-side use.
 * @module getUniqueID
 */

/**
 * Creates a highly likely unique ID string intended for client-side use.
 *
 * **Behavior:**
 * - Retrieves the current timestamp using `Date.now()` and converts it to a base-36 string.
 * - Generates a pseudo-random number using `Math.random()`, converts it to base-36, and takes a substring (typically removing the leading "0.").
 * - Concatenates the timestamp string and the random number string.
 * - Returns the resulting combined string.
 *
 * This combination provides a high degree of practical uniqueness, suitable for many non-cryptographic purposes like generating IDs for DOM elements or tracking keys.
 *
 * @since 4.0 Initial implementation.
 * @since 6.6.0 Removed the `window._uap_unique_hash` array, given that the probability of a collision is already negligible.
 * @return {string} A unique ID string based on timestamp and randomness.
 *
 * @example Importing the getUniqueID utility
 * ```typescript
 * import { getUniqueID } from '@automator/shared/utils/get-unique-id';
 * ```
 *
 * @example Generating unique IDs
 * Demonstrates generating two distinct IDs in succession.
 * ```typescript
 * const firstId = getUniqueID();
 * // => e.g., 'kox8duae4onk9omg0z7x'
 *
 * const secondId = getUniqueID();
 * // => e.g., 'kox8duae4p2l3qrstuvy'
 *
 * console.log( firstId !== secondId ); // => true
 * ```
 *
 * @remarks
 * **Collision probability:**
 * The theoretical probability of two independent calls generating the exact same ID is extremely low. It requires both:
 * 1. The calls occurring within the *same millisecond* (so `Date.now()` returns the identical value).
 * 2. `Math.random()` producing the *exact same pseudo-random sequence* for both calls.
 *
 * The random component typically adds around 11 base-36 characters (approximately 57 bits of entropy). The chance of just the random parts colliding is about 1 in 36^11 (roughly 1 in 1.3 x 10^17, or 1 in 130 quadrillion).
 *
 * Therefore, the overall probability of a collision is negligible for most practical applications. This implementation relies solely on this statistical uniqueness and does *not* track previously generated IDs.
 */
export function getUniqueID(): string {
	// Generate an ID candidate using time and randomness, base-36 encoded.
	return Date.now().toString( 36 ) + Math.random().toString( 36 ).substring( 2 );
};
