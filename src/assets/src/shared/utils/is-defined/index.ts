/**
 * Provides the `isDefined` utility function for checking if a variable is defined (i.e., not `undefined` and not `null`), and its associated `IsDefinedOptions` type.
 * @module isDefined
 */

/**
 * [**Type guard**](https://www.typescriptlang.org/docs/handbook/2/narrowing.html) that checks if a variable is defined (i.e., not `undefined` and not `null`).
 *
 * **Behavior:**
 * - Takes a variable of any type `T`.
 * - Performs a strict check to see if the variable is *not* equal to `undefined` AND *not* equal to `null`.
 * - Returns `true` if both conditions are met (the variable has a value other than `null` or `undefined`).
 * - Returns `false` if the variable is either `null` or `undefined`.
 * - Acts as a TypeScript type predicate (`variable is NonNullable<T>`). If it returns `true`, TypeScript will narrow the type of the variable within the current scope, removing `null` and `undefined` from its possible types.
 *
 * **Summary table:**
 *
 * | Input value (`variable`) | `typeof variable` | `variable === null` | `isDefined( variable )` Result | TypeScript type after `if ( isDefined( variable ) )` |
 * | :----------------------- | :---------------- | :------------------ | :----------------------------- | :--------------------------------------------------- |
 * | `'hello'`                | `'string'`        | `false`             | `true`                         | `string`                                             |
 * | `''`                     | `'string'`        | `false`             | `true`                         | `string`                                             |
 * | `123`                    | `'number'`        | `false`             | `true`                         | `number`                                             |
 * | `0`                      | `'number'`        | `false`             | `true`                         | `number`                                             |
 * | `NaN`                    | `'number'`        | `false`             | `true`                         | `number`                                             |
 * | `true`                   | `'boolean'`       | `false`             | `true`                         | `boolean` (specifically `true`)                      |
 * | `false`                  | `'boolean'`       | `false`             | `true`                         | `boolean` (specifically `false`)                     |
 * | `{}`                     | `'object'`        | `false`             | `true`                         | `object` (or specific object type)                   |
 * | `[]`                     | `'object'`        | `false`             | `true`                         | `any[]` (or specific array type)                     |
 * | `()=>{}`                 | `'function'`      | `false`             | `true`                         | `Function` (or specific function type)               |
 * | `Symbol('a')`            | `'symbol'`        | `false`             | `true`                         | `symbol`                                             |
 * | `null`                   | `'object'`        | `true`              | `false`                        | (Condition is false)                                 |
 * | `undefined`              | `'undefined'`     | `false`             | `false`                        | (Condition is false)                                 |
 *
 * @since 3.3 Initial implementation.
 * @since 3.5 Converted into a utility function.
 * @since 6.6.0 Converted into a TypeScript type guard.
 * @template T - The potential type of the variable (which might include null or undefined).
 * @param {T} variable - The variable being evaluated. Can be of any type, including `null` or `undefined`.
 * @return {variable is NonNullable<T>} `true` if the variable is neither `undefined` nor `null`, acting as a type predicate. Otherwise, returns `false`.
 *
 * @example Importing the isDefined utility
 * ```typescript
 * import { isDefined } from '@automator/shared/utils/is-defined';
 * ```
 *
 * @example Basic usage with potentially undefined value
 * Checks if a variable fetched from somewhere exists before using it.
 * ```typescript
 * declare function fetchData(): string | undefined;
 * const data = fetchData();
 *
 * if ( ! isDefined( data ) ) {
 *     console.log( 'No data received.' );
 *     return;
 * }
 *
 * // TypeScript knows `data` is 'string' now, so you can use String methods on it safely and be confident it won't throw an error.
 * console.log( `Received data: ${ data.toUpperCase() }` );
 * ```
 *
 * @example Filtering an array
 * Removes null and undefined entries from an array while satisfying TypeScript.
 * ```typescript
 * const mixedArray: ( string | null | number | undefined )[] = [ 'hello', null, 42, undefined, 'world' ];
 * const definedItems: ( string | number )[] = mixedArray.filter( isDefined );
 * // definedItems is [ 'hello', 42, 'world' ]
 * // TypeScript knows the type is (string | number)[]
 * console.log( definedItems );
 * ```
 *
 * @example Checking potentially null DOM element
 * Safely access properties after checking if an element was found.
 * ```typescript
 * const $element = document.getElementById( 'my-element' ); // Type: HTMLElement | null
 *
 * if ( ! isDefined( $element ) ) {
 *     console.log( 'Element not found.' );
 *     return;
 * }
 *
 * // TypeScript knows `element` is HTMLElement now, so you can use HTMLElement methods on it safely and be confident it won't throw an error.
 * $element.textContent = 'Element found!';
 * ```
 *
 * @remarks
 * - This function is a **type guard**. Its primary benefit is providing type narrowing information to the TypeScript compiler within conditional blocks (`if`, ternary operators, etc.).
 * - It distinguishes between `undefined`/`null` and other "falsy" values like `0`, `''` (empty string), and `false`, which are all considered "defined" by this function.
 */
export function isDefined<T>( variable: T ): variable is NonNullable<T> {
	// Check if the variable is neither undefined nor null.
	// NonNullable<T> is a built-in utility type that removes undefined and null from T.
	return typeof variable !== 'undefined' && variable !== null;
};
