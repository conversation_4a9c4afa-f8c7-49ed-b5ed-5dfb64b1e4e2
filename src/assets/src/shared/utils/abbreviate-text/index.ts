/**
 * Provides the `abbreviateText` utility function for shortening strings while preserving tokens, and its associated `AbbreviateTextOptions` type.
 * @module abbreviateText
 */

// Import types
import type { AbbreviateTextOptions } from './types';

/**
 * Converts long strings into shorter strings that show the start and end of it, while preserving tokens enclosed in {{...}}
 *
 * **Default behavior:**
 * By default (when no `options` are provided):
 * - The function abbreviates strings containing 7 or more words.
 * - It displays the first 3 words and the last 3 words of the string.
 * - These parts are joined by the separator " ... " (with leading/trailing spaces).
 * - Tokens (e.g., `{{some_token}}`) are preserved and not broken.
 * - Strings with 6 or fewer words are returned in their original form.
 * - If the input `text` is not a string or is an empty string, it's returned as is (with `undefined` input specifically resulting in an empty string `''`).
 *
 * The function offers customization for the number of words, the separator string, and the abbreviation threshold via the `options` parameter.
 *
 * @since 4.13 Initial implementation.
 * @since 6.6.0 Defined the `options` parameter.
 * @param {string} text - The string to abbreviate. Defaults to an empty string.
 * @param {Partial<AbbreviateTextOptions>} options - Options to configure the abbreviation.
 * @return {string} The abbreviated string, or the original string based on length and threshold conditions, or if an error occurs.
 *
 * @example Importing the abbreviateText utility
 * ```typescript
 * import { abbreviateText } from '@automator/shared/utils/abbreviate-text';
 * ```
 *
 * @example Default behavior with a long string
 * When called with a long string and no custom options, the output shows the first 3 words and the last 3 words, separated by the default " ... " string.
 * ```typescript
 * const longText = 'The quick brown fox jumps over the lazy dog and then jumps over the fence';
 * abbreviateText( longText );
 * // => "The quick brown ... over the fence"
 * ```
 *
 * @example Custom number of words (even)
 * With `options.words` set to 4, the input string is abbreviated to its first 2 words and its last 2 words.
 * ```typescript
 * const longText = 'The quick brown fox jumps over the lazy dog and then jumps over the fence';
 * abbreviateText( longText, { words: 4 } );
 * // => "The quick ... the fence"
 * ```
 *
 * @example Custom number of words (odd)
 * If `options.words` is 5 (an odd number), the abbreviation keeps 3 words from the start of the string and 2 words from its end.
 * ```typescript
 * const longText = 'The quick brown fox jumps over the lazy dog and then jumps over the fence';
 * abbreviateText( longText, { words: 5 } );
 * // => "The quick brown ... the fence"
 * ```
 *
 * @example Custom separator
 * Setting `options.separator` to `'[...]'` replaces the default separator; spaces are not added unless part of this custom separator string.
 * ```typescript
 * const longText = 'The quick brown fox jumps over the lazy dog and then jumps over the fence';
 * abbreviateText( longText, { words: 6, separator: '[...]' } );
 * // => "The quick brown[...]over the fence"
 * ```
 *
 * @example Custom separator with spaces
 * When `options.separator` includes spaces, like `' [...] '`, these spaces are present in the final abbreviated output.
 * ```typescript
 * const longText = 'The quick brown fox jumps over the lazy dog and then jumps over the fence';
 * abbreviateText( longText, { words: 6, separator: ' [...] ' } );
 * // => "The quick brown [...] over the fence"
 * ```
 *
 * @example Preserves complete tokens with default options
 * During abbreviation with default settings, any substrings formatted as `{{token}}` within the input text are kept complete and unbroken.
 * ```typescript
 * const textWithTokens = 'The quick brown fox jumps over the {{token:{{token}}: 2}}';
 * abbreviateText( textWithTokens );
 * // => "The quick brown ... over the {{token:{{token}}: 2}}"
 * ```
 *
 * @example Returns original string if short enough (words option)
 * If the input string's word count (e.g., 4 words) is not greater than the value of `options.words` (e.g., 4 or 5), the original string is returned unchanged.
 * ```typescript
 * const shortText = 'The quick brown fox'; // 4 words
 * abbreviateText( shortText, { words: 4 } );
 * // => "The quick brown fox"
 * abbreviateText( shortText, { words: 5 } );
 * // => "The quick brown fox"
 * ```
 *
 * @remarks
 * For a more exhaustive set of scenarios and edge cases, please refer to the unit tests located in `./index.test.ts`.
 */
export function abbreviateText(
	text: string = '',
	options?: AbbreviateTextOptions
): string {
	// Set default options merged with user-provided options
	const config: Required<AbbreviateTextOptions> = {
	  words: 6,
	  separator: ' ... ',
	  minLengthDiff: 1,
	  ...( options || {} ), // Ensure options isn't null/undefined
	};

	// Basic validation and early exit
	if ( typeof text !== 'string' || text === '' ) {
		return text;
	}

	// Ensure minLengthDiff is at least 1
	if ( config.minLengthDiff < 1 ) {
		config.minLengthDiff = 1;
	}

	// Ensure words count is non-negative
	if ( config.words < 0 ) {
		config.words = 0;
	}

	try {
		/*
		 * Regex Explained:
		 * \s+ : Match one or more whitespace characters.
		 * (?! ... ) : Negative lookahead - asserts that the following pattern does NOT match.
		 * [^\{\{]* : Match zero or more characters that are NOT the start of "{{".
		 * This is a simplification assuming tokens don't contain "{{".
		 * \}\} : Match the literal closing "}}".
		 *
		 * Overall: Split by whitespace, UNLESS that whitespace is followed by characters leading up to a "}}" without another "{{" in between.
		 * Aim: Prevent splitting within things like `{{some token}}`.
		 */
		const words: string[] = text.split( /\s+(?![^\{\{]*\}\})/ );
		const wordCount = words.length;

		// Check if abbreviation is needed based on total length and threshold
		if (
			wordCount <= config.words ||
			( wordCount - config.words < config.minLengthDiff )
		) {
			return text;
		}

		// If target word count is 0, return just the separator (or empty string if separator is empty)
		if ( config.words <= 0 ) {
			return config.separator.trim() ? config.separator : ''; // Avoid returning only spaces if separator is '   '
		}

		// Calculate words for start and end parts (asymmetric for odd numbers)
		const startWordCount: number = Math.ceil( config.words / 2 );
		const endWordCount: number = Math.floor( config.words / 2 );

		// Slice and join the parts
		// Ensure slice indices are valid (slice handles out of bounds gracefully)
		const startWords = words.slice( 0, startWordCount ).join( ' ' );
		// Use Math.max to prevent negative index in slice when endWordCount is 0
		const endWords = words.slice( Math.max( 0, wordCount - endWordCount ) ).join( ' ' );

		// Assemble the final string, handling cases where one part might be empty (though less likely with the improved count calculation unless config.words is 1)
		if ( startWords && endWords ) {
			return `${ startWords }${ config.separator }${ endWords }`;
		} else if ( startWords ) {
			// Only start words (e.g., config.words = 1)
			return `${ startWords }${ config.separator }`;
		} else if ( endWords ) {
			// Only end words (less likely scenario)
			return `${ config.separator }${ endWords }`;
		} else {
			// Should ideally not be reached if wordCount > config.words and config.words > 0
			// Return separator as a minimal indication of abbreviation if somehow start/end are empty.
			return config.separator.trim() ? config.separator : '';
		}
	} catch ( error: unknown ) {
		// Log with more context
		console.warn(
			`Text abbreviation failed for text starting with "${text.substring( 0, 30 )}..."`,
			error
		);

		// Return the original text if any error occurs during processing.
		return text;
	}
};

export type { AbbreviateTextOptions };