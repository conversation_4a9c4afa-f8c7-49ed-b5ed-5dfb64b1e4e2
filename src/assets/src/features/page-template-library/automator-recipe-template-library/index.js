// Import components
import './components';

// Utilities
import {
	uapRecipeTemplateLibraryConfig,
	uapRtlUrlFilterQueryManager,
	uapRtlCreateFilterEvent
} from './utilities';

// Styles
import './style.scss';

/**
 * Main Automator Recipe Template Library state handler.
 */
export default class AutomatorRecipeTemplateLibrary {

	/**
	 * Constructor for AutomatorRecipeTemplateLibrary.
	 *
	 * @return {void}
	 */
	constructor() {
		// Set the library configuration
		this.library = uapRecipeTemplateLibraryConfig;

		// Set the UI components
		this.components = {
			'search' : document.querySelector( 'uap-rtl-search' ),
			'categoryMenu' : document.querySelector( 'uap-rtl-category-menu' ),
			'integrationsMenu' : document.querySelector( 'uap-rtl-integration-menu' ),
			'gridDisplay' : document.querySelector( 'uap-rtl-grid-display' ),
		};

		// Handle base filter change events ( ready-to-use )
		document.addEventListener(
			'uap-rtl-base-filter-change',
			this._handleBaseFilterChangeEvent.bind( this )
		);

		// Register filtering event listeners on the components
		this._registerComponentFilterEventListeners();

		// Set a mobile breakpoint.
		this.mobileBreakpoint = 960;
		// Set the inital isMobile state.
		this.isMobile = false;
		// List of component keys that are responsive.
		this.responsiveComponents = [ 'categoryMenu', 'integrationsMenu' ];
		// Attach resize listener
		window.addEventListener( "resize", this._handleResize.bind( this ) );
		// Initialize the responsive components.
		this._handleResize();

		// Initialize the filter state on load.
		this._activeFilter();
	}

	/**
	 * Add filter event listeners to the components.
	 *
	 * @return {void}
	 */
	_registerComponentFilterEventListeners() {
		// Loop through each component and add event listeners
		Object.keys( this.components ).forEach( ( key ) => {
			const component = this.components[ key ];
			if ( component ) {
				// Add filter change event listener ( search, category, integration )
				component.addEventListener(
					'uap-rtl-filter-change',
					this._handleFilterChangeEvent.bind( this )
				);
			}
		} );
	}

	/**
	 * Handle the initial filter state.
	 *
	 * @return {void}
	 */
	_activeFilter() {

		const { activeFilter } = this.library;
		const { filter, type, page } = activeFilter;

		// Perform a search if set.
		if ( type === 'search' && this.components.search ) {
			this.components.search.handleSearch( filter );
			this._maybeScrollToPagedTemplates( page );
			return;
		}

		// Perform a category click if set.
		if ( type === 'category' && this.components.categoryMenu ) {
			this.components.categoryMenu.triggerCategoryClickBySlug( filter );
			this._maybeScrollToPagedTemplates( page );
			return;
		}

		// Perform an integration click if set.
		if ( type === 'integration' && this.components.integrationsMenu ) {
			this.components.integrationsMenu.triggerIntegrationClickBySlug( filter );
			this._maybeScrollToPagedTemplates( page );
			return;
		}

		// Dispatch the filter change event with all templates.
		const filterEvent = uapRtlCreateFilterEvent( 'all', 'all', this.library.templates, page );
		this._handleFilterChangeEvent( filterEvent );
		this._maybeScrollToPagedTemplates( page );
	}

	/**
	 * Maybe set the pagination item as active.
	 *
	 * @param {Number} page - The page number
	 */
	_maybeScrollToPagedTemplates( page ) {
		if ( page && page > 1 && this.components.gridDisplay ) {
			this.components.gridDisplay.scrollToPage = page;
		}
	}

	/**
	 * Handle updates to the base templates results.
	 *
	 * @param {Event} event - The base filter change event object
	 *
	 * @return {void}
	 */
	_handleBaseFilterChangeEvent( event ) {

		// Get filter details.
		const { baseFilters } = event.detail;
		// Set base results templates to all.
		let baseResults = this.library.templates;

		// Loop through the base filters and filter the results by the filter ids.
		Object.keys( baseFilters ).forEach( filterName => {
			const filter = baseFilters[filterName];
			if ( filter.active ) {
				baseResults = baseResults.filter( template => filter.ids.includes( template.id ) );
			}
		} );

		// Map the filtered templates to their IDs
		const baseResultIds = baseResults.map( template => template.id );

		// Populate the component filter data
		this._populateComponentFilterData( 'baseResults', baseResultIds );
	}

	/**
	 * Handle updates to the filter object ( Search, category and integration )
	 *
	 * @param {Event} event - The filter-change event object
	 *
	 * @return {void}
	 */
	_handleFilterChangeEvent( event ) {
		// Update the filter object
		this.filter = {
			...this.filter,
			...event.detail.filter
		};

		// Update the URL query params
		uapRtlUrlFilterQueryManager(
			this.filter.filter,
			this.filter.type,
			this.filter.page
		);

		// Populate the component filter data
		this._populateComponentFilterData( 'filter', this.filter );
	}

	/**
	 * Populate the filter data for each component.
	 *
	 * @param {String} filterProp - The filter property to populate
	 * @param {Object} data - The filter data
	 *
	 * @return {void}
	 */
	_populateComponentFilterData( filterProp, data ) {
		// Loop through each component and populate filter data.
		Object.keys( this.components ).forEach( ( key ) => {
			const component = this.components[ key ];
			if ( component ) {
				component[filterProp] = data;
			}
		} );
	}

	/**
	 * Handle the resize event.
	 *
	 * @return {void}
	 */
	_handleResize() {
		const isNowMobile = window.innerWidth <= this.mobileBreakpoint;
		if ( this.isMobile !== isNowMobile ) {
			this.isMobile = isNowMobile;
			this._updateResponsiveComponents();
		}
	}

	/**
	 * Update the responsive components with isMobile state.
	 */
	_updateResponsiveComponents() {
		this.responsiveComponents.forEach( ( key ) => {
			const component = this.components[ key ];
			if ( component ) {
				component.isMobile = this.isMobile;
			}
		} );
	}
}
