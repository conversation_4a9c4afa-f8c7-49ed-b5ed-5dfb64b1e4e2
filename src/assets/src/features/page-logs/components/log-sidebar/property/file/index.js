// Import dependencies
import { AutomatorSidebarPropertyChild } from '../_base';
import { unsafeCSS, css, html } from 'lit';
import { customElement } from 'lit/decorators.js';
import { repeat } from 'lit/directives/repeat.js';

// Import global utils
import { getFileIconByMime } from '@automator/shared/utils/get-file-icon-by-mime';

// Import escapeHTML
import { escapeHTML } from '@wordpress/escape-html';

// Import i18n functions
import { __ } from '@wordpress/i18n';

// Import style for web component
import styles from './web-component-style.scss?inline';

// eslint-disable-next-line require-jsdoc
@customElement( 'uap-log-sidebar-property-file' )
export class AutomatorLogSidebarPropertyFile extends AutomatorSidebarPropertyChild {
	// Define style
	static styles = css`${ unsafeCSS( styles ) }`;

	/**
	 * Returns the template to render the value of the property
	 *
	 * @return {TemplateResult} The value of the property
	 */
	get _templateValue() {
		// Check if it's empty
		if ( this.parsedValue.length === 0 ) {
			return html`
				<code class="property-value">
					<em class="property-value__empty">${ escapeHTML( __( '(empty)', 'uncanny-automator' ) ) }</em>
				</code>
			`;
		}

		return html`
			<div class="property-value">
				<div class="files">
					${ this._templateFiles }
				</div>
			</div>
		`;
	}

	/**
	 * Generates a repeated list of template files based on the parsed values.
	 *
	 * This getter method uses the `repeat` directive to iterate over the `parsedValue` array, creating a unique template for each attachment using the `_templateFile` method.
	 *
	 * @return {TemplateResult} A TemplateResult containing the repeated list of template files.
	 */
	get _templateFiles() {
		return repeat(
			this.parsedValue,

			/**
			 * Provides a unique key for each attachment.
			 *
			 * This function is used by the `repeat` directive to determine the unique key for each item in the list, which helps in efficiently updating the DOM when the list changes.
			 *
			 * @param {Object} attachment - The current attachment object in the iteration.
			 * @param {Number} rowIndex - The index of the current attachment in the `parsedValue` array.
			 * @return {Number} The unique key for the attachment, which in this case is the index.
			 */
			( attachment, rowIndex ) => rowIndex,

			/**
			 * Generates the template for an individual attachment.
			 *
			 * This function creates the template for each attachment by passing the attachment object to the `_templateFile` method.
			 *
			 * @param {Object} attachment - The current attachment object in the iteration.
			 * @return {TemplateResult} The template for the current attachment.
			 */
			attachment => this._templateFile( attachment )
		);
	}

	/**
	 * Returns the template to render the file
	 *
	 * @param {Object} attachment The file
	 * @return {TemplateResult} The file template
	 */
	_templateFile( attachment ) {
		return html`
			<div class="file">
				<div class="file-icon">
					${ this._templateFileIcon( attachment ) }
				</div>

				<div class="file-name">
					${ attachment.filename }
				</div>

				<div class="file-details">
					#${ attachment.id } | ${ attachment.filesizeHumanReadable }
				</div>

				<div class="file-actions">
					<uo-button
						href=${ attachment.url }
						target="_blank"
						size="extra-small"
						color="secondary"
					>
						${ escapeHTML( __( 'View file', 'uncanny-automator' ) ) } <uo-icon id="external-link"></uo-icon>
					</uo-button>
				</div>
			</div>
		`;
	}

	/**
	 * Template for the file icon.
	 *
	 * @param {Object} attachment - The attachment file object.
	 * @param {String} attachment.mime - The MIME type of the file attachment.
	 * @return {TemplateResult} The template for the file icon.
	 */
	_templateFileIcon( attachment ) {
		switch ( attachment.mime ) {
			case 'image/jpeg':
			case 'image/png':
			case 'image/gif':
			case 'image/webp':
			case 'image/svg+xml':
				return this._templateFileIconImage( attachment );

			default:
				return this._templateFileIconDefault( attachment );
		}
	}

	/**
	 * Template for image attachment icon.
	 *
	 * @param {Object} attachment - The file attachment object.
	 * @param {String} attachment.url - The URL of the file attachment.
	 * @param {String} attachment.alt - The alt text for the file attachment.
	 * @return {TemplateResult} The template for the image file icon
	 */
	_templateFileIconImage( attachment ) {
		return html`
			<img 
				src="${ attachment.url }" 
				alt="${ attachment.alt }" 
				class="file-icon__image-preview"
			/>
		`;
	}

	/**
	 * Template for default attachment icon.
	 *
	 * @param {Object} attachment - The attachment file object.
	 * @param {String} attachment.mime - The MIME type of the file attachment.
	 * @return {TemplateResult} The template for the default file icon
	 */
	_templateFileIconDefault( attachment ) {
		return html`
			<img 
				src="${ getFileIconByMime( attachment.mime ) }" 
				class="file-icon__file-type"
			/>
		`;
	}

	/**
	 * Tries to convert the value (string) into a valid array
	 *
	 * @return {Array} The parsed value
	 */
	get parsedValue() {
		try {
			return JSON.parse( this.value );
		} catch ( e ) {
			console.error( e );

			return [];
		}
	}
}
