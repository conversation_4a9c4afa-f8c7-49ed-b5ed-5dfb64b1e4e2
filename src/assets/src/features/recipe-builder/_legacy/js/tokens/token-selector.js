import $ from 'jquery';

// Import debounce
import debounce from 'debounce';

import Tokens from './index.js';

import { parseDynamicSentence } from '@automator/builder/_legacy/js/utilities';

import {
	openInNewTab,
	recipeSetRequiresUser
} from '../common/utilities';

import Modal from '../common/modal';

import Field from '../field';

// Import global utils
import { getTokenIDsFromString } from '@automator/shared/utils/get-token-id-from-string';
import { isDefined } from '@automator/shared/utils/is-defined';
import { addParametersUTM } from '@automator/shared/utils/add-parameters-utm-to-url';
import { isEmpty } from '@automator/shared/utils/is-empty';

// Import escapeHTML
import { escapeHTML } from '@wordpress/escape-html';

// Import i18n functions
import { __, _x, sprintf } from '@wordpress/i18n';

// Import recipe builder services
import { setRecipeStatus } from '@automator/builder/services';
import { getItem } from '@automator/builder/services/item';
import { isItemInsideUserLoop } from '@automator/builder/services/item/loop';

export default class TokenSelector {
	constructor( Field, options = {} ){
		// Check if the field supports tokens
		if ( Field.attributes.supportsTokens ){
			// Create different Tokens Selectors depending on the field type
			if ( Field.type == 'textarea' ){
				return new TokenSelectorTextarea( Field, options );
			}
			else {
				return new TokenSelectorInput( Field, options );
			}
		}
	}
}

class TokenSelectorInput extends Tokens {
	constructor( Field, options = {} ){
		super(
			Field.attributes.itemType,
			Field.type,
			options
		);

		// Save field
		this.Field = Field;

		// Save the Token Selector instance in the field
		this.Field.TokenSelector = this;

		// Check if the field is inside a user loop
		this.itemIsInsideUserLoop = isItemInsideUserLoop( this.Field.attributes.itemId || 0 );

		// Render the token selector
		this.render();

		// Set data
		this.setData();

		// CodeMirror methods
		this.Field.getField().on( 'ua-codemirror-ready', () => {
			// Get the CodeMirror editor
			this.getCodeMirrorEditor();

			// Render tokens right away, without the delay
			this.editor.setValue( this.editor.getValue() );
			this.renderTokens();

			// Handle requests to add tokens to the field
			this.handleAddTokens();

			// Render tokens in real time
			this.renderTokensInRealTime();
		}); 
	}

	render(){
		this.RenderTokenSelector = new RenderTokenSelector( this.Field, this.tokens );
	}

	setData(){
		this.data = {
			tokenRenderingDebounce: 200,
			// cachedCMWidgets: []
		}
	}

	getCodeMirrorEditor(){
		this.editor = this.Field.fieldCM;
	}

	handleAddTokens(){
		// Listen events that are trying to add a token
		this.Field.field
			.off( 'ua-add-token' )
			.on( 'ua-add-token', ( event, token ) => {
				// Check if it's a date or time field
				if ( [ 'date', 'time' ].includes( this.Field.attributes.fieldType ) ){
					// Remove the value of the field
					this.editor.setValue( '' );
				}

				// Add it to the editor
				this.insertTextAtCursor( `{{${ token.id }}}` );

				// Render tokens right away, without the delay
				this.renderTokens({
					ignoreRequiresUserModal: true
				});

				// Check if it's NOT a date or time field
				if ( ! [ 'date', 'time' ].includes( this.Field.attributes.fieldType ) ){
					// Focus the field again
					this.editor.focus();
				}

				 // Check if it's a date or time field
				if ( [ 'date', 'time' ].includes( this.Field.attributes.fieldType ) ){
					// Set value in the real field
					this.Field.setValue( `{{${ token.id }}}` );
				}
			});
	}

	destroy(){
		// Destroy wrapper
		this.RenderTokenSelector.destroy();
	}

	/**
	 * Insert content in the CM field at the current position
	 *
	 * @since 3.0
	 * 
	 * @param  String   text The content to be added
	 */
	insertTextAtCursor( text ){
		this.editor.doc.replaceRange( text, this.editor.doc.getCursor() );
	}

	renderTokensInRealTime(){
		// Render tokens on blur
		this.editor.on( 'blur', event => {
			// Render tokens
			this.renderTokens();
		} );

		// Render tokens on paste
		this.editor.on( 'paste', event => {
			setTimeout( () => {
				// Render tokens
				this.renderTokens();
			}, 100 );
		} );
	}

	/**
	 * Processes and renders tokens within the editor's content by replacing them with respective DOM nodes.
	 * 
	 * This method iterates over each unique token found in the editor's content, handles multiple instances of each token, and applies specific logic based on token characteristics such as requiring user data. It also attaches event listeners for interaction with the rendered tokens.
	 *
	 * @param {Object} options - Configuration options for rendering tokens.
	 * @param {Boolean} [options.ignoreRequiresUserModal=false] - Optional. Flag to ignore the requirement of displaying a user modal. Defaults to false.
	 */
	renderTokens( options ) {
		// Clear all the current widgets
		// this.data.cachedCMWidgets = [];

		// Get the options
		options = Object.assign( {
			ignoreRequiresUserModal: false,
		}, options );

		// Retrieve the editor's content
		const editorContent = this.editor.getValue();

		// Use getTokenIDsFromString to find tokens in the content
		const tokens = getTokenIDsFromString( editorContent );

		// A function to process a single token instance in the content
		const processTokenInstance = ( tokenId, startIndex ) => {
			const endIndex = startIndex + tokenId.length + 4; // +4 for the surrounding {{}}
	
			// Convert index positions to CodeMirror positions
			const startPos = this.editor.posFromIndex( startIndex );
			const endPos = this.editor.posFromIndex( endIndex );
	
			// Get the token HTML
			const $tokenNode = this.createTokenDOMNode( tokenId );
	
			// Create CodeMirror widget
			const widget = this.editor.markText(
				startPos,
				endPos,
				{
					replacedWith: $tokenNode
				}
			);

			// Check if this is an universal token
			const isUniversalToken = tokenId.startsWith( 'UT' );

			// Check if the token requires user data
			const tokenRequiresUser = ! isUniversalToken ?
				this.tokensThatRequireUser.includes( tokenId ) :
				this.tokensThatRequireUser.some( 
					tokenThatRequireUserData => {
						console.log( `Evaluating ${ tokenThatRequireUserData } against ${ tokenId }. Does it start with ${ tokenThatRequireUserData }? ${ tokenId.startsWith( tokenThatRequireUserData ) }` );
						
						return tokenId.startsWith( tokenThatRequireUserData );
					}
				);

			// Check if the modal is currently open
			const isModalOpen = document.querySelector( '.needs-user-data-modal' ) !== null;

			// Logic to check if the token requires user data or other conditions
			// Check if we need to show the user modal for this token by verifying:
			if (
				// 1. The item is not inside a user loop
				! this.itemIsInsideUserLoop &&
				// 2. This is an anonymous recipe
				UncannyAutomator._recipe.recipe_type === 'anonymous' &&
				// 3. The recipe doesn't require user data yet
				! UncannyAutomator.recipe.requiresUserData &&
				// 4. This token requires user data
				tokenRequiresUser &&
				// 5. Make sure we're not showing the modal already
				! isModalOpen
			) {
				this.tokenNeedsUserModal( $tokenNode, {
					removeToken: () => {
						const widgetPosition = widget.find();
						widget.clear();
						this.editor.replaceRange( '', widgetPosition.from, widgetPosition.to );
					}
				} );
			}

			// Add event listeners for token interaction, assuming $tokenNode creation is done
			if ( ! this.Field.attributes.isReadOnly ) {
				$tokenNode.addEventListener( 'click', () => {
					if ( ! $tokenNode.classList.contains( 'uap-token--cm-focused' ) ) {
						$tokenNode.classList.add('uap-token--cm-focused');

						const onFocusOut = () => {
							$tokenNode.classList.remove( 'uap-token--cm-focused' );
							document.removeEventListener( 'mouseup', onClickOutside );
							document.removeEventListener( 'copy', onCopy );
							document.removeEventListener( 'keydown', onDelete );
							document.removeEventListener( 'keydown', onNavigation );
						};

						const onClickOutside = ( event ) => {
							if (
								! $tokenNode.isEqualNode( event.target ) && ! $tokenNode.contains( event.target ) ) {
								onFocusOut();
							}
						};

						document.addEventListener( 'mouseup', onClickOutside );

						const onCopy = ( event ) => {
							onFocusOut();

							try {
								navigator.clipboard.writeText( `{{${tokenId}}}` );
							} catch (e) {
								// Log error if debug mode is enabled
								if ( UncannyAutomator._site.has_debug_enabled ) {
									console.warn(e);
								}
							}
						};
						document.addEventListener( 'copy', onCopy );

						const onDelete = ( event ) => {
							const key = event.keyCode || event.charCode;
							if ( key === 8 || key === 46 ) {
								const widgetPosition = widget.find();
								widget.clear();
								this.editor.replaceRange( '', widgetPosition.from, widgetPosition.to );
								onFocusOut();
							}
						};
						document.addEventListener('keydown', onDelete);

						const onNavigation = ( event ) => {
							const key = event.keyCode || event.charCode;
							if ( key === 37 || key === 39 ) {
								onFocusOut();
								const widgetPosition = widget.find();
								let position = (key === 37) ? widgetPosition.from : widgetPosition.to;
								this.editor.focus();
								this.editor.setCursor(position);
							}
						};
						document.addEventListener('keydown', onNavigation);
					}
				} );
			}
		};

		// Process each token type once, but look for multiple instances
		tokens.forEach( tokenId => {
			let contentToSearch = editorContent;
			let offset = 0;
	
			while ( contentToSearch.indexOf( `{{${tokenId}}}` ) !== -1 ) {
				const localIndex = contentToSearch.indexOf( `{{${tokenId}}}` );
				const startIndex = offset + localIndex;
	
				// Process this token instance
				processTokenInstance( tokenId, startIndex );
	
				// Update the search offset and content to search for the next instance
				offset += localIndex + tokenId.length + 4; // Adjust offset to after the current token
				contentToSearch = editorContent.substring( offset );
			}
		} );
	}

	/**
	 * Shows modal to tell the user that the token added needs user data
	 * 
	 * @param  {Object} token     The token added
	 * @param  {Object} callbacks Object with callbacks
	 */
	tokenNeedsUserModal( $tokenNode, callbacks = {} ){
		/**
		 * Create modal to ley the user know that
		 * they will need user data
		 */
		let modal;

		// Check if WP is ready
		if ( ! UncannyAutomatorBackend._site.automator.is_pro_active ){
			modal = new Modal({
				title:   `${ escapeHTML( __( 'We need some user data', 'uncanny-automator' ) ) } <span class="needs-user-data-modal-pro-badge">${ escapeHTML( __( 'Pro', 'uncanny-automator' ) ) }</span>`,
				content: `
					<div class="needs-user-data-modal">
						<div class="needs-user-data-modal__title">${ 
							sprintf( 
								/* translators: 1. Token ID */
								escapeHTML( __( 'The token %1$s outputs the data of a WordPress user.', 'uncanny-automator' ) ),
								$tokenNode.outerHTML
							)
						}</div>
						<div class="needs-user-data-modal__disclaimer">
							<p>
								<div class="description">${ sprintf( 
									/* translators: 1. Trademarked term. */
									escapeHTML( __( 'Because the token is associated with user data, it must be mapped to a new or existing user. This requires %1$s.', 'uncanny-automator' ) ), 
									'Uncanny Automator Pro' 
								) } <a href="${ addParametersUTM( 'https://automatorplugin.com/pricing/', { 'medium': 'select_an_item', 'campaign': 'update_to_pro', 'content': 'requires_user_data',
							}) }" target="_blank">${ escapeHTML( __( 'Upgrade now', 'uncanny-automator' ) ) } <uo-icon id="external-link"></uo-icon></a></div>
							</p>
							<p>
								<div class="description">${ escapeHTML( __( "If you don't want to upgrade now, you can add this token to a recipe that runs only for logged-in users.", 'uncanny-automator' ) ) } <a href="${ addParametersUTM( 'https://automatorplugin.com/knowledge-base/creating-a-recipe/#Recipe_types', { 'medium': 'select_an_item', 'content': 'requires_user_data-learn-more',
							}) }" target="_blank">${ escapeHTML( __( 'Learn more', 'uncanny-automator' ) ) } <uo-icon id="external-link"></uo-icon></a></div>
							</p>
						</div>
					</div>
				`,
				buttons: {
					cancel:  escapeHTML( __( 'Remove token', 'uncanny-automator' ) ),
					confirm: escapeHTML( __( 'Upgrade to Pro', 'uncanny-automator' ) ),
				}
			}, {
				size: 'large'
			});

			modal.setEvents({
				onConfirm: () => {
					// Close and destroy modal
					modal.destroy();

					// Open link in new tab
					openInNewTab( addParametersUTM( 'https://automatorplugin.com/pricing/', { 'medium': 'add_token', 'campaign': 'update_to_pro', 'content': 'requires_user_data',
							}) );

					// Remove token
					if ( isDefined( callbacks.removeToken ) ){
						callbacks.removeToken();
					}

					// Can't add, confirm
					if ( isDefined( callbacks.forbiddenConfirm ) ){
						callbacks.forbiddenConfirm();
					}
				},
				onCancel: () => {
					// Close and destroy modal
					modal.destroy();

					// Remove token
					if ( isDefined( callbacks.removeToken ) ){
						callbacks.removeToken();
					}

					// Can't add, cancel
					if ( isDefined( callbacks.forbiddenCancel ) ){
						callbacks.forbiddenCancel();
					}
				},
			});
		}
		else {
			// Check if we should set the recipe to draft when the user adds the action
			// We're doing this because the user will need to fill the User Selector
			const actionWillSetRecipeToDraft = UncannyAutomator._recipe.is_recipe_on;

			modal = new Modal({
				title:   `${ escapeHTML( __( 'We need some user data', 'uncanny-automator' ) ) } <span class="needs-user-data-modal-pro-badge needs-user-data-modal-pro-badge--gray">${ escapeHTML( __( 'Pro', 'uncanny-automator' ) ) }</span>`,
				content: `
					<div class="needs-user-data-modal">
						<div class="needs-user-data-modal__title">${
							sprintf( 
								/* translators: 1. Token ID */
								escapeHTML( __( 'The token %1$s outputs the data of a WordPress user.', 'uncanny-automator' ) ),
								$tokenNode.outerHTML
							)
						}</div>
						<div class="needs-user-data-modal__disclaimer">
							<div class="description">${ escapeHTML( __( "Since this is a recipe that runs for everyone, including logged-out users, you'll need to select a new or existing user that this data will come from.", 'uncanny-automator' ) ) }</div>
						</div>
					</div>
				`,
				buttons: {
					cancel:  escapeHTML( __( 'Remove token', 'uncanny-automator' ) ),
					confirm: escapeHTML( __( 'Set user data', 'uncanny-automator' ) ),
				},
			}, {
				size: 'large',
			});

			modal.setEvents({
				onConfirm: () => {
					let modalElements = modal.getElements();

					// Add loading class to button
					modalElements.modal.box.buttons.confirm.addClass( 'uap-btn--loading' ).prop( 'disabled', true );

					// Do an AJAX call to set the global recipe requires user
					recipeSetRequiresUser(
						// Set to true (requires user)
						true,
						// On success
						( response ) => {
							// On success, close and destroy the modal
							modal.destroy();

							// Render the tokens again, some might be missing
							this.renderTokens();
						},
						// On fail
						( response ) => {
							// Remove loading class to button
							modalElements.modal.box.buttons.confirm.removeClass( 'uap-btn--loading' ).prop( 'disabled', false );
						}
					);

					// Do a call to set the recipe to draft
					if ( actionWillSetRecipeToDraft ){
						setRecipeStatus( false, false );
					}

					// Can add, confirm
					if ( isDefined( callbacks.allowedConfirm ) ){
						callbacks.allowedConfirm();
					}
				},
				onCancel: () => {
					// Close and destroy modal
					modal.destroy();

					// Remove token
					if ( isDefined( callbacks.removeToken ) ){
						callbacks.removeToken();
					}

					 // Can add, confirm
					if ( isDefined( callbacks.allowedCancel ) ){
						callbacks.allowedCancel();
					}
				},
			});
		}
	}
}

class TokenSelectorTextarea extends TokenSelectorInput {
	constructor( Field, options = {} ){
		super( Field, options );

		// Check if it supports TinyMCE
		if ( this.Field.attributes.supportsTinyMCE ){
			// TinyMCE methods
			this.Field.getField().on( 'ua-tinymce-ready', () => {
				// Get the CodeMirror editor
				this.getTinyMCEEditor();

				// Add styles to the editor
				this.addStylesToEditor();

				// Parse tokens in the TinyMCE editor
				setTimeout(() => {
					this.parsePlainTokensInTinyMCE({
						parseTokensInAttributes: false
					});
				}, this.data.tokenRenderingDebounce );

				// Parse tokens in the text editor
				setTimeout(() => {
					this.parseHTMLTokensInPlainTextarea();
				}, this.data.tokenRenderingDebounce );
				
				// Handle requests to add tokens to the field
				this.handleAddTokens();

				// Handle tokens added to the textarea (HTML)
				this.handleHTMLTokensInPlainTextarea();

				// Handle tokens added to TinyMCE (plain)
				this.handlePlainTokensInTinyMCE();
			});
		}
	}

	setData(){
		this.data = {
			tokenRenderingDebounce: 200,
			tokenCSSClass: 'uap-token-tinymce',
			// Regular expression (lazy) used to find tokens in the raw TinyMCE content
			tinymceTokenRegex: new RegExp( /\<ins contenteditable\="false"\>\<\!\-\-UAToken\-\-\>(?:.*?)data\-token\-id\="(.*?)"(?:.*?)\<\!\-\-UATokenEnd\-\-\>\<\/ins\>/gm ),
		}

		// 1. Token HTML, 2. Token ID
		this.data.tinymceTokenTemplate = `<ins contenteditable="false"><!--UAToken--><span class="${ this.data.tokenCSSClass }" contenteditable="false" data-token-id="%2$s">%1$s</span><!--UATokenEnd--></ins>`;
	}

	render(){
		this.RenderTokenSelector = new RenderTokenSelector( this.Field, this.tokens );
	}

	insertTextAtCursor( token = '' ){
		// Check if "Text" view is selected
		// Insert using CodeMirror methods
		if (
			// Doesn't have TinyMCE
			! isDefined( this.tinyMCEeditor )
			// It has TinyMCE, but it's hidden
			|| isDefined( this.tinyMCEeditor ) && this.tinyMCEeditor.hidden
		){
			// Insert content in the CM field at the current position
			this.editor.doc.replaceRange( token, this.editor.doc.getCursor() );
		}
		// Insert using TinyMCE methods
		else {
			// Token without brackets
			// token = token.replace( '{{', '' ).replace( '}}', '' );

			// Remove first two characters and last two characters (remove {{ }})
			token = token.substring( 2, token.length - 2 );

			// Get the token HMTL
			const $tokenNode = this.createTokenDOMNode( token );

			// Check if this is an universal token
			const isUniversalToken = token.startsWith( 'UT' );

			// Check if the token requires user data
			const tokenRequiresUser = ! isUniversalToken ?
				this.tokensThatRequireUser.includes( token ) :
				this.tokensThatRequireUser.some( 
					tokenThatRequireUserData => token.startsWith( tokenThatRequireUserData ) 
				);

			// Check if we need to show the user modal for this token by verifying:
			if (
				// 1. The item is not inside a user loop
				( ! this.itemIsInsideUserLoop ) && 
				// 2. This is an anonymous recipe
				UncannyAutomator._recipe.recipe_type == 'anonymous' &&
				// 3. The recipe doesn't require user data yet
				! UncannyAutomator.recipe.requiresUserData &&
				// 4. This token requires user data
				tokenRequiresUser
			){
				this.tokenNeedsUserModal( $tokenNode, {
					allowedConfirm: () => {
						// Insert token
						this.tinyMCEeditor.execCommand( 'mceInsertContent', false, sprintf( this.data.tinymceTokenTemplate, $tokenNode.outerHTML, token ) );

						// Trigger a change event in the field
						this.Field.getField()
							.trigger( 'change input' );
						}
				});
			}
			else {
				// Insert token
				this.tinyMCEeditor.execCommand( 'mceInsertContent', false, sprintf( this.data.tinymceTokenTemplate, $tokenNode.outerHTML, token ) );

				// Trigger a change event in the field
				this.Field.getField()
					.trigger( 'change input' );
			} 
		}
	}

	getTinyMCEEditor(){
		this.tinyMCEeditor = this.Field.fieldTinyMCE;
	}

	addStylesToEditor(){
		this.tinyMCEeditor.dom.addStyle( `
			html body {
				font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
				font-size: 15px;
				margin-left: 12px;
				margin-right: 12px;
			}

			ins {
				text-decoration: none !important;
			}
		` );
	}

	handleHTMLTokensInPlainTextarea(){
		// Listen changes to the field
		this.Field.getField()
			.on( 'change input',
				debounce(() => {
					// Parse tokens
					this.parseHTMLTokensInPlainTextarea();
				}, this.data.tokenRenderingDebounce )
			);

		// Trigger a change event in the text field 
		// when the user changes to the "Text" tab
		this.Field.getField()
			.on( 'ua-tinymce-activate-text-view', () => {
				// Parse tokens
				setTimeout(() => {
					this.parseHTMLTokensInPlainTextarea();
				}, this.data.tokenRenderingDebounce );
			});
	}

	handlePlainTokensInTinyMCE(){
		// Trigger a change event in the text field 
		// when the user changes to the "Text" tab
		this.Field.getField()
			.on( 'ua-tinymce-activate-visual-view', () => {
				// Parse tokens in the editor
				setTimeout(() => {
					this.parsePlainTokensInTinyMCE({
						parseTokensInAttributes: false
					});
				}, this.data.tokenRenderingDebounce );
			});
	}

	parsePlainTokensInTinyMCE( options = {} ){
		// Get options
		options = Object.assign( {
			parseTokensInAttributes: true
		}, options );

		// Get the content of the TinyMCE field
		const editorContent = this.tinyMCEeditor.getContent({
			format: 'raw'
		});

		// Render the tokens
		const editorContentWithTokens = this.renderTextWithTokens(
			editorContent,
			{
				// 1. Token HTML, 2. Token ID
				template: this.data.tinymceTokenTemplate,
				parseTokensInAttributes: options.parseTokensInAttributes,
				returnString: true
			}
		);

		// Check if the content is different
		if ( editorContent != editorContentWithTokens ){
			// Set the content again
			this.tinyMCEeditor.setContent( editorContentWithTokens );
		}   
	}

	parseHTMLTokensInPlainTextarea(){
		// Get the content of the field
		let content = this.Field.getField().val();

		// Create a variable to check later if the content changed
		let contentChanged = false;

		// Convert TinyMCE tokens to real tokens
		content = content.replaceAll( this.data.tinymceTokenRegex, ( match, tokenID ) => {
			// If this code is reached, it means the content changed
			contentChanged = true;

			// Replace it with a valid token
			return `{{${ tokenID }}}`;
		} );

		// ⤵️ If the content hasn't changed, stop here
		if ( ! contentChanged ) {
			return;
		}

		// Set the value of the normal field
		// But don't trigger the change event
		try {
			this.editor.setValue( content );
		} catch ( e ){
			this.Field.getField().val( content );
		} 
	}

	destroy(){
		this.RenderTokenSelector.destroy();
	}
}

class RenderTokenSelector {
	constructor( Field, tokens ){
		this.Field  = Field;
		this.tokens = tokens;

		// If we're rendering this token selector for an item, get the item information
		this.item = isDefined( this.Field.attributes.itemId ) ? getItem( this.Field.attributes.itemType, this.Field.attributes.itemId ) : null;

		// Check if the item is inside a user loop
		this.itemIsInsideUserLoop = isItemInsideUserLoop( this.Field.attributes.itemId || 0 );

		/**
		 * Save order of groups
		 */
		this.tokens = this.tokens.map( ( tokenGroup, index ) => {
			tokenGroup.order = index;

			return tokenGroup;
		} );

		// Get data
		this.getData();

		// Get elements
		this.getElements();

		// Create wrapper
		this.createWrapper();

		// Create filter
		this.createFilter();

		// Create groups of tokens
		this.createGroupsOfTokens();

		// Open tokens on click
		this.listenToggleButton();

		// Resize dropdown on window resize
		this.resizeDropdownOnWindowResize();

		// Resize on scroll
		this.resizeDropdownOnWindowScroll();

		// Refresh when the field value changes
		this.refreshOnFieldChange();

		// Search
		this.search();

		// Set default filter
		if ( this.shouldSupportFilters() ){
			this.setFilter( true );
		}
	}

	getData(){
		this.data = {
			isOpen:   false,
			position: 'below'
		}
	}

	getElements(){
		this.$elements = {
			wrapper: null,
			toggleButton: null,
			searchField: null,
			dropdown: null,
			allGroups: null,
			dropdownGroupsContainer: null,
			dropdownTokens: null,
			noResultsOption: null,
			seperators: null,
		};
	}

	createWrapper(){
		const $wrapper = $( `
			<div class="form-variable">
				<div class="form-variable__element">
					<div class="form-variable__render-container"></div>
					<div class="form-variable__toggle">
						<uo-icon id="asterisk"></uo-icon>
					</div>
				</div>
				<div class="form-variable__dropdown">
					<div class="form-variable__dropdown-container">
						<div class="form-variable__search">
							<input 
								class="form-variable__search-input" 
								placeholder="${ escapeHTML( __( 'Search tokens', 'uncanny-automator' ) ) }"
							>
						</div>
						<div class="form-variable__groups"></div>
					</div>
				</div>
			</div>
		` );

		// Get the fields that we have to move
		const $fieldsToMove = [];

		// Check if it's a textarea
		if ( this.Field.type == 'textarea' ){
			$fieldsToMove.push( this.Field.getField() );
		}

		// Check if the CodeMirror field is defined
		if ( isDefined( this.Field.getFieldCM() ) ){
			$fieldsToMove.push( this.Field.getFieldCM() );
		}

		// Move the fields
		$fieldsToMove.forEach(( $field ) => {
			// Move the field
			$wrapper
				.insertAfter( $field );

			$wrapper
				.find( '.form-variable__render-container' )
				.append( $field );
		});

		// Save the element
		this.$elements.wrapper = $wrapper;

		// Save the element that has the field inside
		this.$elements.renderContainer = $wrapper
			.find( '.form-variable__render-container' );

		// Save the search field container
		this.$elements.searchFieldContainer = $wrapper.find( '.form-variable__search' );

		// Save the search field
		this.$elements.searchField = $wrapper.find( '.form-variable__search-input' );

		// Save the toggle button
		this.$elements.toggleButton = $wrapper.find( '.form-variable__toggle' );

		// Save the dropdown
		this.$elements.dropdown = $wrapper.find( '.form-variable__dropdown' );

		// Save the container of the groups
		this.$elements.dropdownGroupsContainer = $wrapper.find( '.form-variable__groups' );
	}

	createFilter(){
		// Get the token type strings
		const tokenTypeStrings = {
			text:  escapeHTML( _x( 'Text', 'Token', 'uncanny-automator' ) ),
			email: escapeHTML( _x( 'Email', 'Token', 'uncanny-automator' ) ),
			url:   escapeHTML( _x( 'URL', 'Token', 'uncanny-automator' ) ),
			float: escapeHTML( _x( 'Float', 'Token', 'uncanny-automator' ) ),
			int:   escapeHTML( _x( 'Integer', 'Token', 'uncanny-automator' ) ),
			date:  escapeHTML( _x( 'Date', 'Token', 'uncanny-automator' ) ),
			time:  escapeHTML( _x( 'Time', 'Token', 'uncanny-automator' ) ),
		}

		// Check if it's a field that should filter tokens
		if ( this.shouldSupportFilters() ){
			// Get the field type
			const fieldType = this.Field.attributes.fieldType;

			// Get the field type human-readable name
			const fieldTypeHumanReadable = isDefined( tokenTypeStrings[ fieldType ] ) 
				? tokenTypeStrings[ fieldType ] 
				: fieldType;

			// Create filter container
			const $filterContainer = $( `
				<div class="form-variable__filter"></div>
			` );

			// Create checkbox
			const filterToggle = new Field( 'checkbox', {
				optionCode:   'token__enableFilter',
				currentValue: true,
				label: `<span class="form-variable__filter-label">` + sprintf( 
					/* translators: 1. Token type */
					escapeHTML( __( 'Only %1$s tokens', 'uncanny-automator' ) ), 
					`<span class="form-variable__filter-type">${ fieldTypeHumanReadable }</span>` 
				) + `<span>`,
			} );

			// Get checkbox form element
			const $filterFormElement = filterToggle.createFormElement();

			// Add the field
			$filterContainer.prepend( $filterFormElement );

			// Add filter to the DOM
			this.$elements.searchFieldContainer.append( $filterContainer );

			// Save the filter container
			this.$elements.filterContainer = $filterContainer;

			// Listen changes to the checkbox value
			$filterFormElement.on( 'automator:field:change', () => {
				// Get value
				const filterValue = filterToggle.getValue();

				// Check if we have to filter or remove the filter
				if ( filterValue ){
					this.setFilter( true );
				}
				else {
					this.setFilter( false );
				}
			});
		}    
	}

	createGroupsOfTokens(){
		// Get the container
		const $container = this.$elements.wrapper.find( '.form-variable__groups' );

		// Get all tokens but the ones coming from actions
		const nonItemTokens  = this.tokens.filter( tokenGroup => ! isDefined( tokenGroup.itemType ) );
		const triggersTokens = this.tokens.filter( tokenGroup => tokenGroup.itemType === 'trigger' );
		const actionTokens   = this.tokens.filter( tokenGroup => tokenGroup.itemType === 'action' );

		nonItemTokens.forEach(( groupOfTokens, groupKey ) => {
			$container.append( this.createIndividualGroupOfTokens( groupKey, groupOfTokens ) );
		});

		// Check if it has trigger tokens
		if ( triggersTokens.length > 0 ) {
			$container.get(0).insertAdjacentHTML( 'beforeEnd', `
				<uo-alert 
					size="xsmall" 
					type="info" 
					custom-icon 
					no-radius

					class="form-variable__group-item-separator form-variable__group-item-separator--triggers"
				>
					${ escapeHTML( __( 'Triggers', 'uncanny-automator' ) ) }
				</uo-alert>
			` );

			triggersTokens.forEach(( groupOfTokens, groupKey ) => {
				$container.append( this.createIndividualGroupOfTokens( groupKey, groupOfTokens ) );
			});
		}

		if ( actionTokens.length > 0 ) {
			$container.get(0).insertAdjacentHTML( 'beforeEnd', `
				<uo-alert 
					size="xsmall" 
					type="info" 
					custom-icon 
					no-radius

					class="form-variable__group-item-separator form-variable__group-item-separator--actions"
				>
					${ escapeHTML( __( 'Actions', 'uncanny-automator' ) ) }
				</uo-alert>
			` );

			actionTokens.forEach(( groupOfTokens, groupKey ) => {
				$container.append( this.createIndividualGroupOfTokens( groupKey, groupOfTokens ) );
			});
		}

		// Save all the groups
		this.$elements.allGroups = $container.find( '.form-variable__group' );

		// Save all the items
		this.$elements.dropdownTokens = $container.find( '.form-variable__group-item' );

		// Get separators
		this.$elements.separators = {
			triggers: $container.get(0).querySelector( '.form-variable__group-item-separator--triggers' ),
			actions:  $container.get(0).querySelector( '.form-variable__group-item-separator--actions' ),
		};

		// Add the "no results" option
		this.$elements.noResultsOption = $( `
			<div class="form-variable__group-item-no-results form-variable__group-item-no-results--hide">
				<div class="form-variable__group-item-no-results-title">
					${ escapeHTML( __( 'No tokens found', 'uncanny-automator' ) ) }
				</div>
				<div class="form-variable__group-item-no-results-description">
					${ this.shouldSupportFilters() ? escapeHTML( __( 'Try searching again or disabling the token type filter.', 'uncanny-automator' ) ) : escapeHTML( __( 'Try searching again.', 'uncanny-automator' ) ) }
				</div>
			</div>
		` );
		$container.append( this.$elements.noResultsOption );
	}

	createIndividualGroupOfTokens( groupKey, groupOfTokens ){
		const $group = $( `
			<div 
				class="form-variable__group"
				data-id="${ groupOfTokens.id }"
			>
				<div class="form-variable__group-source">
					<div class="form-variable-group-source__icon"></div>
					<div class="form-variable-group-source__description">
						<div class="form-variable-group-source__title"></div>
						<div class="form-variable-group-source__tags"></div>
					</div>
					<div class="form-variable-group-source__toggle">
						<span class="form-variable-group-source__toggle-icon"></span>
					</div>
				</div>
				<span class="form-variable__group-children"></span>
			</div>
		` );

		// Add the name
		const groupName = parseDynamicSentence( {
			itemID: this.Field.attributes.itemId,
			dynamicSentence: groupOfTokens.name,
			fields: isDefined( UncannyAutomator.recipe.items[ groupOfTokens.itemID ] ) ? UncannyAutomator.recipe.items[ groupOfTokens.itemID ].meta : {},
			abbreviateFields: true
		} );

		$group
			.find( '.form-variable-group-source__title' )
			.append( groupName );

		// Create the icon
		let $icon = groupOfTokens.icon.cloneNode();

		// Add the icon to the group
		$group.find( '.form-variable-group-source__icon' ).append( $icon );

		// Get the toggle button
		const $toggleButton = $group.find('.form-variable__group-source');

		// Function to send custom event based on the group's state
		const sendGroupToggleEvent = ( isOpen ) => {
			const eventName = isOpen 
				? 'uap/builder/legacy/field/token-selector/group/close' 
				: 'uap/builder/legacy/field/token-selector/group/open';

			// Send custom event to the document
			document.dispatchEvent( new CustomEvent( eventName, {
				detail: {
					groupCode: groupOfTokens.id
				}
			} ) );
		};

		// Listen for clicks on the toggle button
		$toggleButton.on( 'click', () => {
			const isOpen = $group.hasClass( 'form-variable__group--open' );
			
			// Send the appropriate custom event
			sendGroupToggleEvent( isOpen );
			
			// Toggle the open class on the group
			$group.toggleClass( 'form-variable__group--open' );
		} );

		// Check if it's an action that's delayed, scheduled or inside a conditions block
		if ( 
			isDefined( groupOfTokens.itemID ) && 
			isDefined( UncannyAutomator.recipe.items[ groupOfTokens.itemID ] )
		) {
			if (
				UncannyAutomator.recipe.items[ groupOfTokens.itemID ].itemType === 'trigger'
			) {
				// Add class to get these groups later
				$group.get(0).classList.add( 'form-variable__group--trigger' );
			}

			if ( 
				UncannyAutomator.recipe.items[ groupOfTokens.itemID ].itemType === 'action'
			) {
				// Get the wrapper of the tags
				const $tagsWrapper = $group.get(0).querySelector( '.form-variable-group-source__tags' );

				// Get the itme of the FIELD. This is where we're adding the token
				const fieldItemID = this.Field.attributes.itemId;

				// Get action of the TOKEN. This is where the token is coming from
				const tokenAction = UncannyAutomator.recipe.items[ groupOfTokens.itemID ];

				// Add class to get these groups later
				$group.get(0).classList.add( 'form-variable__group--action' );

				if ( tokenAction.hasConditions ) {
					$group.get(0).classList.add( 'form-variable__group--action-conditions' );

					$tagsWrapper.insertAdjacentHTML( 'beforeEnd', `
						<uo-tooltip>
							${ escapeHTML( __( "These tokens may be empty if the associated action's filter conditions are not met.", 'uncanny-automator' ) ) }

							<uo-chip
								size="small"
								color="warning"
								filled

								slot="target"
							>
								<uo-icon id="code-merge"></uo-icon> ${ escapeHTML( __( 'Filtered', 'uncanny-automator' ) ) }
							</uo-chip>
						</uo-tooltip>
					` );
				}

				// Check if we're adding this token to an action
				if (
					isDefined( fieldItemID ) && 
					isDefined( UncannyAutomator.recipe.items[ fieldItemID ] ) && 
					UncannyAutomator.recipe.items[ fieldItemID ].itemType === 'action'
				) {
					// Get the field action
					const fieldItem = UncannyAutomator.recipe.items[ fieldItemID ];

					// Check if the action where the token is coming from is scheduled, BUT THE DESTINATION action is not
					// This means the token MIGHT not be available
					if ( tokenAction.isScheduled && ! fieldItem.isScheduled ) {
						$group.get(0).classList.add( 'form-variable__group--action-scheduled' );

						$tagsWrapper.insertAdjacentHTML( 'beforeEnd', `
							<uo-tooltip>
								${ escapeHTML( __( 'These tokens may be empty if the associated action has not yet run when this action runs.', 'uncanny-automator' ) ) }

								<uo-chip
									size="small"
									color="warning"
									filled

									slot="target"
								>
									<uo-icon id="clock"></uo-icon> ${ escapeHTML( __( 'Scheduled', 'uncanny-automator' ) ) }
								</uo-chip>
							</uo-tooltip>
						` );
					}
					
					if ( ( tokenAction.isDelayed && ! fieldItem.isDelayed ) || ( tokenAction.isDelayedCustom && ! fieldItem.isDelayedCustom ) ) {
						$group.get(0).classList.add( 'form-variable__group--action-delayed' );

						$tagsWrapper.insertAdjacentHTML( 'beforeEnd', `
							<uo-tooltip>
								${ escapeHTML( __( 'These tokens may be empty if the associated action has not yet run when this action runs.', 'uncanny-automator' ) ) }

								<uo-chip
									size="small"
									color="warning"
									filled

									slot="target"
								>
									<uo-icon id="clock"></uo-icon> ${ escapeHTML( __( 'Delayed', 'uncanny-automator' ) ) }
								</uo-chip>
							</uo-tooltip>
						` );
					}
				}
			}
		}

		// Get the container where we're going to add the tokens
		const $tokensListContainer = $group.find( '.form-variable__group-children' );

		// Add tokens
		groupOfTokens.tokens.forEach(( token ) => {
			// Check if the token is deprecated
			if ( isDefined( token.deprecated ) && token.deprecated ) {
				return false;
			}

			let tokenName = ! isEmpty( token.name ) ? token.name : '';

			if ( ! token.id ) {
				console.error( 'Pay attention to the token below' );
			}

			// Check if the token requires user data
			const tokenRequiresUserData = // Not inside a user loop
				( ! this.itemIsInsideUserLoop ) && 
				// Recipe is anonymous
				UncannyAutomator._recipe.recipe_type == 'anonymous' && 
				// Recipe doesn't already require user data
				! UncannyAutomator.recipe.requiresUserData && 
				// Token has the requiresUser property
				isDefined( token.requiresUser ) && 
				// Token requires user data
				token.requiresUser;

			// Create the token
			const $token = $( `
				<div 
					class="form-variable__group-item"
					data-search="${
						tokenName
							.trim()
							.toLowerCase()
							.replace( /\s/g, '' )
					}"
					data-type="${ token.type }"
					data-token-id="${ token.id }"
				>
					<span>${ tokenName }</span>

					${
						tokenRequiresUserData
							// Show the tag
							? '<span class="form-variable__group-item-tag">' + escapeHTML( __( 'Needs user data', 'uncanny-automator' ) ) + '</span>' 
							// Otherwise show nothing
							: '' 
					}
				</div>
			` );

			/**
			 * Callback invoked to add the token to the field
			 * 
			 * @return {undefined}
			 */
			const addTokenToField = ( token ) => {
				// Check if we have to clear the field before adding the token
				if ( 
					isDefined( this.Field.attributes.clearWhenAddingToken ) && 
					this.Field.attributes.clearWhenAddingToken
				) {
					// Don't trigger the change event
					this.Field.setValue( '', false );
				}

				// Trigger event to add the token
				this.Field.field.trigger( 'ua-add-token', token );

				// Send custom event to the document
				document.dispatchEvent( new CustomEvent( 'uap/builder/legacy/field/token-selector/token/add', {
					detail: {
						field: this.Field,
						$field: this.Field.field,
						tokenCode: token.id
					}
				} ) );
			}

			// Listen clicks to the tokens
			$token.on( 'click', () => {
				// Close the token selector
				this.close( {
					triggeredBy: 'token-added'
				} );

				// Check if the beforeAdded callback is defined for this token
				if ( isDefined( token.beforeAdded ) ) {
					// Invoke custom method
					token.beforeAdded( addTokenToField, this.Field );
				} else {
					addTokenToField( token );
				}
			});

			// Add token
			$tokensListContainer.append( $token );
		});

		this.tokens[ groupOfTokens.order ].$toggleButton = $toggleButton;

		return $group;
	}

	addAllTokensInGroupCallback( groupOfTokens ) {
		// Create the content for the modal
		const $modalContent = $( `<div></div>` );

		// Create the "Include name" field
		const includeTokenNameField = new Field( 'checkbox', {
			label: escapeHTML( __( 'Include token name', 'uncanny-automator' ) ),
			currentValue: true
		});
		// Add the field
		$modalContent.append( includeTokenNameField.createFormElement() );

		// Create the "Include token ID" field
		const includeTokenIDField = new Field( 'checkbox', {
			label: escapeHTML( __( 'Include token ID', 'uncanny-automator' ) ),
			currentValue: false
		});
		// Add the field
		$modalContent.append( includeTokenIDField.createFormElement() );

		// Add description
		const groupName = groupOfTokens.name instanceof jQuery ? groupOfTokens.name.text() : groupOfTokens.name;

		const $taskDescription = $( `<div class="description">${
			sprintf(
				/* translators: 1. Group name 2. Field name */
				escapeHTML( __( 'On confirmation, all the tokens in the group "%1$s" will be added to the field  "%2$s".', 'uncanny-automator' ) ),
				groupName,
				! isEmpty( this.Field.attributes.label ) ? this.Field.attributes.label : escapeHTML( __( '(no label)', 'uncanny-automator' ) )
			)
		}</div>` );
		$modalContent.append( $taskDescription );

		// Show a modal
		const modal = new Modal({
			title: escapeHTML( __( 'Add tokens', 'uncanny-automator' ) ),
			content: $modalContent,
			buttons: {
				cancel:  escapeHTML( _x( 'Cancel', 'Verb', 'uncanny-automator' ) ),
				confirm: escapeHTML( __( 'Add tokens', 'uncanny-automator' ) ),
			}
		});

		modal.setEvents({
			onConfirm: () => {
				// Try to add all tokens
				try {
					// Close the token selector
					this.close( {
						triggeredBy: 'token-added'
					} );

					// Get the value of the fields
					const hasToIncludeName = includeTokenNameField.getValue(),
					      hasToIncludeID   = includeTokenIDField.getValue();

					// Get the current value of the field
					const currentFieldValue = this.Field.getValue();

					// Create string where we're going to save the output of the tokens
					let tokenContent = '';

					// Create the matrix with the tokens
					const tokensMatrix = [];

					// Iterate tokens
					groupOfTokens.tokens.forEach( token => {
						// Check if the token is deprecated
						if ( token?.deprecated ) {
							return false;
						}

						// Create the token data
						const tokenData = {
							name: token.name,
							id: token.id,
							content: `{{${ token.id }}}`
						};

						tokensMatrix.push( tokenData );
					} );

					tokenContent = this.tokensLogging( tokensMatrix, {
						fieldType: this.Field.type,
						richText: this.Field.type == 'textarea' && isDefined( this.Field.fieldTinyMCE ),
						includeName: hasToIncludeName,
						includeID: hasToIncludeID
					} );

					// Add it to the current token of the field
					// This behavior changes if we're adding it to a textarea with TinyMCE
					// Check if it's a TinyMCE textarea with an HTML template
					if (
						this.Field.type == 'textarea' && 
						isDefined( this.Field.fieldTinyMCE ) && 
						currentFieldValue.includes( '</body>' )
					) {
						try {
							// Add it before the closing body tag
							this.Field.setValue( currentFieldValue.replace( '</body>', `${ tokenContent }</body>` ) );
						} catch ( e ) {
							// It's a normal field or a plain-text textarea
							// Add it at the end
							this.Field.setValue( `${ currentFieldValue } ${ tokenContent }` );
						}
					} else {
						// It's a normal field or a plain-text textarea
						// Add it at the end
						this.Field.setValue( `${ currentFieldValue } ${ tokenContent }` );
					}

					// Check if it's a textarea using the "Visual" view
					if ( this.Field.type == 'textarea' && isDefined( this.Field.fieldTinyMCE ) && ! this.Field.fieldTinyMCE.hidden ) {
						// Trigger change to render tokens
						this.Field.getField().trigger( 'ua-tinymce-activate-visual-view' );
					}
				} catch ( e ) {
					// Output error if the developer mode is enabled
					if ( UncannyAutomator._site.has_debug_enabled ){
						// eslint-disable-next-line no-console
						console.warn( e );
					}

					alert( `Debugging: Couldn't add all tokens. Enable debug mode and check the console.` );
				}

				// Destroy the modal
				modal.destroy();
			},
			afterHide: function(){
				modal.destroy();
			}
		});
	}

	/**
	 * Generates a formatted table or plain text string from a matrix of tokens.
	 *
	 * @param {Array<Object>} tokensMatrix - The matrix of tokens, where each token is an object with name, id, and content properties.
	 * @param {Object} options - Configuration options for generating the output.
	 * @param {String} [options.fieldType='text'] - The type of field; affects plain text formatting (e.g., textarea, text).
	 * @param {Boolean} [options.richText=false] - Whether to output HTML rich text (table) or plain text.
	 * @param {Boolean} [options.includeName=true] - Whether to include the name field in the output.
	 * @param {Boolean} [options.includeID=false] - Whether to include the ID field in the output.
	 * @returns {String} A formatted table (HTML) or plain text string based on the input and options.
	 */
	tokensLogging = (
		tokensMatrix = [],
		{ 
			fieldType = 'text', 
			richText = false, 
			includeName = true, 
			includeID = false 
		} = {}
	) => {
		/**
		 * Creates a table cell or returns plain content based on the richText flag.
		 *
		 * @param {String} content - The content to be placed inside the cell.
		 * @param {String} [tagName='td'] - The HTML tag to use for the cell (e.g., 'td', 'th').
		 * @param {String} [styles=''] - The inline styles to apply to the cell.
		 * @returns {String} The generated HTML string for the cell or the plain content.
		 */
		const createCell = ( content, tagName = 'td', styles = 'padding: 12px; text-align: left; border: 1px solid #dddddd; word-wrap: break-word; overflow-wrap: break-word;' ) => {
			return richText
				? `<${ tagName } style="${ styles }">${ content }</${ tagName }>`
				: content;
		};
	
		/**
		 * Creates a table row or a plain text string for a given token row.
		 *
		 * @param {Object} row - The token row object containing name, id, and content.
		 * @param {String} row.name - The name of the token.
		 * @param {String} row.id - The ID of the token.
		 * @param {String} row.content - The content of the token.
		 * @returns {String} The generated HTML string for the row or the plain text string.
		 */
		const createRow = ({ name, id, content }) => {
			const rowCells = [];
		
			if ( includeName ) rowCells.push( createCell( name ) );
			if ( includeID ) rowCells.push( createCell( richText ? id : `(${id})` ) );
			rowCells.push( createCell( richText ? content : ( includeName || includeID ) ? `: ${ content }` : content ) );
		
			return richText ? `<tr>${ rowCells.join( '' ) }</tr>` : rowCells.join( ' ' );
		};
	
		/**
		 * Creates a table header row for the table if richText is enabled.
		 *
		 * @returns {String} The generated HTML string for the table header or an empty string.
		 */
		const createHeader = () => {
			if ( ! richText ) return '';
		
			const headerCells = [];
			const thStyles = 'padding: 12px; text-align: left; border: 1px solid #dddddd; word-wrap: break-word; background-color: #f2f2f2; overflow-wrap: break-word;';
		
			if ( includeName ) headerCells.push( createCell( escapeHTML( __( 'Name', 'uncanny-automator' ) ), 'th', thStyles ) );
			if ( includeID ) headerCells.push( createCell( escapeHTML( __( 'ID', 'uncanny-automator' ) ), 'th', thStyles ) );
			headerCells.push( createCell( escapeHTML( __( 'Value', 'uncanny-automator' ) ), 'th', thStyles ) );
		
			return `<thead><tr>${ headerCells.join( '' ) }</tr></thead>`;
		};
	
		/**
		 * Creates the body of the table or a plain text representation of the token rows.
		 *
		 * @param {Array<Object>} rows - An array of token row objects.
		 * @returns {String} The generated HTML string for the table body or a plain text string.
		 */
		const createBody = rows => {
			const content = rows.map( createRow );
			const separator = ! richText ? ( fieldType === 'textarea' ? '\n\n' : ' | ' ) : '';
		
			return richText ? `<tbody>${ content.join( '' ) }</tbody>` : content.join( separator );
		};
	
		/**
		 * Creates the full table or plain text output based on the provided token rows.
		 *
		 * @param {Array<Object>} rows - An array of token row objects.
		 * @returns {String} The generated HTML string for the table or a plain text string.
		 */
		const createTable = rows => {
			return richText
				? `<table style="width: 100%; table-layout: fixed; border-collapse: collapse; margin: 20px 0;">${ createHeader() }${ createBody( rows ) }</table>`
				: createBody( rows );
		};
	
		return createTable( tokensMatrix );
	};
  

	listenToggleButton(){
		this.$elements.toggleButton.on( 'click', () => {
			// Check if it's NOT open
			if ( ! this.data.isOpen ){
				this.open();
			}
			else {
				this.close( {
					triggeredBy: 'toggle-button'
				} );
			}
		});
	}

	search(){
		// Listen changes to the search field
		this.$elements.searchField.on( 'input', debounce( () => {
			// Get the value
			let query = this.$elements.searchField.val();

			// Remove spaces and make the query lowercase
			query = query
				.trim()
				.toLowerCase()
				.replace( /\s/g, '' );

			if ( ! isEmpty( query ) ){
				// Open all groups of tokens
				this.$elements.allGroups
					.addClass( 'form-variable__group--open' );

				// Hide all elements
				this.$elements.dropdownTokens
					.addClass( 'form-variable__group-item--hidden-by-search' );

				// Get search matches
				const $searchMatches = this.$elements.dropdownTokens
					.filter(( i, token ) => {
						return token.dataset.search.search( query ) !== -1
					}
				);

				// Show the search matches
				$searchMatches
					.removeClass( 'form-variable__group-item--hidden-by-search' );
			}
			else {
				// Close all groups
				this.$elements.allGroups
					.removeClass( 'form-variable__group--open' );

				// Show all tokens
				this.$elements.dropdownTokens
					.removeClass( 'form-variable__group-item--hidden-by-search' );
			}

			// Set the visibility of the group of tokens
			this.updateGroupsOfTokensVisibility();
		}, 30 ) );

		this.$elements.searchField.on( 'focus', () => {
			this.$elements.wrapper.addClass( 'form-variable--search-focus' );
		} );

		this.$elements.searchField.on( 'blur', () => {
			this.$elements.wrapper.removeClass( 'form-variable--search-focus' );
		} );
	}

	setFilter( filterByType = true ){
		// Check if we have to filter
		if ( filterByType ){
			// Hide all elements
			this.$elements.dropdownTokens
				.addClass( 'form-variable__group-item--hidden-by-filter' );

			// Get the tokens by type
			const $tokensByType = this.$elements.dropdownTokens
				.filter(( i, token ) => {
					// Check if the field is a "float" field. In that case,
					// promote all the "int" tokens to "float". All integers
					// are floats
					const tokenType = ( token.dataset.type == 'int' && this.Field.attributes.fieldType == 'float' ) ? 'float' : token.dataset.type;

					// Return the tokens that match the field type
					return tokenType == this.Field.attributes.fieldType;
				}
			);

			// Show the tokens that match the field type
			$tokensByType
				.removeClass( 'form-variable__group-item--hidden-by-filter' );
		}
		else {
			// Show all tokens
			this.$elements.dropdownTokens
				.removeClass( 'form-variable__group-item--hidden-by-filter' );
		}

		// Set the visibility of the group of tokens
		this.updateGroupsOfTokensVisibility();
	}

	updateGroupsOfTokensVisibility(){
		// this.$elements.allGroups
			// .removeClass( 'form-variable__group--open' );

		// Create the variable that we will use to evaluate
		// if there are available tokens
		let hasResults = false;

		// Show all the groups of tokens
		this.$elements.allGroups
			.removeClass( 'form-variable__group--without-available-tokens' );

		// Hide the group of tokens that don't have available tokens
		this.$elements.allGroups
			.filter(( i, group ) => {
				// Check if the group has available tokens
				const groupHasAvailableTokens = group
					.querySelectorAll( '.form-variable__group-item:not(.form-variable__group-item--hidden-by-filter):not(.form-variable__group-item--hidden-by-search)' )
					.length > 0;

				// If it doesn't have available tokens, then hide the group
				if ( ! groupHasAvailableTokens ){
					group.classList.add( 'form-variable__group--without-available-tokens' );
				}
				else {
					// The group has available tokens, update "hasResults"
					hasResults = true;
				}
			}
		);

		// Check if there are available tokens at all
		if ( hasResults ){
			this.$elements.noResultsOption
				.addClass( 'form-variable__group-item-no-results--hide' );

			// Check if we have to show or hide the separators
			if ( this.$elements.separators.triggers ) {
				// Check if tokens from triggers
				if ( this.$elements.allGroups.filter( '.form-variable__group--trigger:not(.form-variable__group--without-available-tokens)' ).length === 0 ) {
					this.$elements.separators.triggers.classList.add( 'form-variable__group-item--hidden-by-search' );
				} else {
					this.$elements.separators.triggers.classList.remove( 'form-variable__group-item--hidden-by-search' );
				}
			}

			// Check if we have to show or hide the separators
			if ( this.$elements.separators.actions ) {
				// Check if tokens from actions
				if ( this.$elements.allGroups.filter( '.form-variable__group--action:not(.form-variable__group--without-available-tokens)' ).length === 0 ) {
					this.$elements.separators.actions.classList.add( 'form-variable__group-item--hidden-by-search' );
				} else {
					this.$elements.separators.actions.classList.remove( 'form-variable__group-item--hidden-by-search' );
				}
			}
		}
		else {
			this.$elements.noResultsOption
				.removeClass( 'form-variable__group-item-no-results--hide' );

			if ( this.$elements.separators.triggers ) { this.$elements.separators.triggers.classList.add( 'form-variable__group-item--hidden-by-search' ); }
			if ( this.$elements.separators.actions ) { this.$elements.separators.actions.classList.add( 'form-variable__group-item--hidden-by-search' ); }
		}
	}

	open(){
		// If it's a date or time field, try to close
		// the calendar
		if ( [ 'date', 'time' ].includes( this.Field.attributes.fieldType ) ){
			try {
				this.Field.flatpickrInstance.close();
			} catch ( e ){
				// Continue regardless of error
		
				// Output error if the developer mode is enabled
				if ( UncannyAutomator._site.has_debug_enabled ){
					// eslint-disable-next-line no-console
					console.warn( e );
				}
			}
		}

		// Add open class
		this.$elements.wrapper.addClass( 'form-variable--open' );

		// Save status
		this.data.isOpen = true;

		// Listen clicks outside the token selector and close it
		this.closeWhenClickedOutside();

		// Reset the search field (remove the value)
		this.$elements.searchField
			.val( '' )
			.trigger( 'input' )
			// Set padding of the search field depending on the width
			// of the checkbox label
			.css({
				'padding-right': `${ ( ( isDefined( this.$elements.filterContainer ) ? this.$elements.filterContainer.outerWidth() : 0 ) + 12 ) }px`
			});;

		// Focus the search field
		this.$elements.searchField
			.focus();

		// Determine view (default/bubble)
		this.setDropdownView();
								
		// Determine direction of the selector
		this.setDropdownDirection();

		// Set scroll
		this.setDropdownHeight();

		// Create the custom context menus
		this.addAllTokensInGroup();

		// Send custom event to the document
		document.dispatchEvent( new CustomEvent( 'uap/builder/legacy/field/token-selector/open', {
			detail: {
				fieldCode: this.Field.attributes.optionCode
			}
		} ) );
	}

	addAllTokensInGroup() {
		// Iterate the groups of tokens
		this.tokens.forEach(( groupOfTokens, groupKey ) => {
			// Listen to Ctrl + Click on the group of tokens
			groupOfTokens.$toggleButton.off( 'click.add-all-tokens-in-group' ).on( 'click.add-all-tokens-in-group', ( event ) => {
				// Shift + Click
				if ( event.shiftKey ) {
					this.close( {
						triggeredBy: 'token-added'
					} );

					this.addAllTokensInGroupCallback( groupOfTokens )
				}
			} );
		});
	}

	close( { triggeredBy = 'unknown' } ){
		this.$elements.wrapper.removeClass( 'form-variable--open' );

		// Save status
		this.data.isOpen = false;

		// Rest input value and trigger 'input' event
		this.$elements.searchField
			.val( '' )
			.trigger( 'input' );

		// Stop listening clicks outside the token selector
		$( document ).off( 'mouseup.uap-token-selector-click-outside' );

		// Send custom event to the document
		document.dispatchEvent( new CustomEvent( 'uap/builder/legacy/field/token-selector/close', {
			detail: {
				fieldCode: this.Field.attributes.optionCode,
				triggeredBy: triggeredBy
			}
		} ) );
	}

	destroy(){
		// Remove all the elements but the original field
		this.$elements.renderContainer.children().insertAfter( this.$elements.wrapper );
		this.$elements.wrapper.remove();
	}

	closeWhenClickedOutside(){
		$( document ).on( 'mouseup.uap-token-selector-click-outside', ( event ) => {
			// Set the targetted container
			const $container = this.$elements.wrapper;

			if ( ! $container.is( event.target ) && $container.has( event.target ).length === 0 ){
				// Hide
				this.close( {
					triggeredBy: 'click-outside'
				} );
			}
		});
	}

	getDropdownPosition(){
		const $window = $( window );

		const position = {}
		position.above = this.$elements.wrapper.offset().top - $window.scrollTop();
		position.below = $window.height() - ( position.above + this.$elements.wrapper.outerHeight() );

		// Check if the current field is a textarea
		if ( this.Field.type == 'textarea' ){
			// Recalculate the space below
			position.below = $window.height() - ( position.above + 38 );
		}

		// Add "real size" class. We're using this to
		// get the real size of the dropdown
		this.$elements.dropdown.addClass( 'uo-realsize' );
		// Get the dropdown height
		const dropdownHeight = this.$elements.dropdown.outerHeight() + 110; // + 75px to show at least 1 option
		this.$elements.dropdown.removeClass( 'uo-realsize' );

		// Show dropdown always below for textareas
		if ( this.Field.type == 'textarea' ){
			position.hasSpaceBelow = true;
		}
		else {
			position.hasSpaceBelow = position.below >= dropdownHeight;
		}

		return position;
	}

	setDropdownView() {
		if ( this.$elements.dropdown.width() < 350 ) {
			// Set the "bubble" view
			this.$elements.wrapper.addClass( 'form-variable--bubble' );
		}
	}

	setDropdownDirection(){
		// Get the position of the dropdown
		const position = this.getDropdownPosition();

		// Remove the position classes
		this.$elements.wrapper.removeClass( 'form-variable--above form-variable--below' );

		// Determine the position
		if ( position.below > position.above || position.hasSpaceBelow ){
			// Let's show it below
			this.$elements.wrapper.addClass( 'form-variable--below' );

			// Save the position
			this.data.position = 'below';
		}
		else {
			// Let's show it above
			this.$elements.wrapper.addClass( 'form-variable--above' );

			// Save the position
			this.data.position = 'above';
		}
	}

	setDropdownHeight(){
		// Check if it's open
		if ( this.data.isOpen ){
			// Get the max height of the dropdown
			const maxHeight = this.getDropdownPosition()[ this.data.position ] - this.$elements.searchField.outerHeight() - 45;

			// Set the max height
			this.$elements.dropdownGroupsContainer
				.css({ 
					'max-height' : `${ maxHeight }px`
				});
		}
	}

	focusSearchField(){
		this.$elements.searchField
			.focus();
	}

	resizeDropdownOnWindowResize(){
		$( window ).on( 'resize', 
			debounce( () => {
				// Determine direction of the selector
				this.setDropdownDirection();

				// Set height
				this.setDropdownHeight();
			}, 20 )
		);
	}

	resizeDropdownOnWindowScroll(){
		$( window ).on( 'scroll', 
			debounce( () => {
				// Determine direction of the selector
				this.setDropdownDirection();

				// Set height
				this.setDropdownHeight();
			}, 300 )
		);
	}

	refreshOnFieldChange(){
		// CodeMirror methods
		this.Field.formElement.on( 'automator:field:change', () => {
			// Determine direction of the selector
			this.setDropdownDirection();

			// Set height
			this.setDropdownHeight();
		});
	}

	shouldSupportFilters(){
		// Get the field type
		const fieldType = this.Field.attributes.fieldType;

		return [ 'email', 'int', 'float', 'url', 'date', 'time' ].includes( fieldType );
	}
}
