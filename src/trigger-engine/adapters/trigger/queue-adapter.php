<?php

namespace Uncanny_Automator\Trigger_Engine\Adapters\Trigger;

use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Queue_Interface;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Queue\Trigger_Queue;

/**
 * Event Processor using Trigger_Queue
 *
 * This class adapts the Trigger_Queue to implement the Trigger_Queue_Interface,
 * providing a clean interface for event processing.
 *
 * @package Uncanny_Automator\Trigger_Engine\Adapters\Trigger
 * @since 6.7
 */
final class Queue_Adapter implements Trigger_Queue_Interface {

	/**
	 * The queue instance.
	 *
	 * @var Trigger_Queue
	 */
	private $queue;

	/**
	 * Initializes the queue adapter.
	 *
	 * Creates and initializes a Trigger_Queue instance.
	 *
	 * @return void
	 */
	public function __construct() {
		$this->queue = Trigger_Queue::get_instance();
		$this->queue->init();
	}

	/**
	 * Queue an event for processing.
	 *
	 * Adds a trigger event to the queue for later processing.
	 *
	 * @param string $trigger_code The trigger code to process.
	 * @param string $hook_name The WordPress hook that triggered this event.
	 * @param array<int,mixed> $args The arguments passed to the hook.
	 * 
	 * @return bool True if the event was queued, false if it was rejected (e.g., duplicate).
	 */
	public function queue_event( 
		string $trigger_code, 
		string $hook_name, 
		array $args ): bool {

		return $this->queue->enqueue( $trigger_code, $hook_name, $args );

	}

	/**
	 * Process all queued events.
	 * 
	 * Processes all events in the queue. This method is idempotent and can be called
	 * multiple times safely.
	 *
	 * @return void
	 */
	public function process_queue(): void {
		$this->queue->process_queue();
	}

	/**
	 * Get queue statistics.
	 *
	 * Returns statistics about the queue for monitoring and debugging.
	 *
	 * @return array<string,int> An array of queue statistics.
	 */
	public function get_stats(): array {
		return $this->queue->get_stats();
	}
}
