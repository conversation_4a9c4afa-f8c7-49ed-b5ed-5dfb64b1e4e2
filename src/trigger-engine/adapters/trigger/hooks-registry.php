<?php

namespace Uncanny_Automator\Trigger_Engine\Adapters\Trigger;

use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Queue_Registry_Interface;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Discovery_Interface;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Queue_Interface;

/**
 * WordPress Hooks Registry for Automator Triggers
 *
 * This class implements a pub/sub pattern for WordPress hooks, registering
 * trigger hooks early and publishing events to a queue for deferred processing.
 * It replaces the previous "actionify_triggers" approach with a cleaner event system.
 *
 * @package Uncanny_Automator\Trigger_Engine\Adapters\Trigger
 * @since 6.7
 */
final class Hooks_Registry implements Trigger_Queue_Registry_Interface {

	/**
	 * Map of registered WordPress hooks to trigger codes.
	 *
	 * @var array<string,array<int,string>>
	 */
	private $registered_hooks = array();

	/**
	 * Tracks processed event signatures to prevent duplicates.
	 *
	 * @var array<string,bool>
	 */
	private $processed_events = array();

	/**
	 * The trigger discovery service.
	 *
	 * @var Trigger_Discovery_Interface
	 */
	private $trigger_discovery;

	/**
	 * The event processor service.
	 *
	 * @var Trigger_Queue_Interface
	 */
	private $event_processor;

	/**
	 * Initializes the hooks registry.
	 *
	 * @param Trigger_Discovery_Interface $trigger_discovery Service to discover active triggers.
	 * @param Trigger_Queue_Interface     $event_processor Service to queue and process events.
	 *
	 * @return void
	 */
	public function __construct(
		Trigger_Discovery_Interface $trigger_discovery,
		Trigger_Queue_Interface $event_processor
	) {
		$this->trigger_discovery = $trigger_discovery;
		$this->event_processor   = $event_processor;
	}

	/**
	 * Register WordPress hooks for all active triggers.
	 *
	 * Discovers all active triggers and registers WordPress action hooks
	 * that will publish events to our event system when triggered.
	 *
	 * @return void
	 */
	public function register_automation_hooks(): void {
		$active_triggers = $this->trigger_discovery->get_active_triggers();

		if ( empty( $active_triggers ) ) {
			return;
		}

		$flattened_triggers = $this->flatten_trigger_array( $active_triggers );
		$unique_triggers    = $this->remove_duplicate_triggers( $flattened_triggers );

		foreach ( $unique_triggers as $trigger ) {
			$this->register_single_trigger( $trigger );
		}

		/**
		 * Fires after all automation triggers have been registered.
		 *
		 * @param array $unique_triggers List of unique triggers that were registered.
		 */
		do_action( 'automator_triggers_registered', $unique_triggers );
	}

	/**
	 * Publish an event when a WordPress hook fires.
	 *
	 * This method is called by the WordPress action hooks registered in
	 * register_automation_hooks(). It finds matching triggers and queues
	 * events for processing.
	 *
	 * @param string $hook_name The WordPress hook that was triggered.
	 * @param array<int,mixed> $args The arguments passed to the hook.
	 *
	 * @return void
	 */
	public function publish_event( string $hook_name, array $args ): void {
		// Simple deduplication
		$event_signature = $this->generate_event_signature( $hook_name, $args );

		if ( isset( $this->processed_events[ $event_signature ] ) ) {
			return; // Already processed
		}

		$this->processed_events[ $event_signature ] = true;

		// Find triggers that should respond to this hook
		$matching_triggers = $this->find_triggers_for_hook( $hook_name );

		foreach ( $matching_triggers as $trigger_code ) {
			$this->event_processor->queue_event( $trigger_code, $hook_name, $args );
		}
	}

	/**
	 * Get system statistics for monitoring and debugging.
	 *
	 * @return array{
	 *   registered_hooks: int,
	 *   processed_events: int,
	 *   processor_stats: array<string,int>
	 * } Statistics about the event system.
	 */
	public function get_stats(): array {
		return array(
			'registered_hooks' => count( $this->registered_hooks ),
			'processed_events' => count( $this->processed_events ),
			'processor_stats'  => $this->event_processor->get_stats(),
		);
	}

	/**
	 * Register a single trigger hook with WordPress.
	 *
	 * @param array{hook?: string, code?: string} $trigger The trigger configuration.
	 *
	 * @return void
	 */
	private function register_single_trigger( array $trigger ): void {
		$hook_name    = $trigger['hook'] ?? '';
		$trigger_code = $trigger['code'] ?? '';

		if ( empty( $hook_name ) || empty( $trigger_code ) ) {
			return;
		}

		// Only register each hook once
		if ( isset( $this->registered_hooks[ $hook_name ] ) ) {
			$this->registered_hooks[ $hook_name ][] = $trigger_code;
			return;
		}

		$this->registered_hooks[ $hook_name ] = array( $trigger_code );

		// Register WordPress hook that publishes to our event system
		add_action(
			$hook_name,
			function ( ...$args ) use ( $hook_name ) {
				$this->publish_event( $hook_name, $args );
			},
			10,
			99
		);
	}

	/**
	 * Find triggers that should respond to a specific hook.
	 *
	 * @param string $hook_name The WordPress hook name.
	 *
	 * @return array<int,string> Array of trigger codes that respond to this hook.
	 */
	private function find_triggers_for_hook( string $hook_name ): array {
		return $this->registered_hooks[ $hook_name ] ?? array();
	}

	/**
	 * Flatten nested trigger array structure.
	 *
	 * @param array<string,mixed> $triggers The raw triggers array from discovery.
	 *
	 * @return array<int,array<string,string>> Flattened array of trigger configurations.
	 */
	private function flatten_trigger_array( array $triggers ): array {
		$result = array();

		foreach ( $triggers as $group ) {
			if ( isset( $group[0] ) ) {
				$result = array_merge( $result, $group );
				continue;
			}

			$result[] = $group;
		}

		return $result;
	}

	/**
	 * Remove duplicate triggers based on code and hook combination.
	 *
	 * @param array<int,array<string,string>> $triggers Array of trigger configurations.
	 *
	 * @return array<int,array<string,string>> Array with duplicates removed.
	 */
	private function remove_duplicate_triggers( array $triggers ): array {
		$unique = array();
		$seen   = array();

		foreach ( $triggers as $trigger ) {
			$key = ( $trigger['code'] ?? '' ) . '|' . ( $trigger['hook'] ?? '' );

			if ( ! isset( $seen[ $key ] ) ) {
				$unique[]     = $trigger;
				$seen[ $key ] = true;
			}
		}

		return $unique;
	}

	/**
	 * Generate a unique signature for an event to prevent duplicate processing.
	 *
	 * Creates a hash based on hook name, arguments, user ID, and time window
	 * to identify unique events within a short time period.
	 *
	 * @param string $hook_name The WordPress hook name.
	 * @param array<int,mixed> $args The arguments passed to the hook.
	 *
	 * @return string MD5 hash signature for the event.
	 */
	private function generate_event_signature( string $hook_name, array $args ): string {
		$signature_data = array(
			'hook'        => $hook_name,
			'args_count'  => count( $args ),
			'args_hash'   => md5( maybe_serialize( $args ) ),
			'user_id'     => get_current_user_id(),
			'time_window' => floor( time() / 3 ), // 3-second dedup window
		);

		return md5( maybe_serialize( $signature_data ) );
	}
}
