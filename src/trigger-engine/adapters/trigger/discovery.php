<?php

namespace Uncanny_Automator\Trigger_Engine\Adapters\Trigger;

use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Discovery_Interface;

/**
 * WordPress Trigger Discovery
 *
 * This class is responsible for discovering active triggers from WordPress database.
 * It queries the WordPress database for published triggers and recipes,
 * and returns them in a structured format for the event system.
 *
 * @package Uncanny_Automator\Trigger_Engine\Adapters\Trigger
 * @since 6.7
 */
final class Discovery implements Trigger_Discovery_Interface {

	/**
	 * Get all active triggers from the WordPress database.
	 *
	 * Queries the WordPress database for all published triggers associated with
	 * published recipes. Returns a structured array of trigger configurations.
	 *
	 * @return array<string,mixed> Array of active triggers indexed by trigger code.
	 */
	public function get_active_triggers(): array {
		global $wpdb;

		$results = $wpdb->get_results(
			$wpdb->prepare(
				"SELECT 
                    action_hook_meta.meta_value as action_hook,
                    code_meta.meta_value as trigger_code
                FROM $wpdb->postmeta action_hook_meta
                INNER JOIN $wpdb->postmeta code_meta
                    ON code_meta.post_id = action_hook_meta.post_id
                    AND code_meta.meta_key = 'code'
                INNER JOIN $wpdb->posts trigger_post 
                    ON trigger_post.ID = action_hook_meta.post_id
                    AND trigger_post.post_status = %s
                    AND trigger_post.post_type = 'uo-trigger'
                INNER JOIN $wpdb->posts recipe_post 
                    ON recipe_post.ID = trigger_post.post_parent
                    AND recipe_post.post_status = %s
                    AND recipe_post.post_type = 'uo-recipe'
                WHERE action_hook_meta.meta_key = 'add_action'",
				'publish',
				'publish'
			),
			ARRAY_A
		);

		if ( empty( $results ) ) {
			return array();
		}

		$active_triggers = array();

		foreach ( $results as $result ) {
			$action_hook = maybe_unserialize( $result['action_hook'] );

			if ( is_array( $action_hook ) ) {
				foreach ( $action_hook as $hook ) {
					$active_triggers[ $result['trigger_code'] ][] = array(
						'hook' => (string) $hook,
						'code' => $result['trigger_code'],
					);
				}
				continue;
			}

			$active_triggers[ $result['trigger_code'] ] = array(
				'hook' => (string) $action_hook,
				'code' => $result['trigger_code'],
			);
		}

		return $active_triggers;
	}

	/**
	 * Check if a specific trigger is active.
	 *
	 * Determines whether a trigger with the given code exists and is active.
	 *
	 * @param string $trigger_code The trigger code to check.
	 *
	 * @return bool True if the trigger is active, false otherwise.
	 */
	public function is_trigger_active( string $trigger_code ): bool {
		$triggers = $this->get_active_triggers();
		return isset( $triggers[ $trigger_code ] );
	}
}
