# Pub/Sub with Deferred Queue Architecture  
## Event Listener System for Uncanny Automator

A robust, scalable, and WordPress 6.7+ compatible event processing system that leverages a **Publish-Subscribe (Pub/Sub) pattern with a Deferred Queue** for reliable automation and performance[1][2].

---

## Why This System Exists

**WordPress 6.7+** enforces that translation functions (`__()`, `esc_attr_x()`, etc.) cannot be called before the `init` hook, or you’ll encounter `_doing_it_wrong()` warnings[1][2].  
**Problem:** Many plugins—including Uncanny Automator—previously loaded triggers and registered hooks before `init`, which broke with this change[1][2].

---

## Solution Overview

This system introduces a **deferred event queue** that:

- Allows trigger files to be loaded safely at `init` (supporting translation functions)[1][2].
- Registers all necessary WordPress action hooks as early as possible to catch all events[1][2].
- Queues any events fired before triggers are available, then processes them after dependencies are loaded[1][2].
- Provides deduplication, error handling, and database-backed reliability[1][2].

---

## Architecture Diagram

```
src/event-listener/
├── listener.php        # Registers WP hooks, manages trigger discovery
├── lazy-queue.php      # Core in-memory + DB queue system
├── args-packager.php   # Cleans and serializes hook arguments
└── trigger-args.php    # Safe data container for trigger arguments
```


---

## How It Works

### 1. Early Hook Registration
- On plugin load, all active triggers are discovered and WordPress action hooks are registered immediately[1][2].
- This ensures no events are missed, even if fired before `init`[1][2].

### 2. Deferred Queueing
- If a trigger fires before the system is ready, its arguments are queued in memory[1][2].
- Triggers are processed during `wp_loaded` or `shutdown`, after translations and integrations are available[1][2].

### 3. Safe Serialization
- Arguments are cleaned to remove closures, break cycles, and ensure serializability[1][3][2].
- Data is safely stored for later execution or database fallback[1][3][2].

### 4. Deduplication & Reliability
- Each trigger is hashed (by code, user, post, time window, and argument signature) to prevent duplicates[1][4][2].
- Failed triggers are saved to the database and retried on the next request[1][4][2].
- Automatic daily cleanup removes old entries[1][4][2].

---

## Key Features

- **Full WP 6.7+ Compatibility:** Translation functions can be used safely in triggers[1][2].
- **No Missed Events:** Hooks are registered early, so all plugin events are caught[1][5][2].
- **Performance:** Trigger processing is deferred, preventing page slowdowns[1][4][2].
- **Reliability:** Database fallback ensures no events are lost[1][4][2].
- **Extensible:** Queue backend can be swapped (e.g., for Redis, SQS) without changing failure recovery[1][2].
- **Deduplication:** Smart hashing prevents duplicate trigger processing[1][4][2].

---

## Database Schema

```sql
CREATE TABLE wp_uap_fast_queue (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    trigger_hash varchar(32) NOT NULL,
    trigger_code varchar(100) NOT NULL,
    trigger_data longtext NOT NULL,
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY trigger_hash (trigger_hash),
    KEY created_at (created_at)
);
```


---

## Integration & Usage

### Initialization

```php
use Uncanny_Automator\Event_Listener\Listener;
Listener::get_instance()->listen();
```


### Hook Points

- `automator_enqueue_trigger_before_enqueue_trigger`
- `automator_enqueue_trigger_before_process_trigger_item`
- `automator_enqueue_trigger_after_trigger_execution`
- `automator_enqueue_trigger_after_trigger_execution_error`
[1][2]

---

## Configuration

- **MAX_PAYLOAD_SIZE:** Default 1MB (adjust as needed)[1][4][2].
- **ONE_OFF_PRIORITY:** Unique priority for one-off hook execution[1][4][2].
- **TABLE_NAME:** Database table for failed triggers[1][4][2].

---

## Debugging & Monitoring

- Use `Lazy_Queue::get_instance()->get_stats()` for queue health[1][4][2].
- Logs are written to `wp-content/debug.log` and custom Automator logs when `WP_DEBUG` is enabled[1][4][2].

---

## Performance Impact

| Before (Direct)      | After (Deferred Queue)         |
|----------------------|-------------------------------|
| Immediate processing | ~0.1ms overhead per trigger   |
| Page slowdowns       | Batched, deferred execution   |
| Risk of timeouts     | Reliable, non-blocking        |
| Duplicate triggers   | Smart deduplication           |
[1][4][2]

---

## Maintenance

- **Daily Cleanup:** Old DB entries are removed via `wp_cron`[1][4][2].
- **Monitoring:** Check queue stats and database size for health[1][4][2].
- **Troubleshooting:**  
  - If triggers don’t fire, ensure `process_queue()` is called[1][4][2].
  - High DB queue may indicate serialization or validation errors[1][4][2].

---

## Extensibility

- **Queue Backend:** Easily migrate to Redis, SQS, or other systems for primary queueing; database fallback remains unchanged[1][2].
- **Priority/Batched Processing:** Supports future enhancements for priority queues and batch jobs[1][2].

---

## Summary

This **Pub/Sub with Deferred Queue Architecture** ensures Uncanny Automator is fully compatible with WordPress 6.7+, never misses events, and is robust, performant, and ready for enterprise scale[1][2].  
It is a formal, industry-standard solution recognized across the WordPress ecosystem and broader software engineering community[1][2].
