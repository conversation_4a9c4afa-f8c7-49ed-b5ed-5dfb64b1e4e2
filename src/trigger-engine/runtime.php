<?php
namespace Uncanny_Automator\Trigger_Engine;

use Uncanny_Automator\Trigger_Engine\Core\Trigger\Engine;
use Uncanny_Automator\Traits\Singleton;
use Uncanny_Automator\Trigger_Engine\Adapters\Trigger\Hooks_Registry;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Queue\Trigger_Queue;

/**
 * The main entry point to initialize trigger hooks and process events.
 */
final class Runtime {

	use Singleton;

	/**
	 * @var Trigger_Queue_Registry_Interface
	 */
	private $event_system;

	/**
	 * Initializes the event system.
	 */
	public function __construct() {
		$this->event_system = Engine::create();
	}

	/**
	 * Listen.
	 */
	public function listen(): void {
		$this->event_system->register_automation_hooks();
		add_action( 'wp_loaded', array( $this, 'process_events' ), 1 );
		add_action( 'shutdown', array( $this, 'process_events' ), 900 );
	}

	/**
	 * Process events.
	 */
	public function process_events(): void {
		$event_processor = Trigger_Queue::get_instance();
		$event_processor->process_queue();
	}

	/**
	 * Get stats.
	 *
	 * @return array
	 */
	public function get_stats(): array {
		return $this->event_system->get_stats();
	}
}
