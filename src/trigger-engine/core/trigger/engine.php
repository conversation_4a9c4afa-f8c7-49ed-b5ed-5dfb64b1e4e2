<?php

namespace Uncanny_Automator\Trigger_Engine\Core\Trigger;

use Uncanny_Automator\Trigger_Engine\Adapters\Trigger\Discovery;
use Uncanny_Automator\Trigger_Engine\Adapters\Trigger\Hooks_Registry;
use Uncanny_Automator\Trigger_Engine\Adapters\Trigger\Queue_Adapter;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Queue_Registry_Interface;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Discovery_Interface;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces\Trigger_Queue_Interface;
use Uncanny_Automator\Trigger_Engine\Core\Trigger\Queue\Trigger_Queue;

/**
 * Event System Factory
 */
final class Engine {

	/** @var Trigger_Queue_Registry_Interface|null */
	private static $instance = null;

	/**
	 * Create fully configured Event System
	 */
	public static function create(): Trigger_Queue_Registry_Interface {
		if ( null === self::$instance ) {
			self::$instance = self::build_event_system();
		}

		return self::$instance;
	}

	/**
	 * Build event system with clean dependency injection
	 */
	private static function build_event_system(): Trigger_Queue_Registry_Interface {
		$trigger_discovery = new Discovery();
		$event_processor   = new Queue_Adapter();

		return new Hooks_Registry( $trigger_discovery, $event_processor );
	}

	/**
	 * Create for testing with mock dependencies
	 */
	public static function create_for_testing(
		$mock_trigger_discovery = null,
		$mock_event_processor = null
	): Hooks_Registry {
		return new Hooks_Registry(
			$mock_trigger_discovery ?? new Discovery(),
			$mock_event_processor ?? new Queue_Adapter()
		);
	}

	/**
	 * Reset singleton (for testing)
	 */
	public static function reset(): void {
		self::$instance = null;
	}
}
