<?php
namespace Uncanny_Automator\Trigger_Engine\Core\Trigger\Arguments;

use Uncanny_Automator\Trigger_Engine\Core\Trigger\Arguments\Trigger_Arguments;
use SplObjectStorage;

/**
 * Safe Serialization Handler for Trigger Arguments
 *
 * This class handles safe serialization and deserialization of trigger arguments,
 * ensuring that closures are removed, object cycles are broken, and only safe
 * data structures are preserved.
 *
 * @package Uncanny_Automator\Trigger_Engine\Core\Trigger\Arguments
 * @since 6.7
 */
class Trigger_Arguments_Packager {

	/**
	 * Object storage for cycle detection during cleaning.
	 *
	 * @var SplObjectStorage
	 */
	private $seen;

	/**
	 * Constructor
	 *
	 * Initializes the object storage for cycle detection.
	 *
	 * @return void
	 */
	public function __construct() {
		$this->seen = new SplObjectStorage();
	}

	/**
	 * Package arguments into a serialized Trigger_Arguments object.
	 *
	 * Creates a Trigger_Arguments object with the provided arguments and metadata,
	 * then serializes it safely for storage or transmission.
	 *
	 * @param array<int|string,mixed> $args Hook arguments to package.
	 * @param array<string,mixed> $metadata Trigger metadata to include.
	 * 
	 * @return string|false Serialized package or false on failure.
	 */
	public function package( array $args, array $metadata = array() ) {
		try {
			$package = new Trigger_Arguments( $args, $metadata );
			return maybe_serialize( $package );
		} catch ( \Throwable $e ) {
			automator_log( 'Packager error (package): ' . $e->getMessage(), 'safe-hook-arguments-packaging' );
			return false;
		}
	}

	/**
	 * Unpack serialized data back into a Trigger_Arguments object.
	 *
	 * Safely deserializes the payload and ensures it's a valid Trigger_Arguments instance.
	 *
	 * @param string $payload The serialized Trigger_Arguments object.
	 * 
	 * @return Trigger_Arguments|false Unpacked Trigger_Arguments object or false on failure.
	 */
	public function unpack( $payload ) {
		try {
			// Only allow our safe wrapper class
			$data = maybe_unserialize( $payload );

			if ( $data instanceof Trigger_Arguments ) {
				return $data;
			}

			return false;
		} catch ( \Error $e ) {
			automator_log( 'Packager error (unpack): ' . $e->getMessage(), 'safe-hook-arguments-packaging' );
			return false;
		}
	}

	/**
	 * Clean a single value for safe serialization.
	 *
	 * Public method exposed for external use that resets cycle detection
	 * and cleans a value for serialization.
	 *
	 * @param mixed $val The value to clean.
	 * 
	 * @return mixed The cleaned value safe for serialization.
	 */
	public function clean_value( $val ) {
		// Reset for each top-level call
		$this->seen = new SplObjectStorage();
		return $this->clean( $val );
	}

	/**
	 * Recursively clean values for safe serialization.
	 *
	 * Handles different data types:
	 * - Scalars and null are returned as-is
	 * - Arrays are recursively cleaned
	 * - Objects are cloned and their properties cleaned
	 * - Closures are removed
	 * - Cycles are broken
	 * - Resources are removed
	 *
	 * @param mixed $val The value to clean.
	 * 
	 * @return mixed The cleaned value safe for serialization.
	 */
	private function clean( $val ) {
		// scalars & null
		if ( is_scalar( $val ) || null === $val ) {
			return $val;
		}

		// arrays → recurse
		if ( is_array( $val ) ) {
			$out = array();
			foreach ( $val as $k => $v ) {
				$out[ $k ] = $this->clean( $v );
			}
			return $out;
		}

		// objects → cycle-detect & clone
		if ( is_object( $val ) ) {
			// break cycles
			if ( $this->seen->contains( $val ) ) {
				return null;
			}
			$this->seen->attach( $val );

			// drop closures entirely
			if ( $val instanceof \Closure ) {
				return null;
			}

			// clone + scrub props
			try {
				$clone = clone $val;
				$ref   = new \ReflectionClass( $val );
				foreach ( $ref->getProperties() as $prop ) {
					$prop->setAccessible( true );
					$v = $prop->getValue( $val );
					if ( is_array( $v ) || is_object( $v ) ) {
						$prop->setValue( $clone, $this->clean( $v ) );
					}
					// scalars left untouched
				}
				return $clone;
			} catch ( \Exception $e ) {
				// If we can't clone/access, just drop it
				return null;
			}
		}

		// resources & unknown → drop
		return null;
	}
}
