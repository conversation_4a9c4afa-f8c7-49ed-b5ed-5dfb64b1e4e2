<?php
namespace Uncanny_Automator\Trigger_Engine\Core\Trigger\Arguments;

/**
 * Safe wrapper for hook arguments that can be serialized/unserialized safely
 */
class Trigger_Arguments {

	/**
	 * The original arguments, cleaned for serialization
	 *
	 * @var array
	 */
	private $args;

	/**
	 * Metada<PERSON> about the trigger
	 *
	 * @var array
	 */
	private $metadata;

	/**
	 * Create a new package
	 *
	 * @param array $args Original hook arguments
	 * @param array $metadata Trigger metadata (code, recipe_id, etc.)
	 */
	public function __construct( array $args = array(), array $metadata = array() ) {
		$this->args     = $this->clean_args( $args );
		$this->metadata = $metadata;
	}

	/**
	 * Get the original arguments
	 *
	 * @return array
	 */
	public function get_args() {
		return $this->args;
	}

	/**
	 * Get metadata
	 *
	 * @return array
	 */
	public function get_metadata() {
		return $this->metadata;
	}

	/**
	 * Get a specific metadata value
	 *
	 * @param string $key
	 * @param mixed  $default
	 * @return mixed
	 */
	public function get_meta( $key, $default_value = null ) {
		return isset( $this->metadata[ $key ] ) ? $this->metadata[ $key ] : $default_value;
	}

	/**
	 * Clean arguments for safe serialization
	 *
	 * @param array $args
	 * @return array
	 */
	private function clean_args( array $args ) {

		$cleaner = new Trigger_Arguments_Packager();
		$cleaned = array();

		foreach ( $args as $index => $arg ) {
			// Use the existing cleaner to handle cycles/closures
			$cleaned[ $index ] = $cleaner->clean_value( $arg );
		}

		return $cleaned;
	}

	/**
	 * Magic method to allow direct access to args by index
	 *
	 * @param int $index
	 * @return mixed
	 */
	public function __get( $index ) {
		return isset( $this->args[ $index ] ) ? $this->args[ $index ] : null;
	}

	/**
	 * Check if an argument exists
	 *
	 * @param int $index
	 * @return bool
	 */
	public function __isset( $index ) {
		return isset( $this->args[ $index ] );
	}

	/**
	 * Convert to array for backward compatibility
	 *
	 * @return array
	 */
	public function to_array() {
		return $this->args;
	}

	/**
	 * Get count of arguments
	 *
	 * @return int
	 */
	public function count() {
		return count( $this->args );
	}
}
