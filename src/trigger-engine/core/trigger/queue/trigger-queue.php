<?php

namespace Uncanny_Automator\Trigger_Engine\Core\Trigger\Queue;

use Uncanny_Automator\Trigger_Engine\Core\Trigger\Arguments\Trigger_Arguments_Packager;
use Uncanny_Automator\Traits\Singleton;

/**
 * Fast Queue System for Uncanny Automator
 *
 * Design principles:
 * - Database as storage (reliable)
 * - Shutdown hook as processor (fast)
 * - Minimal overhead
 * - No loopback dependencies
 * - Smart deduplication
 */
class Trigger_Queue {

	use Singleton;

	/**
	 * Maximum payload size for the fast queue.
	 *
	 * @var int
	 */
	const MAX_PAYLOAD_SIZE = 1048576; // 1 MiB

	/**
	 * Priority for the one-off listener.
	 *
	 * Needs to be unique to prevent collisions with other plugins.
	 *
	 * This magic number is derived from the date: May 25, 2025 → 5252025.
	 *
	 * @link https://developer.wordpress.org/reference/functions/add_action/#priority
	 *
	 * @var int
	 */
	const ONE_OFF_PRIORITY = 288662867;

	/**
	 * Table name for the fast queue
	 *
	 * @var string
	 */
	const TABLE_NAME = 'uap_fast_queue';

	/**
	 * In-memory queue for current request
	 *
	 * @var array
	 */
	private $memory_queue = array();

	/**
	 * Processed triggers hash (deduplication)
	 *
	 * @var array
	 */
	private $processed_hashes = array();

	/**
	 * Whether shutdown hook is registered
	 *
	 * @var bool
	 */
	private $shutdown_registered = false;

	/**
	 * Whether the queue has been drained this request
	 *
	 * @var bool
	 */
	private $queue_drained = false;

	/**
	 * Serializer for safe serialization
	 *
	 * @var Trigger_Arguments_Packager
	 */
	private $hook_arguments_packager;

	/**
	 * Constructor
	 *
	 * @return void
	 */
	public function __construct() {
		$this->hook_arguments_packager = new Trigger_Arguments_Packager();
	}

	/**
	 * Initialize the queue system
	 *
	 * @return void
	 */
	public function init() {
		$this->register_cleanup();
	}

	/**
	 * Queue a trigger for processing
	 *
	 * @param string $trigger_code
	 * @param string $action_hook
	 * @param array  $args
	 * @return bool
	 */
	public function enqueue( $trigger_code, $action_hook, $args ) {

		$trigger_hash = $this->generate_trigger_hash( $trigger_code, $args );

		// Skip if already processed in this request
		if ( isset( $this->processed_hashes[ $trigger_hash ] ) ) {
			return false;
		}

		$hook_args = array(
			'trigger_code' => $trigger_code,
			'action_hook'  => $action_hook,
			'trigger_hash' => $trigger_hash,
		);

		do_action( 'automator_enqueue_trigger_before_enqueue_trigger', $hook_args );

		$this->processed_hashes[ $trigger_hash ] = true;

		$this->memory_queue[] = array(
			'hash'         => $trigger_hash,
			'trigger_code' => $trigger_code,
			'action_hook'  => $action_hook,
			'args'         => $args,
			'user_id'      => get_current_user_id(),
			'post_id'      => get_the_ID(),
			'timestamp'    => time(),
		);

		$this->ensure_shutdown_hooks();

		return true;
	}

	/**
	 * Process items from database (failed items from previous requests)
	 *
	 * @return void
	 */
	public function process_database_queue() {

		global $wpdb;
		$table_name = $wpdb->prefix . self::TABLE_NAME;

		$items = $wpdb->get_results(
			$wpdb->prepare(
				'SELECT * FROM `' . esc_sql( $table_name ) . '` ORDER BY created_at ASC LIMIT %d',
				5
			),
			ARRAY_A
		);

		if ( empty( $items ) ) {
			return;
		}

		foreach ( $items as $row ) {
			try {
				$trigger_data = $this->hook_arguments_packager->unpack( $row['trigger_data'] );
				if ( ! $trigger_data ) {
					$wpdb->delete( $table_name, array( 'id' => $row['id'] ), array( '%d' ) );
					continue;
				}

				$item = $trigger_data->get_args();

				// **QUEUE** it for the normal in-memory run, don’t fire now.
				$this->memory_queue[] = $item;

				// remove from DB so we don’t replay next request
				$wpdb->delete( $table_name, array( 'id' => $row['id'] ), array( '%d' ) );

				$this->ensure_shutdown_hooks();

			} catch ( \Error $e ) {
				automator_log( 'Fast Queue DB error: ' . $e->getMessage(), 'fast-automator-queue' );
				$wpdb->delete( $table_name, array( 'id' => $row['id'] ), array( '%d' ) );
			}
		}
	}

	/**
	 * Process the in-memory queue. Idempotent.
	 *
	 * @return void
	 */
	public function process_queue() {

		if ( $this->queue_drained || empty( $this->memory_queue ) ) {
			return;
		}

		$this->queue_drained = true;

		foreach ( $this->memory_queue as $item ) {
			$this->process_trigger_item( $item );
		}

		$this->memory_queue = array();
	}

	/**
	 * Process a single queued item.
	 *
	 * @param array $item
	 * @return void
	 */
	private function process_trigger_item( $item ) {

		$hook_args = array( 'item' => $item );
		do_action( 'automator_enqueue_trigger_before_process_trigger_item', $hook_args );

		$trigger_obj = Automator()->get_trigger( $item['trigger_code'] );

		$this->execute_trigger( $trigger_obj, $item );

	}

	/**
	 * Execute one queued trigger with proper hook context.
	 *
	 * @param array $trigger_obj
	 * @param array $item
	 * @return void
	 */
	private function execute_trigger( $trigger_obj, $item ) {

		$hook   = $item['action_hook'] ?? '';
		$method = $trigger_obj['validation_function'] ?? null;

		if ( empty( $hook ) || ! is_callable( $method ) ) {
			return;
		}

		$args = (array) ( $item['args'] ?? array() );
		$num  = count( $args );

		add_action( $hook, $method, self::ONE_OFF_PRIORITY, $num );

		try {

			do_action_ref_array( $hook, $args );

		} catch ( \Throwable $e ) {

			$this->log( "Fast Queue: Error executing trigger: {$e->getMessage()}" );

		}

		remove_action( $hook, $method, self::ONE_OFF_PRIORITY );
	}

	/**
	 * Helper to remove an item from memory queue by hash.
	 *
	 * @param string $hash
	 * @return void
	 */
	private function memory_queue_remove( $hash ) {

		foreach ( $this->memory_queue as $key => $entry ) {
			if ( $entry['hash'] === $hash ) {
				unset( $this->memory_queue[ $key ] );
				break;
			}
		}
	}

	/**
	 * Generate consistent hash for deduplication
	 *
	 * @param string $trigger_code
	 * @param array  $args
	 * @return string
	 */
	private function generate_trigger_hash( $trigger_code, $args ) {

		$hash_data = array(
			'trigger_code'   => $trigger_code,
			'user_id'        => get_current_user_id(),
			'post_id'        => get_the_ID(),
			'time_window'    => floor( time() / 3 ),
			'trigger_args'   => $args,
			'args_signature' => $this->get_args_signature( $args ),
		);

		return md5( wp_json_encode( $hash_data ) );
	}

	/**
	 * Create a lightweight signature of arguments without full object data
	 *
	 * @param array $args
	 * @return array
	 */
	private function get_args_signature( $args ) {

		$signature = array();

		foreach ( $args as $index => $arg ) {
			if ( is_object( $arg ) ) {
				$signature[ $index ] = array(
					'type'  => 'object',
					'class' => get_class( $arg ),
					'id'    => $this->extract_object_id( $arg ),
				);
			} elseif ( is_array( $arg ) ) {
				$signature[ $index ] = array(
					'type'  => 'array',
					'count' => count( $arg ),
					'keys'  => array_slice( array_keys( $arg ), 0, 3 ),
				);
			} else {
				$signature[ $index ] = $arg;
			}
		}

		return $signature;
	}

	/**
	 * Extract ID from common WordPress objects
	 *
	 * @param mixed $any_object
	 * @return int|null
	 */
	private function extract_object_id( $any_object ) {
		if ( isset( $any_object->ID ) ) {
			return $any_object->ID;
		}
		if ( isset( $any_object->id ) ) {
			return $any_object->id;
		}
		if ( method_exists( $any_object, 'get_id' ) ) {
			return $any_object->get_id();
		}
		return null;
	}

	/**
	 * Save failed items to database for later retry.
	 *
	 * @param array  $item
	 * @param string $error_message
	 * @return bool
	 */
	private function save_failed_item( $item, $error_message ) {

		global $wpdb;

		$table_name = $wpdb->prefix . self::TABLE_NAME;

		$existing = $wpdb->get_var(
			$wpdb->prepare(
				'SELECT id FROM `' . esc_sql( $table_name ) . '` WHERE trigger_hash = %s',
				$item['hash']
			)
		);

		if ( $existing ) {
			return false;
		}

		$payload = $this->hook_arguments_packager->package( $item );
		if ( ! $payload || strlen( $payload ) > self::MAX_PAYLOAD_SIZE ) {
			return false;
		}

		$res = $wpdb->insert(
			$table_name,
			array(
				'trigger_hash' => $item['hash'],
				'trigger_code' => $item['trigger_code'],
				'trigger_data' => $payload,
			),
			array( '%s', '%s', '%s' )
		);

		return (bool) $res;
	}

	/**
	 * Register cleanup hooks
	 *
	 * @return void
	 */
	private function register_cleanup() {

		add_action( 'automator_cleanup_fast_queue', array( $this, 'cleanup_old_items' ) );

		if ( ! wp_next_scheduled( 'automator_cleanup_fast_queue' ) ) {
			wp_schedule_event( time(), 'daily', 'automator_cleanup_fast_queue' );
		}
	}

	/**
	 * Clean up old database items
	 *
	 * @return void
	 */
	public function cleanup_old_items() {

		global $wpdb;

		$table_name = $wpdb->prefix . self::TABLE_NAME;
		$wpdb->query(
			'DELETE FROM `' . esc_sql( $table_name ) . '` WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)'
		);
	}

	/**
	 * Get queue statistics for debugging
	 *
	 * @return array
	 */
	public function get_stats() {

		global $wpdb;

		$table_name = $wpdb->prefix . self::TABLE_NAME;
		$stats      = $wpdb->get_row(
			$wpdb->prepare(
				'SELECT
                    COUNT(*) AS total_failed,
                    COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) AS failed_last_hour
                FROM `' . esc_sql( $table_name ) . '` WHERE 1 = %d',
				1
			),
			OBJECT
		);

		return array(
			'memory_queue_size'   => count( $this->memory_queue ),
			'database_queue_size' => (int) $stats->total_failed,
			'failed_last_hour'    => (int) $stats->failed_last_hour,
			'processed_hashes'    => count( $this->processed_hashes ),
		);
	}

	/**
	 * Log a message to the debug log
	 *
	 * @param string $message
	 * @return void
	 */
	private function log( $message ) {

		if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
			automator_log( $message, 'fast-automator-queue', true, 'fast-automator-queue' );
		}
	}

	private function ensure_shutdown_hooks() {
		if ( $this->shutdown_registered ) {
			return;
		}

		// 1. Drain any backlog from the DB *before* MemberPress' init.
		add_action( 'plugins_loaded', [ $this, 'process_database_queue' ], 0 );
		add_action( 'plugins_loaded', [ $this, 'process_queue' ], -9999 ); // runs before every normal priority

		// 2. Drain what was queued during this request (normal pages).
		add_action( 'wp_loaded', [ $this, 'process_queue' ], 1 );

		// 3. Absolute last-chance drain (fatal or redirect).
		register_shutdown_function( [ $this, 'process_queue' ] );

		$this->shutdown_registered = true;
	}

}
