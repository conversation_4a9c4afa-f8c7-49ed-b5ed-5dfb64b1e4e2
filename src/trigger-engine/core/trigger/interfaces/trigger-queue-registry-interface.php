<?php

namespace Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces;

/**
 * Simple event system interface - just handles hook registration and event publishing
 *
 * This replaces the "actionify_triggers" mess with clean event handling
 */
interface Trigger_Queue_Registry_Interface {

	/**
	 * Register WordPress hooks for automation triggers
	 */
	public function register_automation_hooks(): void;

	/**
	 * Publish event when WordPress hook fires
	 */
	public function publish_event( string $hook_name, array $args ): void;

	/**
	 * Get system stats for debugging
	 */
	public function get_stats(): array;
}
