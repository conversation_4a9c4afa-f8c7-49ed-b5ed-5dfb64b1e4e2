<?php

namespace Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces;

/**
 * Trigger Discovery Interface
 *
 * This interface defines methods for discovering active automation triggers
 * from the WordPress database. It abstracts away the WordPress post queries
 * behind a clean interface, allowing for different implementations or mocks
 * in testing environments.
 *
 * @package Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces
 * @since 6.7
 */
interface Trigger_Discovery_Interface {

	/**
	 * Get all active trigger configurations.
	 *
	 * Retrieves all active triggers from the data store and returns them
	 * in a structured format for the event system.
	 *
	 * @return array<string,mixed> Array of active triggers indexed by trigger code.
	 */
	public function get_active_triggers(): array;

	/**
	 * Check if a specific trigger is active.
	 *
	 * Determines whether a trigger with the given code exists and is active.
	 *
	 * @param string $trigger_code The trigger code to check.
	 *
	 * @return bool True if the trigger is active, false otherwise.
	 */
	public function is_trigger_active( string $trigger_code ): bool;
}
