<?php

namespace Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces;

/**
 * Trigger Queue Interface
 *
 * This interface defines methods for queueing and processing automation trigger events.
 * It provides a clean abstraction for the event processing system, allowing for
 * different queue implementations (memory, database, external services) while
 * maintaining a consistent interface.
 *
 * @package Uncanny_Automator\Trigger_Engine\Core\Trigger\Interfaces
 * @since 6.7
 */
interface Trigger_Queue_Interface {

	/**
	 * Queue an event for processing.
	 *
	 * Adds a trigger event to the queue for later processing. The implementation
	 * should handle deduplication and validation.
	 *
	 * @param string $trigger_code The trigger code to process.
	 * @param string $hook_name The WordPress hook that triggered this event.
	 * @param array<int,mixed> $args The arguments passed to the hook.
	 * 
	 * @return bool True if the event was queued, false if it was rejected (e.g., duplicate).
	 */
	public function queue_event( string $trigger_code, string $hook_name, array $args ): bool;

	/**
	 * Process all queued events.
	 * 
	 * Processes all events in the queue. This method should be idempotent
	 * and safe to call multiple times.
	 *
	 * @return void
	 */
	public function process_queue(): void;

	/**
	 * Get queue statistics for monitoring and debugging.
	 *
	 * @return array<string,int> An array of queue statistics.
	 */
	public function get_stats(): array;
}
