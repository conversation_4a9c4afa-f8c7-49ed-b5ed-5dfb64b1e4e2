(()=>{"use strict";var e={977:(e,t,r)=>{function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},s.apply(this,arguments)}r.d(t,{Z:()=>l});var n=r(307);const l=e=>{let{width:t=24,height:r=24,className:l="mp-icon",...o}=e;return(0,n.createElement)("svg",s({className:l,width:t,height:r,clipRule:"evenodd",fillRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:"1.41421",viewBox:"0 0 208 115",xmlns:"http://www.w3.org/2000/svg"},o),(0,n.createElement)("g",{fillRule:"nonzero"},(0,n.createElement)("path",{d:"m136.03 50.254c0-11.712 9.528-21.24 21.24-21.24 11.711 0 21.239 9.528 21.239 21.24v50.28c0 7.989 6.474 14.463 14.463 14.463 7.988 0 14.463-6.474 14.463-14.463v-50.28c0-27.66-22.505-50.166-50.165-50.166-13.96 0-26.601 5.738-35.704 14.971 8.934 9.063 14.461 21.494 14.461 35.195z",fill:"#46c1c2"}),(0,n.createElement)("path",{d:"m107.104 50.254c0-13.701 5.527-26.132 14.462-35.195-9.104-9.233-21.745-14.971-35.704-14.971-13.958 0-26.597 5.736-35.7 14.967 8.913 9.038 14.433 21.429 14.463 35.088.06-11.661 9.562-21.129 21.237-21.129 11.711 0 21.239 9.528 21.239 21.24z",fill:"#3bc3d5"}),(0,n.createElement)("path",{d:"m136.027 100.534v-50.28c0-13.701-5.527-26.132-14.461-35.195-8.935 9.063-14.462 21.494-14.462 35.195v50.283c0 3.994 1.618 7.609 4.235 10.226 2.617 2.616 6.232 4.234 10.225 4.234 7.988 0 14.463-6.474 14.463-14.463z",fill:"#14a9b2"}),(0,n.createElement)("path",{d:"m14.462.088c-7.988 0-14.462 6.475-14.462 14.463 0 7.989 6.474 14.463 14.462 14.463 11.675 0 21.177 9.468 21.237 21.129.03-13.659 5.55-26.05 14.463-35.088-9.103-9.231-21.742-14.967-35.7-14.967z",fill:"#1482c5"}),(0,n.createElement)("path",{d:"m35.699 50.143c0 .037.003.074.003.111v50.28c0 7.989 6.474 14.463 14.463 14.463 2.494 0 4.841-.631 6.889-1.743 4.508-2.449 7.568-7.225 7.568-12.717v-50.283c0-.037.003-.074.003-.111-.03-13.659-5.55-26.05-14.463-35.088-8.913 9.038-14.433 21.429-14.463 35.088z",fill:"#0f6cb2"}),(0,n.createElement)("path",{d:"m28.907 14.542c0 7.983-6.471 14.454-14.454 14.454-7.982 0-14.453-6.471-14.453-14.454 0-7.982 6.471-14.453 14.453-14.453 7.983 0 14.454 6.471 14.454 14.453z",fill:"#164699"}),(0,n.createElement)("path",{d:"m64.603 100.546c0 7.983-6.471 14.454-14.453 14.454-7.983 0-14.454-6.471-14.454-14.454 0-7.982 6.471-14.453 14.454-14.453 7.982 0 14.453 6.471 14.453 14.453z",fill:"#0a579b"}),(0,n.createElement)("path",{d:"m136.011 100.546c0 7.983-6.472 14.454-14.453 14.454-7.983 0-14.454-6.471-14.454-14.454 0-7.982 6.471-14.453 14.454-14.453 7.981 0 14.453 6.471 14.453 14.453z",fill:"#108e9f"}),(0,n.createElement)("path",{d:"m207.435 100.546c0 7.983-6.471 14.454-14.454 14.454-7.982 0-14.453-6.471-14.453-14.454 0-7.982 6.471-14.453 14.453-14.453 7.983 0 14.454 6.471 14.454 14.453z",fill:"#14aba3"})))}},984:(e,t,r)=>{r.d(t,{Z:()=>o});var s=r(307),n=r(609),l=r(977);const o=e=>{let{icon:t,label:r,instructions:o,children:a,iconClass:i="mp-icon-placeholder"}=e;return(0,s.createElement)(n.Placeholder,{className:"mp-placeholder",icon:t,label:(0,s.createElement)("div",null,(0,s.createElement)(l.Z,{className:i}),r),instructions:o},a)}},541:(e,t,r)=>{var s=r(307),n=r(981),l=r(977),o=r(379),a=r.n(o),i=r(795),m=r.n(i),c=r(569),p=r.n(c),u=r(565),d=r.n(u),b=r(216),g=r.n(b),h=r(589),_=r.n(h),f=r(140),v={};v.styleTagTransform=_(),v.setAttributes=d(),v.insert=p().bind(null,"head"),v.domAPI=m(),v.insertStyleElement=g(),a()(f.Z,v),f.Z&&f.Z.locals&&f.Z.locals,(0,n.setCategories)([...(0,n.getCategories)().filter((e=>{let{slug:t}=e;return"memberpress"!==t})),{slug:"memberpress",title:"MemberPress",icon:(0,s.createElement)(l.Z,null)}])},585:(e,t,r)=>{var s=r(307),n=r(984);const{registerBlockType:l}=wp.blocks,{__}=wp.i18n;l("memberpress/account-form",{title:__("Account Form","memberpress"),icon:"excerpt-view",category:"memberpress",description:__("Display the MemberPress Account form.","memberpress"),keywords:[__("membership account form","memberpress")],attributes:{},supports:{customClassName:!1,html:!1},edit:function(e){let{className:t}=e;return(0,s.createElement)("div",{className:t},(0,s.createElement)(n.Z,{icon:"excerpt-view",label:__("MemberPress Account Form","memberpress"),instructions:__("Display the MemberPress Account form","memberpress")}))},save:function(){return null}})},859:(e,t,r)=>{var s=r(307),n=r(736),l=r(175),o=r(609),a=r(423),i=r.n(a),m=r(818);const{registerBlockType:c}=wp.blocks,{__}=wp.i18n;c("memberpress/account-info",{title:__("Account Info","memberpress"),icon:"admin-users",category:"memberpress",description:__("Display the user meta field, which is chosen by slug.","memberpress"),keywords:[__("membership user info","memberpress")],supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const a=(0,l.useBlockProps)(),{field:c}=t,{fields:p}=(0,m.useSelect)((e=>{let t=window.memberpressBlocks.custom_fields;return{fields:[{label:"full_name",value:"full_name"},{label:"full_name_last_first",value:"full_name_last_first"},{label:"full_name_last_initial",value:"full_name_last_initial"},{label:"last_name_first_initial",value:"last_name_first_initial"},{label:"first_name",value:"first_name"},{label:"last_name",value:"last_name"},{label:"user_login",value:"user_login"},{label:"user_email",value:"user_email"},{label:"nickname",value:"nickname"},{label:"description",value:"description"},{label:"mepr-address-one",value:"mepr-address-one"},{label:"mepr-address-two",value:"mepr-address-two"},{label:"mepr-address-city",value:"mepr-address-city"},{label:"mepr-address-state",value:"mepr-address-state"},{label:"mepr-address-zip",value:"mepr-address-zip"},{label:"mepr-address-country",value:"mepr-address-country"},{label:"mepr_user_message",value:"mepr_user_message"},{label:"user_registered",value:"user_registered"},{label:"display_name",value:"display_name"},{label:"ID",value:"ID"}].concat(t)}}));return(0,s.createElement)("div",a,(0,s.createElement)(l.InspectorControls,null,(0,s.createElement)(o.PanelBody,{title:"MemberPress Account Info",initialOpen:!0},(0,s.createElement)(o.SelectControl,{label:(0,n.__)("Field Slug:","memberpress"),value:c,multiple:!1,options:p,onChange:e=>r({field:e})}))),(0,s.createElement)(o.Disabled,null,(0,s.createElement)(i(),{block:"memberpress/account-info",attributes:{field:c}})))},save:function(){return null}})},727:(e,t,r)=>{var s=r(307),n=r(609),l=r(175),o=r(423),a=r.n(o);const{registerBlockType:i}=wp.blocks,{__}=wp.i18n;i("memberpress/account-links",{title:__("Account Links","memberpress"),icon:"editor-ul",category:"memberpress",description:__("Display the list of MemberPress Account links.","memberpress"),keywords:[__("membership account links","memberpress")],attributes:{},supports:{customClassName:!1,html:!1},edit:function(){const e=(0,l.useBlockProps)();return(0,s.createElement)("div",e,(0,s.createElement)(n.Disabled,null,(0,s.createElement)(a(),{block:"memberpress/account-links"})))},save:function(){return null}})},149:(e,t,r)=>{var s=r(307),n=r(736),l=r(175),o=r(609),a=r(423),i=r.n(a),m=r(379),c=r.n(m),p=r(795),u=r.n(p),d=r(569),b=r.n(d),g=r(565),h=r.n(g),_=r(216),f=r.n(_),v=r(589),w=r.n(v),y=r(772),E={};E.styleTagTransform=w(),E.setAttributes=h(),E.insert=b().bind(null,"head"),E.domAPI=u(),E.insertStyleElement=f(),c()(y.Z,E),y.Z&&y.Z.locals&&y.Z.locals;const{registerBlockType:k}=wp.blocks,{__}=wp.i18n;k("memberpress/login-form",{title:__("Login Form","memberpress"),icon:"admin-network",category:"memberpress",description:__("Display the MemberPress Login form","memberpress"),keywords:[__("membership login form","memberpress")],attributes:{use_redirect:{type:"boolean"}},supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const a=(0,l.useBlockProps)(),{use_redirect:m}=t;return(0,s.createElement)("div",a,(0,s.createElement)(l.InspectorControls,null,(0,s.createElement)(o.PanelBody,{title:"MemberPress Login Form",initialOpen:!0},(0,s.createElement)(o.ToggleControl,{label:(0,n.__)("Login Redirect URL?","memberpress"),checked:m,onChange:()=>r({use_redirect:!m})}))),(0,s.createElement)(o.Disabled,null,(0,s.createElement)(i(),{block:"memberpress/login-form",attributes:{use_redirect:m}})))},save:function(){return null}})},458:(e,t,r)=>{var s=r(307),n=r(736),l=r(175),o=r(609),a=r(423),i=r.n(a),m=r(818),c=r(379),p=r.n(c),u=r(795),d=r.n(u),b=r(569),g=r.n(b),h=r(565),_=r.n(h),f=r(216),v=r.n(f),w=r(589),y=r.n(w),E=r(886),k={};k.styleTagTransform=y(),k.setAttributes=_(),k.insert=g().bind(null,"head"),k.domAPI=d(),k.insertStyleElement=v(),p()(E.Z,k),E.Z&&E.Z.locals&&E.Z.locals;const{registerBlockType:C}=wp.blocks,{__}=wp.i18n;C("memberpress/membership-signup",{title:__("Registration","memberpress"),icon:"groups",category:"memberpress",description:__("Display a signup form for a MemberPress membership.","memberpress"),keywords:[__("membership signup form","memberpress")],attributes:{membership:{type:"string"}},supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const a=(0,l.useBlockProps)(),{membership:c}=t,{options:p}=(0,m.useSelect)((e=>{let t=window.memberpressBlocks.memberships;return{options:[{label:(0,n.__)("-- Select a Membership","memberpress"),value:""}].concat(t)}}));return(0,s.createElement)("div",a,(0,s.createElement)(l.InspectorControls,null,(0,s.createElement)(o.PanelBody,{title:"MemberPress Signup Form",initialOpen:!0},(0,s.createElement)(o.SelectControl,{label:(0,n.__)("Select a Membership registration form to display","memberpress"),multiple:!1,value:c,options:p,onChange:e=>{r({membership:e})}}))),(0,s.createElement)(o.Disabled,null,(0,s.createElement)(i(),{block:"memberpress/membership-signup",attributes:{membership:c}})))},save:function(){return null}})},703:(e,t,r)=>{var s=r(981),n=r(307),l=r(175),o=r(609),a=r(423),i=r.n(a);r(818);const m=["image"],{__}=wp.i18n;var c=r(736),p=r(379),u=r.n(p),d=r(795),b=r.n(d),g=r(569),h=r.n(g),_=r(565),f=r.n(_),v=r(216),w=r.n(v),y=r(589),E=r.n(y),k=r(697),C={};C.styleTagTransform=E(),C.setAttributes=f(),C.insert=h().bind(null,"head"),C.domAPI=b(),C.insertStyleElement=w(),u()(k.Z,C),k.Z&&k.Z.locals&&k.Z.locals,(0,s.registerBlockType)("memberpress/pro-account-tabs",{title:(0,c.__)("MP ReadyLaunch™ Account","memberpress"),icon:"open-folder",category:"memberpress",description:(0,c.__)("MemberPress Account Tabs.","memberpress"),keywords:[(0,c.__)("membership account form","memberpress")],supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const s=(0,l.useBlockProps)({className:"alignwide wp-block"}),{show_welcome_image:a,welcome_image:c}=t,p=e=>{r({welcome_image:e.url})},u=(0,n.createElement)("p",null,__("To edit the Welcome image, you need permission to upload media.","image-selector-example"));return(0,n.createElement)("div",s,(0,n.createElement)(l.InspectorControls,null,(0,n.createElement)(o.PanelBody,{title:"Options",initialOpen:!0},(0,n.createElement)(o.ToggleControl,{label:"Show Welcome Image",checked:a,onChange:()=>r({show_welcome_image:!a})}),a&&(0,n.createElement)("div",{className:"editor-post-featured-image"},(0,n.createElement)(l.MediaUploadCheck,{fallback:u},(0,n.createElement)(l.MediaUpload,{title:__("Welcome image","image-selector-example"),onSelect:p,allowedTypes:m,value:c,render:e=>{let{open:t}=e;return(0,n.createElement)("div",{className:"editor-post-featured-image__container"},(0,n.createElement)(o.Button,{className:c?"editor-post-featured-image__preview":"editor-post-featured-image__toggle",onClick:t},!c&&__("Set Welcome image","image-selector-example"),!!c&&(0,n.createElement)(o.ResponsiveWrapper,{naturalWidth:2e3,naturalHeight:2e3,isInline:!0},(0,n.createElement)("img",{className:"mepr-editor-media-preview-img",src:c,alt:__("Welcome image","image-selector-example")}))))}})),!!c&&(0,n.createElement)(l.MediaUploadCheck,null,(0,n.createElement)(l.MediaUpload,{title:__("Welcome image","image-selector-example"),onSelect:p,allowedTypes:m,value:c,render:e=>{let{open:t}=e;return(0,n.createElement)(o.Button,{onClick:t,isDefault:!0,isLarge:!0},__("Replace Welcome Image","image-selector-example"))}})),!!c&&(0,n.createElement)(l.MediaUploadCheck,null,(0,n.createElement)(o.Button,{onClick:()=>{r({welcome_image:""})},className:"",isLink:!0,isDestructive:!0},__("Remove Welcome Image","image-selector-example")))))),(0,n.createElement)(o.Disabled,null,(0,n.createElement)(i(),{block:"memberpress/pro-account-tabs",attributes:{welcome_image:c,show_welcome_image:a}})))},save:function(){return null}})},691:(e,t,r)=>{var s=r(981),n=r(307),l=r(175),o=r(609),a=r(423),i=r.n(a),m=r(818);const{__}=wp.i18n;var c=r(736),p=r(379),u=r.n(p),d=r(795),b=r.n(d),g=r(569),h=r.n(g),_=r(565),f=r.n(_),v=r(216),w=r.n(v),y=r(589),E=r.n(y),k=r(801),C={};C.styleTagTransform=E(),C.setAttributes=f(),C.insert=h().bind(null,"head"),C.domAPI=b(),C.insertStyleElement=w(),u()(k.Z,C),k.Z&&k.Z.locals&&k.Z.locals,(0,s.registerBlockType)("memberpress/checkout",{title:(0,c.__)("MP ReadyLaunch™ Registration","memberpress"),icon:"cart",category:"memberpress",description:(0,c.__)("MemberPress Checkout","memberpress"),keywords:[(0,c.__)("membership account form","memberpress")],supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const s=(0,l.useBlockProps)({className:"wp-block"}),{membership_id:a,show_title:c,button_highlight_color:p}=t,{options:u}=(0,m.useSelect)((e=>{let t=window.memberpressBlocks.memberships;return{options:[{label:__("Select Membership","memberpress"),value:""}].concat(t)}}));return(0,n.createElement)("div",s,(0,n.createElement)(l.InspectorControls,null,(0,n.createElement)(o.PanelBody,{title:"Options",initialOpen:!0},(0,n.createElement)(o.SelectControl,{label:"Membership",value:a,options:u,onChange:e=>{r({membership_id:e})},__nextHasNoMarginBottom:!0}))),(0,n.createElement)(o.Disabled,null,(0,n.createElement)(i(),{block:"memberpress/checkout",attributes:{membership_id:a}})))},save:function(){return null}})},94:(e,t,r)=>{var s=r(981),n=r(379),l=r.n(n),o=r(795),a=r.n(o),i=r(569),m=r.n(i),c=r(565),p=r.n(c),u=r(216),d=r.n(u),b=r(589),g=r.n(b),h=r(289),_={};_.styleTagTransform=g(),_.setAttributes=p(),_.insert=m().bind(null,"head"),_.domAPI=a(),_.insertStyleElement=d(),l()(h.Z,_),h.Z&&h.Z.locals&&h.Z.locals;var f=r(736),v=r(307),w=r(175),y=r(609),E=r(423),k=r.n(E);const C=["image"],{__}=wp.i18n;(0,s.registerBlockType)("memberpress/pro-login-form",{title:(0,f.__)("MP ReadyLaunch™ Login","memberpress"),icon:"admin-network",category:"memberpress",description:(0,f.__)("MemberPress Checkout","memberpress"),supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const s=(0,w.useBlockProps)(),{show_welcome_image:n,welcome_image:l}=t,o=e=>{r({welcome_image:e.url})},a=(0,v.createElement)("p",null,__("To edit the Welcome image, you need permission to upload media.","image-selector-example"));return(0,v.createElement)("div",s,(0,v.createElement)(w.InspectorControls,null,(0,v.createElement)(y.PanelBody,{title:"MemberPress Login Options",initialOpen:!0},(0,v.createElement)(y.ToggleControl,{label:"Show Welcome Image",checked:n,onChange:()=>r({show_welcome_image:!n})}),n&&(0,v.createElement)("div",{className:"editor-post-featured-image"},(0,v.createElement)(w.MediaUploadCheck,{fallback:a},(0,v.createElement)(w.MediaUpload,{title:__("Welcome image","image-selector-example"),onSelect:o,allowedTypes:C,value:l,render:e=>{let{open:t}=e;return(0,v.createElement)("div",{className:"editor-post-featured-image__container"},(0,v.createElement)(y.Button,{className:l?"editor-post-featured-image__preview":"editor-post-featured-image__toggle",onClick:t},!l&&__("Set Welcome image","image-selector-example"),!!l&&(0,v.createElement)(y.ResponsiveWrapper,{naturalWidth:2e3,naturalHeight:2e3,isInline:!0},(0,v.createElement)("img",{className:"mepr-editor-login-preview-img",src:l,alt:__("Welcome image","image-selector-example")}))))}})),!!l&&(0,v.createElement)(w.MediaUploadCheck,null,(0,v.createElement)(w.MediaUpload,{title:__("Welcome image","image-selector-example"),onSelect:o,allowedTypes:C,value:l,render:e=>{let{open:t}=e;return(0,v.createElement)(y.Button,{onClick:t,isDefault:!0,isLarge:!0},__("Replace Welcome Image","image-selector-example"))}})),!!l&&(0,v.createElement)(w.MediaUploadCheck,null,(0,v.createElement)(y.Button,{className:"",onClick:()=>{r({welcome_image:void 0})},isLink:!0,isDestructive:!0},__("Remove Welcome Image","image-selector-example")))))),(0,v.createElement)(y.Disabled,null,(0,v.createElement)(k(),{block:"memberpress/pro-login-form",attributes:{welcome_image:l,show_welcome_image:n,admin_view:1}})))},save:function(){return null}})},613:(e,t,r)=>{var s=r(981),n=r(736),l=r(307),o=r(175),a=r(609),i=r(423),m=r.n(i),c=r(818);(0,s.registerBlockType)("memberpress/pro-pricing-table",{title:(0,n.__)("MP ReadyLaunch™ Pricing Table","memberpress"),icon:"money-alt",category:"memberpress",description:(0,n.__)("MemberPress Pricing Table Options","memberpress"),supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const s=(0,o.useBlockProps)(),{group_id:i,show_title:p,button_highlight_color:u}=t,{options:d}=(0,c.useSelect)((e=>{let t=window.memberpressBlocks.groups;return{options:[{label:(0,n.__)("Select Group","memberpress"),value:""}].concat(t)}}));return(0,l.createElement)("div",s,(0,l.createElement)(o.InspectorControls,null,(0,l.createElement)(a.PanelBody,{title:"Options",initialOpen:!0},(0,l.createElement)(a.SelectControl,{label:(0,n.__)("Group","memberpress"),value:i,options:d,onChange:e=>{r({group_id:e})},__nextHasNoMarginBottom:!0}),(0,l.createElement)(a.ToggleControl,{label:(0,n.__)("Show Global Pricing Headline","memberpress"),checked:p,onChange:()=>r({show_title:!p})}),(0,l.createElement)(a.Flex,{direction:"column"},(0,l.createElement)(a.__experimentalText,null,"Button Highlight Color"),(0,l.createElement)(a.ColorPicker,{label:"Show Title",color:u,onChange:e=>r({button_highlight_color:e}),defaultValue:"#EF1010"})))),(0,l.createElement)(m(),{block:"memberpress/pro-pricing-table",attributes:{show_title:p,button_highlight_color:u,group_id:i}}))},save:function(){return null}})},449:(e,t,r)=>{var s=r(307),n=r(984),l=r(379),o=r.n(l),a=r(795),i=r.n(a),m=r(569),c=r.n(m),p=r(565),u=r.n(p),d=r(216),b=r.n(d),g=r(589),h=r.n(g),_=r(225),f={};f.styleTagTransform=h(),f.setAttributes=u(),f.insert=c().bind(null,"head"),f.domAPI=i(),f.insertStyleElement=b(),o()(_.Z,f),_.Z&&_.Z.locals&&_.Z.locals;const{registerBlockType:v}=wp.blocks,{__}=wp.i18n,{InspectorControls:w,InnerBlocks:y}=wp.blockEditor,{PanelBody:E,PanelRow:k,RadioControl:C,SelectControl:x,TextareaControl:P}=wp.components,{rules:S,disabled_blocks:M}=memberpressBlocks;M.includes("protected-content")||v("memberpress/protected-content",{title:__("Protected","memberpress"),description:__("Layout blocks and content protected by MemberPress.","memberpress"),icon:"lock",category:"memberpress",keywords:[__("login protected membership hidden rule","memberpress")],supports:{customClassName:!1,html:!1},attributes:{rule:{type:"string"},ifallowed:{type:"string"},unauth:{type:"string"},unauth_message:{type:"string"}},edit(e){let{attributes:t,setAttributes:r,className:l}=e;const{rule:o,ifallowed:a,unauth:i,unauth_message:m}=t,c=S.filter((e=>e.value===parseInt(o))).pop();return(0,s.createElement)(s.Fragment,null,(0,s.createElement)(w,null,(0,s.createElement)(E,{title:__("Access Rule","memberpress")},(0,s.createElement)(k,null,(0,s.createElement)(x,{value:o||"",help:__("Select a Rule to determine member access.","memberpress"),options:[{label:__("Select a Rule","memberpress"),value:""},...S],onChange:e=>{r({rule:e})}})),c&&""!=c.ruleLink&&(0,s.createElement)("div",null,(0,s.createElement)(k,null,(0,s.createElement)("a",{href:c.ruleLink,target:"_blank"},__("View Rule","memberpress")))),(0,s.createElement)(k,null,(0,s.createElement)(C,{label:__("If Allowed","memberpress"),help:__('When set to "show", the content is shown to authorized members only. When set to "hide", the content is hidden from authorized members.',"memberpress"),selected:a||"show",options:[{label:__("Show","memberpress"),value:"show"},{label:__("Hide","memberpress"),value:"hide"}],onChange:e=>{r({ifallowed:e})}}))),(0,s.createElement)(E,{title:__("Unauthorized Access","memberpress"),initialOpen:!1},(0,s.createElement)(k,null,(0,s.createElement)(x,{label:__("Unauthorized Action","memberpress"),value:i||"",help:__("Specify how to handle unauthorized access.","memberpress"),options:[{label:__("Hide Only","memberpress"),value:""},{label:__("Show Message","memberpress"),value:"message"},{label:__("Show Login Form","memberpress"),value:"login"},{label:__("Show Login Form & Message","memberpress"),value:"both"}],onChange:e=>{r({unauth:e})}})),i&&("message"===i||"both"===i)&&(0,s.createElement)(k,null,(0,s.createElement)(P,{label:__("Unauthorized Message","memberpress"),help:__("Message to show on Unauthorized Access","memberpress"),value:m||"",onChange:e=>{r({unauth_message:e})}})))),(0,s.createElement)("div",{className:l},(0,s.createElement)(n.Z,{icon:"lock",label:__("MemberPress Protected Content","memberpress"),instructions:__("Add child blocks that will only be shown to authorized members.","memberpress")}),(0,s.createElement)(y,null)))},save:()=>(0,s.createElement)("div",null,(0,s.createElement)(y.Content,null))})},138:(e,t,r)=>{var s=r(307),n=r(736),l=r(175),o=r(609),a=r(818),i=r(423),m=r.n(i),c=r(379),p=r.n(c),u=r(795),d=r.n(u),b=r(569),g=r.n(b),h=r(565),_=r.n(h),f=r(216),v=r.n(f),w=r(589),y=r.n(w),E=r(574),k={};k.styleTagTransform=y(),k.setAttributes=_(),k.insert=g().bind(null,"head"),k.domAPI=d(),k.insertStyleElement=v(),p()(E.Z,k),E.Z&&E.Z.locals&&E.Z.locals;const{registerBlockType:C}=wp.blocks,{__}=wp.i18n;C("memberpress/subscriptions",{title:__("Subscriptions","memberpress"),icon:"groups",category:"memberpress",description:__("Display the subscriptions list of currently logged-in user.","memberpress"),keywords:[__("membership subscriptions","memberpress")],supports:{customClassName:!1,html:!1},edit:function(e){let{attributes:t,setAttributes:r}=e;const i=(0,l.useBlockProps)(),{order_by:c,order:p,not_logged_in_message:u,no_subscriptions_message:d,top_description:b,bottom_description:g,use_access_url:h}=t,{orderByOptions:_}=(0,a.useSelect)((e=>({orderByOptions:[{label:(0,n.__)("-- Sort by","memberpress"),value:""},{label:(0,n.__)("Date","memberpress"),value:"date"},{label:(0,n.__)("Title","memberpress"),value:"title"}]}))),{orderOptions:f}=(0,a.useSelect)((e=>({orderOptions:[{label:(0,n.__)("-- Sort order","memberpress"),value:""},{label:(0,n.__)("Ascending","memberpress"),value:"asc"},{label:(0,n.__)("Descending","memberpress"),value:"desc"}]})));return(0,s.createElement)("div",i,(0,s.createElement)(l.InspectorControls,null,(0,s.createElement)(o.PanelBody,{title:"MemberPress Subscriptions",initialOpen:!0},(0,s.createElement)(o.SelectControl,{label:(0,n.__)("Sort by:","memberpress"),value:c,multiple:!1,options:_,onChange:e=>r({order_by:e})}),(0,s.createElement)(o.SelectControl,{label:(0,n.__)("Sort order:","memberpress"),value:p,multiple:!1,options:f,onChange:e=>r({order:e})}),(0,s.createElement)(o.TextareaControl,{label:(0,n.__)("Not Logged In Message:","memberpress"),value:u,onChange:e=>r({not_logged_in_message:e})}),(0,s.createElement)(o.TextareaControl,{label:(0,n.__)("No Subscriptions Message:","memberpress"),value:d,onChange:e=>r({no_subscriptions_message:e})}),(0,s.createElement)(o.TextareaControl,{label:(0,n.__)("Top Description (optional):","memberpress"),value:b,onChange:e=>r({top_description:e})}),(0,s.createElement)(o.TextareaControl,{label:(0,n.__)("Bottom Description (optional):","memberpress"),value:g,onChange:e=>r({bottom_description:e})}),(0,s.createElement)(o.ToggleControl,{label:(0,n.__)("Use Membership Access URLs","memberpress"),help:(0,n.__)("Makes the Subscription name clickable, pointing to the Membership Access URL you have set in the Membership settings (Advanced tab)","memberpress"),checked:h,onChange:()=>r({use_access_url:!h})}))),(0,s.createElement)(o.Disabled,null,(0,s.createElement)(m(),{block:"memberpress/subscriptions",attributes:{not_logged_in_message:u,no_subscriptions_message:d,top_description:b,bottom_description:g,use_access_url:h}})))},save:function(){return null}})},140:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".mp-icon-placeholder{position:absolute;top:15px;left:15px}.mp-placeholder{padding:60px 20px 20px !important;display:block !important;min-height:auto !important}",""]);const a=o},772:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".wp-block-memberpress-login-form{margin-bottom:20px !important}",""]);const a=o},886:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".mepr-signup-form{margin:0 0 20px !important}.mepr-signup-form input[type=email],.mepr-signup-form input[type=number],.mepr-signup-form input[type=password],.mepr-signup-form input[type=tel],.mepr-signup-form input[type=text],.mepr-signup-form input[type=url],.mepr-signup-form select,.mepr-signup-form textarea{background:#f9fafb}.mepr-signup-form input[type=submit]{margin-bottom:20px;padding:15px 30px;background-color:#046bd2;color:#fff;font-size:1rem;font-weight:500}",""]);const a=o},697:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".mepr-editor-media-preview-img{object-fit:contain;width:200px;height:200px !important}",""]);const a=o},801:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".mepr-visuallyhidden,.mepr_mepr_vat_customer_type,.mepr_vat_number_row{display:none}",""]);const a=o},289:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".wp-block-memberpress-pro-login-form{max-width:none !important}.mepr-editor-login-preview-img{object-fit:contain;width:200px;height:200px !important}",""]);const a=o},225:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".wp-block-memberpress-protected-content{border:2px dashed #0f6dae;padding:10px}.wp-block-memberpress-protected-content .block-editor-block-list__layout .block-editor-block-list__block.is-selected>.block-editor-block-list__block-edit:before{margin:0 15px}.wp-block-memberpress-protected-content .editor-block-toolbar.block-editor-block-toolbar{left:16px}",""]);const a=o},574:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(81),n=r.n(s),l=r(645),o=r.n(l)()(n());o.push([e.id,".wp-block-memberpress-subscriptions{margin-bottom:20px !important}",""]);const a=o},645:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",s=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),s&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),s&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,s,n,l){"string"==typeof e&&(e=[[null,e,void 0]]);var o={};if(s)for(var a=0;a<this.length;a++){var i=this[a][0];null!=i&&(o[i]=!0)}for(var m=0;m<e.length;m++){var c=[].concat(e[m]);s&&o[c[0]]||(void 0!==l&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=l),r&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=r):c[2]=r),n&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=n):c[4]="".concat(n)),t.push(c))}},t}},81:e=>{e.exports=function(e){return e[1]}},379:e=>{var t=[];function r(e){for(var r=-1,s=0;s<t.length;s++)if(t[s].identifier===e){r=s;break}return r}function s(e,s){for(var l={},o=[],a=0;a<e.length;a++){var i=e[a],m=s.base?i[0]+s.base:i[0],c=l[m]||0,p="".concat(m," ").concat(c);l[m]=c+1;var u=r(p),d={css:i[1],media:i[2],sourceMap:i[3],supports:i[4],layer:i[5]};if(-1!==u)t[u].references++,t[u].updater(d);else{var b=n(d,s);s.byIndex=a,t.splice(a,0,{identifier:p,updater:b,references:1})}o.push(p)}return o}function n(e,t){var r=t.domAPI(t);return r.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,n){var l=s(e=e||[],n=n||{});return function(e){e=e||[];for(var o=0;o<l.length;o++){var a=r(l[o]);t[a].references--}for(var i=s(e,n),m=0;m<l.length;m++){var c=r(l[m]);0===t[c].references&&(t[c].updater(),t.splice(c,1))}l=i}}},569:e=>{var t={};e.exports=function(e,r){var s=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(r)}},216:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},565:(e,t,r)=>{e.exports=function(e){var t=r.nc;t&&e.setAttribute("nonce",t)}},795:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(r){!function(e,t,r){var s="";r.supports&&(s+="@supports (".concat(r.supports,") {")),r.media&&(s+="@media ".concat(r.media," {"));var n=void 0!==r.layer;n&&(s+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),s+=r.css,n&&(s+="}"),r.media&&(s+="}"),r.supports&&(s+="}");var l=r.sourceMap;l&&"undefined"!=typeof btoa&&(s+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(l))))," */")),t.styleTagTransform(s,e,t.options)}(t,e,r)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},589:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},175:e=>{e.exports=window.wp.blockEditor},981:e=>{e.exports=window.wp.blocks},609:e=>{e.exports=window.wp.components},818:e=>{e.exports=window.wp.data},307:e=>{e.exports=window.wp.element},736:e=>{e.exports=window.wp.i18n},423:e=>{e.exports=window.wp.serverSideRender}},t={};function r(s){var n=t[s];if(void 0!==n)return n.exports;var l=t[s]={id:s,exports:{}};return e[s](l,l.exports,r),l.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nc=void 0,r(984),r(541),r(585),r(859),r(727),r(149),r(458),r(703),r(691),r(94),r(613),r(449),r(138)})();